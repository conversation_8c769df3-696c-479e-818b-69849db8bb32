# **EMULACIÓN DE CONSOLA - Ejemplos de Promesas**

## **🖥️ EJECUCIÓN PASO A PASO CON SALIDAS REALES**

### **Ejemplo 1: Carga Exitosa de Usuario**

```javascript
// Crear instancia y cargar usuario válido
const example = new BasicPromiseExample();
await example.loadUser(1);
```

**💻 Salida de Consola:**
```bash
🔄 [2024-01-15T10:30:45.123Z] Iniciando carga de usuario 1...
🔄 [2024-01-15T10:30:45.124Z] Iniciando fetch para usuario 1
✅ [2024-01-15T10:30:46.456Z] Usuario 1 obtenido en 1332ms
✅ [2024-01-15T10:30:46.457Z] Usuario cargado exitosamente: Usuario 1 (1334ms)
🏁 [2024-01-15T10:30:46.458Z] Operación finalizada para usuario 1

# Estado del objeto después de la operación
console.log(example.data);
{
  id: 1,
  name: "Usuario 1",
  email: "<EMAIL>",
  createdAt: "2024-01-15T10:30:46.456Z",
  lastLogin: "2024-01-14T15:22:33.891Z",
  status: "active",
  networkDelay: 1332
}

console.log(example.isLoading);
false

console.log(example.loadHistory);
[
  {
    userId: 1,
    startTime: 1705315845123,
    status: "success",
    loadTime: 1334,
    endTime: 1705315846457
  }
]

console.log(example.errorCount);
0
```

### **Ejemplo 2: Error de Validación**

```javascript
// Intentar cargar usuario con ID inválido
try {
    await example.loadUser(-1);
} catch (error) {
    console.log('Error capturado:', error.message);
}
```

**💻 Salida de Consola:**
```bash
🔄 [2024-01-15T10:31:15.789Z] Iniciando carga de usuario -1...
🔄 [2024-01-15T10:31:15.790Z] Iniciando fetch para usuario -1
❌ [2024-01-15T10:31:16.123Z] Error: ID de usuario inválido: -1. Debe ser mayor a 0
❌ [2024-01-15T10:31:16.124Z] Error cargando usuario -1: ID de usuario inválido: -1. Debe ser mayor a 0 (335ms)
🏁 [2024-01-15T10:31:16.125Z] Operación finalizada para usuario -1

# Error capturado en el catch
Error capturado: ID de usuario inválido: -1. Debe ser mayor a 0

# Estado después del error
console.log(example.data);
{
  id: 1,
  name: "Usuario 1",
  email: "<EMAIL>",
  // ... (datos del usuario anterior se mantienen)
}

console.log(example.isLoading);
false

console.log(example.errorCount);
1

console.log(example.loadHistory);
[
  {
    userId: 1,
    startTime: 1705315845123,
    status: "success",
    loadTime: 1334,
    endTime: 1705315846457
  },
  {
    userId: -1,
    startTime: 1705315875789,
    status: "error",
    error: "ID de usuario inválido: -1. Debe ser mayor a 0",
    errorCode: "INVALID_USER_ID",
    loadTime: 335,
    endTime: 1705315876124
  }
]
```

### **Ejemplo 3: Carga Múltiple en Paralelo**

```javascript
// Cargar múltiples usuarios simultáneamente
const userIds = [1, 2, 3, 4, 5];
const results = await example.loadMultipleUsers(userIds);
```

**💻 Salida de Consola:**
```bash
🔄 Cargando 5 usuarios en paralelo...
🔄 [2024-01-15T10:32:00.100Z] Iniciando fetch para usuario 1
🔄 [2024-01-15T10:32:00.101Z] Iniciando fetch para usuario 2
🔄 [2024-01-15T10:32:00.102Z] Iniciando fetch para usuario 3
🔄 [2024-01-15T10:32:00.103Z] Iniciando fetch para usuario 4
🔄 [2024-01-15T10:32:00.104Z] Iniciando fetch para usuario 5
✅ [2024-01-15T10:32:01.234Z] Usuario 3 obtenido en 1131ms
✅ [2024-01-15T10:32:01.456Z] Usuario 1 obtenido en 1355ms
✅ [2024-01-15T10:32:01.789Z] Usuario 5 obtenido en 1686ms
✅ [2024-01-15T10:32:02.012Z] Usuario 2 obtenido en 1910ms
✅ [2024-01-15T10:32:02.345Z] Usuario 4 obtenido en 2242ms
✅ 5 usuarios cargados exitosamente

# Resultado de la operación paralela
console.log(results);
[
  {
    id: 1,
    name: "Usuario 1",
    email: "<EMAIL>",
    createdAt: "2024-01-15T10:32:01.456Z",
    lastLogin: "2024-01-14T08:15:22.334Z",
    status: "active",
    networkDelay: 1355
  },
  {
    id: 2,
    name: "Usuario 2",
    email: "<EMAIL>",
    createdAt: "2024-01-15T10:32:02.012Z",
    lastLogin: "2024-01-14T12:45:11.567Z",
    status: "active",
    networkDelay: 1910
  },
  // ... usuarios 3, 4, 5 con estructura similar
]

# Tiempo total: ~2.3 segundos (vs ~11 segundos si fuera secuencial)
```

### **Ejemplo 4: Manejo de Errores Mixtos**

```javascript
// Cargar usuarios con algunos IDs válidos y otros inválidos
const mixedIds = [1, -1, 2, 0, 3];
const results = await example.loadUsersWithErrorHandling(mixedIds);
```

**💻 Salida de Consola:**
```bash
🔄 Cargando usuarios con manejo de errores...
🔄 [2024-01-15T10:33:00.200Z] Iniciando fetch para usuario 1
🔄 [2024-01-15T10:33:00.201Z] Iniciando fetch para usuario -1
🔄 [2024-01-15T10:33:00.202Z] Iniciando fetch para usuario 2
🔄 [2024-01-15T10:33:00.203Z] Iniciando fetch para usuario 0
🔄 [2024-01-15T10:33:00.204Z] Iniciando fetch para usuario 3
✅ [2024-01-15T10:33:01.123Z] Usuario 1 obtenido en 922ms
❌ [2024-01-15T10:33:01.234Z] Error: ID de usuario inválido: -1. Debe ser mayor a 0
✅ [2024-01-15T10:33:01.456Z] Usuario 2 obtenido en 1253ms
❌ [2024-01-15T10:33:01.567Z] Error: ID de usuario inválido: 0. Debe ser mayor a 0
✅ [2024-01-15T10:33:01.789Z] Usuario 3 obtenido en 1586ms
✅ Exitosos: 3, ❌ Fallidos: 2

# Resultado estructurado
console.log(results);
{
  successful: [
    {
      id: 1,
      name: "Usuario 1",
      email: "<EMAIL>",
      // ... resto de datos
    },
    {
      id: 2,
      name: "Usuario 2",
      email: "<EMAIL>",
      // ... resto de datos
    },
    {
      id: 3,
      name: "Usuario 3",
      email: "<EMAIL>",
      // ... resto de datos
    }
  ],
  failed: [
    {
      id: -1,
      error: "ID de usuario inválido: -1. Debe ser mayor a 0"
    },
    {
      id: 0,
      error: "ID de usuario inválido: 0. Debe ser mayor a 0"
    }
  ],
  summary: {
    total: 5,
    success: 3,
    failed: 2
  }
}
```

### **Ejemplo 5: Promise Manager Avanzado**

```javascript
// Usar el sistema avanzado de gestión de promesas
const manager = new AdvancedPromiseManager({
    maxConcurrency: 3,
    defaultTimeout: 5000,
    retryAttempts: 2
});

// Configurar listeners para observabilidad
manager.on('promiseCreated', (data) => {
    console.log(`✨ Nueva promesa: ${data.id}`);
});

manager.on('promiseResolved', (data) => {
    console.log(`✅ Promesa resuelta: ${data.id} (${data.executionTime}ms)`);
});

// Crear promesa con retry automático
const result = await manager.createPromise((resolve, reject) => {
    const shouldFail = Math.random() > 0.7;
    setTimeout(() => {
        if (shouldFail) {
            reject(new Error('Fallo simulado'));
        } else {
            resolve('¡Operación exitosa!');
        }
    }, 1000);
}, {
    retryAttempts: 3,
    retryDelay: 500,
    tags: ['demo', 'retry-example']
});
```

**💻 Salida de Consola (Caso con Retry):**
```bash
✨ Nueva promesa: promise_1705315980123_abc123def
🔄 Retry 1 para promesa: promise_1705315980123_abc123def
🔄 Retry 2 para promesa: promise_1705315980123_abc123def
✅ Promesa resuelta: promise_1705315980123_abc123def (3547ms)

# Resultado final
console.log(result);
"¡Operación exitosa!"

# Métricas del manager
console.log(manager.getMetrics());
{
  totalPromises: 1,
  successfulPromises: 1,
  failedPromises: 0,
  averageExecutionTime: 3547,
  concurrentPeak: 1,
  activePromises: 0,
  completedPromises: 1,
  failedPromises: 0,
  successRate: "100.00%"
}
```

## **📊 ANÁLISIS DE PERFORMANCE**

### **Comparación: Secuencial vs Paralelo**

```bash
# Carga Secuencial (5 usuarios)
Tiempo total: ~8.5 segundos
Memoria pico: Baja (una promesa a la vez)
CPU: Subutilizada
Red: Subutilizada

# Carga Paralela (5 usuarios)
Tiempo total: ~2.3 segundos (73% más rápido)
Memoria pico: Media (5 promesas simultáneas)
CPU: Mejor utilizada
Red: Mejor utilizada

# Conclusión: Paralelización mejora significativamente el throughput
```

### **Métricas de Error Handling**

```bash
# Sin manejo de errores
1 fallo → Toda la operación falla
Información de debugging: Limitada
Recovery: Manual

# Con manejo robusto
1 fallo → Solo esa operación falla
Información de debugging: Completa
Recovery: Automático con retry
Observabilidad: Métricas detalladas
```
