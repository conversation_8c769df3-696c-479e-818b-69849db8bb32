# **EJEMPLOS DE CÓDIGO JAVASCRIPT - IMPLEMENTACIONES PRÁCTICAS**

## **📚 FUNDAMENTOS - VARIABLES Y TIPOS**

### **Ejemplo 1: Declaración y Uso de Variables**
```javascript
// ✅ Buenas prácticas con variables
const API_URL = 'https://api.ejemplo.com'; // Constante global
let contador = 0; // Variable que cambiará
let usuario; // Variable sin inicializar

// Función que demuestra scope
function ejemploScope() {
    let variableLocal = 'Solo visible aquí';
    
    if (true) {
        let variableBloque = 'Solo en este bloque';
        const CONSTANTE_BLOQUE = 'Inmutable en bloque';
        
        console.log(variableLocal); // ✅ Accesible
        console.log(variableBloque); // ✅ Accesible
    }
    
    // console.log(variableBloque); // ❌ Error: fuera de scope
}

// Demostración de tipos primitivos
const ejemplosTipos = {
    texto: 'Hola mundo',
    numero: 42,
    decimal: 3.14159,
    booleano: true,
    indefinido: undefined,
    nulo: null,
    simbolo: Symbol('id'),
    bigInt: 123456789012345678901234567890n
};

console.log('Tipos de datos:', Object.entries(ejemplosTipos)
    .map(([key, value]) => `${key}: ${typeof value}`)
);
```

### **Ejemplo 2: Destructuring Avanzado**
```javascript
// Destructuring de objetos
const usuario = {
    nombre: 'Ana García',
    edad: 28,
    email: '<EMAIL>',
    direccion: {
        calle: 'Av. Principal 123',
        ciudad: 'Madrid',
        pais: 'España'
    },
    hobbies: ['lectura', 'natación', 'fotografía']
};

// Destructuring básico
const { nombre, edad } = usuario;

// Destructuring con renombrado
const { email: correoElectronico } = usuario;

// Destructuring anidado
const { direccion: { ciudad, pais } } = usuario;

// Destructuring con valores por defecto
const { telefono = 'No disponible' } = usuario;

// Destructuring de arrays
const [hobby1, hobby2, ...otrosHobbies] = usuario.hobbies;

console.log(`${nombre} (${edad} años) vive en ${ciudad}, ${pais}`);
console.log(`Primer hobby: ${hobby1}, otros: ${otrosHobbies.join(', ')}`);

// Destructuring en parámetros de función
function mostrarUsuario({ nombre, edad, direccion: { ciudad } }) {
    return `${nombre}, ${edad} años, de ${ciudad}`;
}

console.log(mostrarUsuario(usuario));
```

## **⚡ FUNCIONES AVANZADAS**

### **Ejemplo 3: Funciones de Alto Orden**
```javascript
// Función que retorna otra función (closure)
function crearContador(inicial = 0) {
    let contador = inicial;
    
    return {
        incrementar: (valor = 1) => contador += valor,
        decrementar: (valor = 1) => contador -= valor,
        obtener: () => contador,
        reset: () => contador = inicial
    };
}

const contador1 = crearContador(10);
const contador2 = crearContador();

console.log(contador1.incrementar(5)); // 15
console.log(contador2.incrementar()); // 1
console.log(contador1.obtener()); // 15

// Función que toma otra función como parámetro
function procesarArray(array, transformacion) {
    return array.map(transformacion);
}

const numeros = [1, 2, 3, 4, 5];
const cuadrados = procesarArray(numeros, x => x ** 2);
const dobles = procesarArray(numeros, x => x * 2);

console.log('Cuadrados:', cuadrados); // [1, 4, 9, 16, 25]
console.log('Dobles:', dobles); // [2, 4, 6, 8, 10]

// Composición de funciones
const componer = (...funciones) => valor => 
    funciones.reduceRight((acc, fn) => fn(acc), valor);

const sumar5 = x => x + 5;
const multiplicarPor2 = x => x * 2;
const elevarAlCuadrado = x => x ** 2;

const operacionCompleja = componer(
    elevarAlCuadrado,
    multiplicarPor2,
    sumar5
);

console.log(operacionCompleja(3)); // ((3 + 5) * 2)² = 256
```

### **Ejemplo 4: Currying y Partial Application**
```javascript
// Currying: transformar función de múltiples argumentos en cadena de funciones
function curry(fn) {
    return function curried(...args) {
        if (args.length >= fn.length) {
            return fn.apply(this, args);
        } else {
            return function(...args2) {
                return curried.apply(this, args.concat(args2));
            };
        }
    };
}

// Función original
function sumar(a, b, c) {
    return a + b + c;
}

// Versión currificada
const sumarCurry = curry(sumar);

// Diferentes formas de usar
console.log(sumarCurry(1)(2)(3)); // 6
console.log(sumarCurry(1, 2)(3)); // 6
console.log(sumarCurry(1)(2, 3)); // 6

// Partial application práctica
const sumar10 = sumarCurry(10);
const sumar10y5 = sumar10(5);

console.log(sumar10y5(3)); // 18

// Ejemplo práctico: validador currificado
const validar = curry((regla, mensaje, valor) => {
    if (!regla(valor)) {
        throw new Error(mensaje);
    }
    return valor;
});

const validarEmail = validar(
    email => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),
    'Email inválido'
);

const validarLongitud = longitud => validar(
    str => str.length >= longitud,
    `Debe tener al menos ${longitud} caracteres`
);

try {
    const emailValido = validarEmail('<EMAIL>');
    const passwordValido = validarLongitud(8)('mipassword123');
    console.log('Validación exitosa');
} catch (error) {
    console.error(error.message);
}
```

## **🏗️ PROGRAMACIÓN ORIENTADA A OBJETOS**

### **Ejemplo 5: Clases ES6 Completas**
```javascript
// Clase base con todas las características modernas
class Vehiculo {
    // Campos privados
    #velocidad = 0;
    #encendido = false;
    
    // Campos estáticos
    static #contadorVehiculos = 0;
    static tiposVehiculo = ['auto', 'moto', 'camion'];
    
    constructor(marca, modelo, año) {
        this.marca = marca;
        this.modelo = modelo;
        this.año = año;
        this.id = ++Vehiculo.#contadorVehiculos;
        
        // Validación en constructor
        if (!marca || !modelo) {
            throw new Error('Marca y modelo son requeridos');
        }
    }
    
    // Getter y setter para velocidad
    get velocidad() {
        return this.#velocidad;
    }
    
    set velocidad(nueva) {
        if (nueva < 0) {
            throw new Error('La velocidad no puede ser negativa');
        }
        this.#velocidad = Math.min(nueva, this.velocidadMaxima);
    }
    
    // Getter para estado
    get encendido() {
        return this.#encendido;
    }
    
    // Método para encender
    encender() {
        if (!this.#encendido) {
            this.#encendido = true;
            console.log(`${this.descripcion} encendido`);
        }
    }
    
    // Método para apagar
    apagar() {
        if (this.#encendido && this.#velocidad === 0) {
            this.#encendido = false;
            console.log(`${this.descripcion} apagado`);
        } else if (this.#velocidad > 0) {
            console.log('No se puede apagar mientras está en movimiento');
        }
    }
    
    // Método abstracto (debe ser implementado por subclases)
    get velocidadMaxima() {
        throw new Error('velocidadMaxima debe ser implementado por la subclase');
    }
    
    // Getter computado
    get descripcion() {
        return `${this.marca} ${this.modelo} (${this.año})`;
    }
    
    // Método estático
    static obtenerTotal() {
        return this.#contadorVehiculos;
    }
    
    // Método para acelerar
    acelerar(incremento = 10) {
        if (!this.#encendido) {
            console.log('Debe encender el vehículo primero');
            return;
        }
        
        this.velocidad += incremento;
        console.log(`${this.descripcion} acelerando a ${this.velocidad} km/h`);
    }
    
    // Método para frenar
    frenar(decremento = 10) {
        this.velocidad = Math.max(0, this.velocidad - decremento);
        console.log(`${this.descripcion} frenando a ${this.velocidad} km/h`);
    }
    
    // Método toString
    toString() {
        return `Vehículo #${this.id}: ${this.descripcion}`;
    }
}

// Clase derivada
class Auto extends Vehiculo {
    #puertas;
    
    constructor(marca, modelo, año, puertas = 4) {
        super(marca, modelo, año);
        this.#puertas = puertas;
    }
    
    get velocidadMaxima() {
        return 200; // km/h
    }
    
    get puertas() {
        return this.#puertas;
    }
    
    // Sobrescribir método
    acelerar(incremento = 15) {
        console.log('Auto acelerando suavemente...');
        super.acelerar(incremento);
    }
    
    // Método específico de Auto
    abrirPuertas() {
        if (this.velocidad === 0) {
            console.log(`Abriendo ${this.#puertas} puertas del auto`);
        } else {
            console.log('No se pueden abrir las puertas en movimiento');
        }
    }
}

// Uso de las clases
const miAuto = new Auto('Toyota', 'Corolla', 2023, 4);
console.log(miAuto.toString());

miAuto.encender();
miAuto.acelerar(30);
miAuto.frenar(10);
miAuto.abrirPuertas();
miAuto.frenar(20);
miAuto.abrirPuertas();
miAuto.apagar();

console.log(`Total de vehículos creados: ${Vehiculo.obtenerTotal()}`);
```

## **🔄 PROGRAMACIÓN ASÍNCRONA**

### **Ejemplo 6: Async/Await Avanzado**
```javascript
// Simulador de API
class APISimulador {
    static async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    static async obtenerUsuario(id) {
        await this.delay(1000); // Simular latencia
        
        if (id === 999) {
            throw new Error('Usuario no encontrado');
        }
        
        return {
            id,
            nombre: `Usuario ${id}`,
            email: `usuario${id}@ejemplo.com`,
            activo: Math.random() > 0.2
        };
    }
    
    static async obtenerPosts(usuarioId) {
        await this.delay(800);
        
        return Array.from({ length: 3 }, (_, i) => ({
            id: i + 1,
            usuarioId,
            titulo: `Post ${i + 1} del usuario ${usuarioId}`,
            contenido: `Contenido del post ${i + 1}...`
        }));
    }
    
    static async obtenerComentarios(postId) {
        await this.delay(500);
        
        return Array.from({ length: 2 }, (_, i) => ({
            id: i + 1,
            postId,
            autor: `Comentarista ${i + 1}`,
            texto: `Comentario ${i + 1} en el post ${postId}`
        }));
    }
}

// Función para manejar múltiples operaciones asíncronas
async function obtenerDatosCompletos(usuarioId) {
    try {
        console.log(`Obteniendo datos para usuario ${usuarioId}...`);
        
        // Obtener usuario
        const usuario = await APISimulador.obtenerUsuario(usuarioId);
        console.log('Usuario obtenido:', usuario.nombre);
        
        if (!usuario.activo) {
            throw new Error('Usuario inactivo');
        }
        
        // Obtener posts del usuario
        const posts = await APISimulador.obtenerPosts(usuarioId);
        console.log(`${posts.length} posts obtenidos`);
        
        // Obtener comentarios para todos los posts en paralelo
        const comentariosPromises = posts.map(post => 
            APISimulador.obtenerComentarios(post.id)
        );
        
        const comentariosArrays = await Promise.all(comentariosPromises);
        const comentarios = comentariosArrays.flat();
        
        console.log(`${comentarios.length} comentarios obtenidos`);
        
        return {
            usuario,
            posts,
            comentarios
        };
        
    } catch (error) {
        console.error('Error obteniendo datos:', error.message);
        throw error;
    }
}

// Función con timeout
async function obtenerDatosConTimeout(usuarioId, timeoutMs = 5000) {
    const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Timeout')), timeoutMs)
    );
    
    return Promise.race([
        obtenerDatosCompletos(usuarioId),
        timeoutPromise
    ]);
}

// Función para procesar múltiples usuarios
async function procesarUsuarios(userIds) {
    const resultados = [];
    
    for (const id of userIds) {
        try {
            const datos = await obtenerDatosConTimeout(id, 3000);
            resultados.push({ id, datos, error: null });
        } catch (error) {
            resultados.push({ id, datos: null, error: error.message });
        }
    }
    
    return resultados;
}

// Uso de las funciones asíncronas
async function ejemploAsincronoCompleto() {
    console.log('=== Ejemplo de programación asíncrona ===');
    
    try {
        // Procesar un usuario individual
        const datosUsuario = await obtenerDatosCompletos(1);
        console.log('Datos completos obtenidos:', {
            usuario: datosUsuario.usuario.nombre,
            posts: datosUsuario.posts.length,
            comentarios: datosUsuario.comentarios.length
        });
        
        // Procesar múltiples usuarios
        console.log('\nProcesando múltiples usuarios...');
        const resultados = await procesarUsuarios([2, 3, 999, 4]);
        
        resultados.forEach(resultado => {
            if (resultado.error) {
                console.log(`Usuario ${resultado.id}: Error - ${resultado.error}`);
            } else {
                console.log(`Usuario ${resultado.id}: Éxito - ${resultado.datos.posts.length} posts`);
            }
        });
        
    } catch (error) {
        console.error('Error en ejemplo:', error.message);
    }
}

// Ejecutar ejemplo
ejemploAsincronoCompleto();
```

Estos ejemplos demuestran implementaciones prácticas y avanzadas de los conceptos fundamentales de JavaScript, mostrando las mejores prácticas y patrones modernos de desarrollo.
