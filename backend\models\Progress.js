/**
 * MODELO DE PROGRESO
 * ==================
 * 
 * Modelo para tracking detallado del progreso de usuarios en cursos
 * Incluye analytics, tiempo de estudio y métricas de rendimiento
 */

const mongoose = require('mongoose');

const progressSchema = new mongoose.Schema({
    // Referencias principales
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    
    courseId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course',
        required: true
    },
    
    // Progreso general del curso
    overallProgress: {
        percentage: {
            type: Number,
            default: 0,
            min: 0,
            max: 100
        },
        status: {
            type: String,
            enum: ['not_started', 'in_progress', 'completed', 'paused', 'dropped'],
            default: 'not_started'
        },
        startedAt: Date,
        completedAt: Date,
        lastAccessedAt: {
            type: Date,
            default: Date.now
        }
    },
    
    // Progreso por partes
    partsProgress: [{
        partNumber: {
            type: Number,
            required: true
        },
        title: String,
        percentage: {
            type: Number,
            default: 0,
            min: 0,
            max: 100
        },
        status: {
            type: String,
            enum: ['not_started', 'in_progress', 'completed'],
            default: 'not_started'
        },
        startedAt: Date,
        completedAt: Date,
        
        // Progreso por capítulos
        chaptersProgress: [{
            chapterNumber: {
                type: Number,
                required: true
            },
            title: String,
            percentage: {
                type: Number,
                default: 0,
                min: 0,
                max: 100
            },
            status: {
                type: String,
                enum: ['not_started', 'in_progress', 'completed'],
                default: 'not_started'
            },
            startedAt: Date,
            completedAt: Date,
            
            // Progreso por lecciones
            lessonsProgress: [{
                lessonNumber: {
                    type: Number,
                    required: true
                },
                title: String,
                status: {
                    type: String,
                    enum: ['not_started', 'in_progress', 'completed', 'skipped'],
                    default: 'not_started'
                },
                startedAt: Date,
                completedAt: Date,
                timeSpentMinutes: {
                    type: Number,
                    default: 0
                },
                attempts: {
                    type: Number,
                    default: 0
                },
                score: {
                    type: Number,
                    min: 0,
                    max: 100
                },
                
                // Progreso de ejercicios
                exercisesProgress: [{
                    exerciseId: String,
                    title: String,
                    status: {
                        type: String,
                        enum: ['not_started', 'in_progress', 'completed', 'failed'],
                        default: 'not_started'
                    },
                    attempts: {
                        type: Number,
                        default: 0
                    },
                    bestScore: {
                        type: Number,
                        default: 0,
                        min: 0,
                        max: 100
                    },
                    timeSpentMinutes: {
                        type: Number,
                        default: 0
                    },
                    hintsUsed: {
                        type: Number,
                        default: 0
                    },
                    completedAt: Date,
                    submissions: [{
                        code: String,
                        result: {
                            passed: Boolean,
                            score: Number,
                            errors: [String],
                            testResults: [{
                                testName: String,
                                passed: Boolean,
                                expected: String,
                                actual: String,
                                error: String
                            }]
                        },
                        submittedAt: {
                            type: Date,
                            default: Date.now
                        }
                    }]
                }]
            }]
        }]
    }],
    
    // Proyectos del curso
    projectsProgress: [{
        projectId: String,
        title: String,
        status: {
            type: String,
            enum: ['not_started', 'in_progress', 'submitted', 'reviewed', 'completed'],
            default: 'not_started'
        },
        startedAt: Date,
        submittedAt: Date,
        completedAt: Date,
        timeSpentMinutes: {
            type: Number,
            default: 0
        },
        
        // Entregables del proyecto
        deliverables: [{
            type: {
                type: String,
                enum: ['code', 'documentation', 'demo', 'presentation', 'report']
            },
            url: String,
            description: String,
            submittedAt: Date
        }],
        
        // Evaluación del proyecto
        evaluation: {
            score: {
                type: Number,
                min: 0,
                max: 100
            },
            feedback: String,
            criteria: [{
                name: String,
                score: Number,
                maxScore: Number,
                feedback: String
            }],
            evaluatedBy: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            },
            evaluatedAt: Date
        }
    }],
    
    // Métricas de tiempo
    timeTracking: {
        totalMinutes: {
            type: Number,
            default: 0
        },
        dailyMinutes: [{
            date: {
                type: Date,
                required: true
            },
            minutes: {
                type: Number,
                default: 0
            }
        }],
        weeklyMinutes: [{
            weekStart: Date,
            minutes: Number
        }],
        monthlyMinutes: [{
            month: Number,
            year: Number,
            minutes: Number
        }],
        averageSessionMinutes: {
            type: Number,
            default: 0
        },
        longestSessionMinutes: {
            type: Number,
            default: 0
        }
    },
    
    // Métricas de rendimiento
    performance: {
        averageScore: {
            type: Number,
            default: 0,
            min: 0,
            max: 100
        },
        totalExercisesCompleted: {
            type: Number,
            default: 0
        },
        totalExercisesAttempted: {
            type: Number,
            default: 0
        },
        successRate: {
            type: Number,
            default: 0,
            min: 0,
            max: 100
        },
        averageAttempts: {
            type: Number,
            default: 0
        },
        hintsUsageRate: {
            type: Number,
            default: 0
        },
        
        // Métricas por dificultad
        performanceByDifficulty: [{
            difficulty: {
                type: Number,
                min: 1,
                max: 5
            },
            exercisesCompleted: Number,
            averageScore: Number,
            averageAttempts: Number
        }],
        
        // Tendencias de mejora
        improvementTrend: {
            type: String,
            enum: ['improving', 'stable', 'declining', 'insufficient_data'],
            default: 'insufficient_data'
        },
        
        // Fortalezas y debilidades
        strengths: [String],
        weaknesses: [String]
    },
    
    // Racha de estudio
    studyStreak: {
        current: {
            type: Number,
            default: 0
        },
        longest: {
            type: Number,
            default: 0
        },
        lastStudyDate: Date,
        streakStartDate: Date
    },
    
    // Logros y badges ganados en este curso
    achievements: [{
        achievementId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Achievement'
        },
        earnedAt: {
            type: Date,
            default: Date.now
        },
        progress: {
            type: Number,
            default: 100
        }
    }],
    
    // Notas y marcadores del usuario
    userNotes: [{
        lessonId: String,
        content: String,
        isPublic: {
            type: Boolean,
            default: false
        },
        createdAt: {
            type: Date,
            default: Date.now
        },
        updatedAt: Date
    }],
    
    bookmarks: [{
        lessonId: String,
        title: String,
        note: String,
        createdAt: {
            type: Date,
            default: Date.now
        }
    }],
    
    // Configuración de estudio
    studySettings: {
        reminderEnabled: {
            type: Boolean,
            default: true
        },
        reminderTime: String, // formato HH:MM
        studyGoalMinutesPerDay: {
            type: Number,
            default: 60
        },
        preferredStudyDays: [{
            type: String,
            enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
        }]
    },
    
    // Certificación
    certification: {
        eligible: {
            type: Boolean,
            default: false
        },
        issued: {
            type: Boolean,
            default: false
        },
        certificateId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Certificate'
        },
        issuedAt: Date,
        finalScore: Number
    }

}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Índices para optimización
progressSchema.index({ userId: 1, courseId: 1 }, { unique: true });
progressSchema.index({ userId: 1 });
progressSchema.index({ courseId: 1 });
progressSchema.index({ 'overallProgress.status': 1 });
progressSchema.index({ 'overallProgress.lastAccessedAt': -1 });
progressSchema.index({ 'studyStreak.current': -1 });
progressSchema.index({ updatedAt: -1 });

// Virtuals
progressSchema.virtual('isCompleted').get(function() {
    return this.overallProgress.status === 'completed';
});

progressSchema.virtual('completionRate').get(function() {
    return this.overallProgress.percentage;
});

progressSchema.virtual('totalStudyHours').get(function() {
    return Math.round((this.timeTracking.totalMinutes / 60) * 100) / 100;
});

progressSchema.virtual('averageStudyTimePerDay').get(function() {
    if (this.timeTracking.dailyMinutes.length === 0) return 0;
    const totalMinutes = this.timeTracking.dailyMinutes.reduce((sum, day) => sum + day.minutes, 0);
    return Math.round((totalMinutes / this.timeTracking.dailyMinutes.length) * 100) / 100;
});

progressSchema.virtual('isOnTrack').get(function() {
    const goalMinutes = this.studySettings.studyGoalMinutesPerDay;
    const averageMinutes = this.averageStudyTimePerDay;
    return averageMinutes >= goalMinutes * 0.8; // 80% del objetivo
});

// Middleware pre-save
progressSchema.pre('save', function(next) {
    // Actualizar progreso general basado en partes
    if (this.partsProgress.length > 0) {
        const totalPercentage = this.partsProgress.reduce((sum, part) => sum + part.percentage, 0);
        this.overallProgress.percentage = Math.round(totalPercentage / this.partsProgress.length);
        
        // Actualizar estado general
        if (this.overallProgress.percentage === 100) {
            this.overallProgress.status = 'completed';
            if (!this.overallProgress.completedAt) {
                this.overallProgress.completedAt = new Date();
            }
        } else if (this.overallProgress.percentage > 0) {
            this.overallProgress.status = 'in_progress';
            if (!this.overallProgress.startedAt) {
                this.overallProgress.startedAt = new Date();
            }
        }
    }
    
    // Calcular métricas de rendimiento
    this.calculatePerformanceMetrics();
    
    // Actualizar racha de estudio
    this.updateStudyStreak();
    
    next();
});

// Métodos de instancia
progressSchema.methods.calculatePerformanceMetrics = function() {
    let totalExercises = 0;
    let completedExercises = 0;
    let totalScore = 0;
    let totalAttempts = 0;
    let totalHints = 0;
    
    this.partsProgress.forEach(part => {
        part.chaptersProgress.forEach(chapter => {
            chapter.lessonsProgress.forEach(lesson => {
                lesson.exercisesProgress.forEach(exercise => {
                    totalExercises++;
                    totalAttempts += exercise.attempts;
                    totalHints += exercise.hintsUsed;
                    
                    if (exercise.status === 'completed') {
                        completedExercises++;
                        totalScore += exercise.bestScore;
                    }
                });
            });
        });
    });
    
    this.performance.totalExercisesAttempted = totalExercises;
    this.performance.totalExercisesCompleted = completedExercises;
    
    if (completedExercises > 0) {
        this.performance.averageScore = Math.round(totalScore / completedExercises);
        this.performance.averageAttempts = Math.round((totalAttempts / completedExercises) * 100) / 100;
    }
    
    if (totalExercises > 0) {
        this.performance.successRate = Math.round((completedExercises / totalExercises) * 100);
        this.performance.hintsUsageRate = Math.round((totalHints / totalExercises) * 100) / 100;
    }
};

progressSchema.methods.updateStudyStreak = function() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const lastStudy = this.studyStreak.lastStudyDate;
    if (!lastStudy) return;
    
    const lastStudyDate = new Date(lastStudy);
    lastStudyDate.setHours(0, 0, 0, 0);
    
    const daysDiff = Math.floor((today - lastStudyDate) / (1000 * 60 * 60 * 24));
    
    if (daysDiff === 0) {
        // Mismo día, no cambiar racha
        return;
    } else if (daysDiff === 1) {
        // Día consecutivo, incrementar racha
        this.studyStreak.current += 1;
        if (this.studyStreak.current > this.studyStreak.longest) {
            this.studyStreak.longest = this.studyStreak.current;
        }
    } else {
        // Racha rota
        this.studyStreak.current = 1;
        this.studyStreak.streakStartDate = today;
    }
    
    this.studyStreak.lastStudyDate = today;
};

progressSchema.methods.addStudyTime = function(minutes) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Actualizar tiempo total
    this.timeTracking.totalMinutes += minutes;
    
    // Actualizar tiempo diario
    const todayEntry = this.timeTracking.dailyMinutes.find(
        entry => entry.date.getTime() === today.getTime()
    );
    
    if (todayEntry) {
        todayEntry.minutes += minutes;
    } else {
        this.timeTracking.dailyMinutes.push({
            date: today,
            minutes: minutes
        });
    }
    
    // Mantener solo los últimos 365 días
    if (this.timeTracking.dailyMinutes.length > 365) {
        this.timeTracking.dailyMinutes.sort((a, b) => b.date - a.date);
        this.timeTracking.dailyMinutes = this.timeTracking.dailyMinutes.slice(0, 365);
    }
    
    // Actualizar última actividad
    this.overallProgress.lastAccessedAt = new Date();
    this.studyStreak.lastStudyDate = new Date();
};

progressSchema.methods.completeLesson = function(partNumber, chapterNumber, lessonNumber, timeSpent = 0, score = null) {
    const part = this.partsProgress.find(p => p.partNumber === partNumber);
    if (!part) return false;
    
    const chapter = part.chaptersProgress.find(c => c.chapterNumber === chapterNumber);
    if (!chapter) return false;
    
    const lesson = chapter.lessonsProgress.find(l => l.lessonNumber === lessonNumber);
    if (!lesson) return false;
    
    lesson.status = 'completed';
    lesson.completedAt = new Date();
    lesson.timeSpentMinutes += timeSpent;
    if (score !== null) lesson.score = score;
    
    if (timeSpent > 0) {
        this.addStudyTime(timeSpent);
    }
    
    // Recalcular progreso del capítulo
    this.recalculateChapterProgress(part, chapter);
    
    return true;
};

progressSchema.methods.recalculateChapterProgress = function(part, chapter) {
    const totalLessons = chapter.lessonsProgress.length;
    const completedLessons = chapter.lessonsProgress.filter(l => l.status === 'completed').length;
    
    chapter.percentage = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0;
    
    if (chapter.percentage === 100 && !chapter.completedAt) {
        chapter.status = 'completed';
        chapter.completedAt = new Date();
    } else if (chapter.percentage > 0) {
        chapter.status = 'in_progress';
        if (!chapter.startedAt) {
            chapter.startedAt = new Date();
        }
    }
    
    // Recalcular progreso de la parte
    this.recalculatePartProgress(part);
};

progressSchema.methods.recalculatePartProgress = function(part) {
    const totalChapters = part.chaptersProgress.length;
    const totalPercentage = part.chaptersProgress.reduce((sum, chapter) => sum + chapter.percentage, 0);
    
    part.percentage = totalChapters > 0 ? Math.round(totalPercentage / totalChapters) : 0;
    
    if (part.percentage === 100 && !part.completedAt) {
        part.status = 'completed';
        part.completedAt = new Date();
    } else if (part.percentage > 0) {
        part.status = 'in_progress';
        if (!part.startedAt) {
            part.startedAt = new Date();
        }
    }
};

progressSchema.methods.addNote = function(lessonId, content, isPublic = false) {
    this.userNotes.push({
        lessonId,
        content,
        isPublic,
        createdAt: new Date()
    });
};

progressSchema.methods.addBookmark = function(lessonId, title, note = '') {
    const existingBookmark = this.bookmarks.find(b => b.lessonId === lessonId);
    if (!existingBookmark) {
        this.bookmarks.push({
            lessonId,
            title,
            note,
            createdAt: new Date()
        });
    }
};

progressSchema.methods.checkCertificationEligibility = function() {
    const completionThreshold = 80; // Configurable
    this.certification.eligible = this.overallProgress.percentage >= completionThreshold;
    return this.certification.eligible;
};

// Métodos estáticos
progressSchema.statics.findByUser = function(userId) {
    return this.find({ userId }).populate('courseId', 'title language category level');
};

progressSchema.statics.findByCourse = function(courseId) {
    return this.find({ courseId }).populate('userId', 'username profile.firstName profile.lastName');
};

progressSchema.statics.getActiveStudents = function(days = 7) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    return this.find({
        'overallProgress.lastAccessedAt': { $gte: cutoffDate }
    }).populate('userId courseId');
};

progressSchema.statics.getCompletionStats = function(courseId) {
    return this.aggregate([
        { $match: { courseId: mongoose.Types.ObjectId(courseId) } },
        {
            $group: {
                _id: '$overallProgress.status',
                count: { $sum: 1 },
                averageProgress: { $avg: '$overallProgress.percentage' }
            }
        }
    ]);
};

module.exports = mongoose.model('Progress', progressSchema);
