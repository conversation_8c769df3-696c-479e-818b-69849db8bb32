# **Capítulo 1 - Introducción y Configuración**

## **📖 Descripción del Capítulo**

Este capítulo establece las bases fundamentales para tu viaje en JavaScript. Aprenderás sobre la historia y evolución del lenguaje, configurarás un entorno de desarrollo profesional, y escribirás tus primeros programas. Es esencial completar este capítulo antes de avanzar, ya que proporciona las herramientas y conocimientos necesarios para todo el curso.

## **🎯 Objetivos de Aprendizaje**

Al completar este capítulo, serás capaz de:

- [ ] Comprender la historia y evolución de JavaScript
- [ ] Configurar un entorno de desarrollo profesional
- [ ] Instalar y usar herramientas esenciales de desarrollo
- [ ] Escribir y ejecutar tu primer programa en JavaScript
- [ ] Usar herramientas de debugging y desarrollo del navegador
- [ ] Aplicar mejores prácticas desde el inicio

## **📊 Información del Capítulo**

- **Dificultad:** ⭐⭐ (Principiante)
- **Tiempo estimado:** 8-12 horas
- **Temas:** 5
- **Subtemas:** 50
- **Ejercicios prácticos:** 15
- **Proyectos:** 3

## **📋 Índice de Temas**

### **[1.1. Historia y Evolución de JavaScript](1.1.%20Historia%20y%20Evolución%20de%20JavaScript/README.md)** ⭐⭐
**Tiempo:** 1-2 horas | **Subtemas:** 10

Descubre los orígenes de JavaScript, su evolución a través de los años, y su papel en el desarrollo web moderno.

**Contenido:**
- [1.1.1 - Orígenes de JavaScript](1.1.%20Historia%20y%20Evolución%20de%20JavaScript/contenido/1.1.1%20-%20Orígenes%20de%20JavaScript.md)
- [1.1.2 - ECMAScript y Estándares](1.1.%20Historia%20y%20Evolución%20de%20JavaScript/contenido/1.1.2%20-%20ECMAScript%20y%20Estándares.md)
- [1.1.3 - JavaScript en el Navegador](1.1.%20Historia%20y%20Evolución%20de%20JavaScript/contenido/1.1.3%20-%20JavaScript%20en%20el%20Navegador.md)
- [1.1.4 - Node.js y JavaScript del Lado del Servidor](1.1.%20Historia%20y%20Evolución%20de%20JavaScript/contenido/1.1.4%20-%20Node.js%20y%20JavaScript%20del%20Lado%20del%20Servidor.md)
- [1.1.5 - Frameworks y Librerías Populares](1.1.%20Historia%20y%20Evolución%20de%20JavaScript/contenido/1.1.5%20-%20Frameworks%20y%20Librerías%20Populares.md)
- [1.1.6 - JavaScript Moderno (ES6+)](1.1.%20Historia%20y%20Evolución%20de%20JavaScript/contenido/1.1.6%20-%20JavaScript%20Moderno%20(ES6+).md)
- [1.1.7 - Ecosistema de Desarrollo](1.1.%20Historia%20y%20Evolución%20de%20JavaScript/contenido/1.1.7%20-%20Ecosistema%20de%20Desarrollo.md)
- [1.1.8 - Tendencias Actuales](1.1.%20Historia%20y%20Evolución%20de%20JavaScript/contenido/1.1.8%20-%20Tendencias%20Actuales.md)
- [1.1.9 - Futuro de JavaScript](1.1.%20Historia%20y%20Evolución%20de%20JavaScript/contenido/1.1.9%20-%20Futuro%20de%20JavaScript.md)
- [1.1.10 - Casos de Uso Modernos](1.1.%20Historia%20y%20Evolución%20de%20JavaScript/contenido/1.1.10%20-%20Casos%20de%20Uso%20Modernos.md)

---

### **[1.2. Configuración del Entorno de Desarrollo](1.2.%20Configuración%20del%20Entorno%20de%20Desarrollo/README.md)** ⭐⭐⭐
**Tiempo:** 2-3 horas | **Subtemas:** 10

Aprende a configurar un entorno de desarrollo profesional con todas las herramientas necesarias.

**Contenido:**
- [1.2.1 - Instalación de Node.js](1.2.%20Configuración%20del%20Entorno%20de%20Desarrollo/contenido/1.2.1%20-%20Instalación%20de%20Node.js.md)
- [1.2.2 - Configuración de Visual Studio Code](1.2.%20Configuración%20del%20Entorno%20de%20Desarrollo/contenido/1.2.2%20-%20Configuración%20de%20Visual%20Studio%20Code.md)
- [1.2.3 - Extensiones Esenciales](1.2.%20Configuración%20del%20Entorno%20de%20Desarrollo/contenido/1.2.3%20-%20Extensiones%20Esenciales.md)
- [1.2.4 - Configuración de Git](1.2.%20Configuración%20del%20Entorno%20de%20Desarrollo/contenido/1.2.4%20-%20Configuración%20de%20Git.md)
- [1.2.5 - Terminal y Línea de Comandos](1.2.%20Configuración%20del%20Entorno%20de%20Desarrollo/contenido/1.2.5%20-%20Terminal%20y%20Línea%20de%20Comandos.md)
- [1.2.6 - Gestores de Paquetes (npm/yarn)](1.2.%20Configuración%20del%20Entorno%20de%20Desarrollo/contenido/1.2.6%20-%20Gestores%20de%20Paquetes.md)
- [1.2.7 - Configuración de Prettier y ESLint](1.2.%20Configuración%20del%20Entorno%20de%20Desarrollo/contenido/1.2.7%20-%20Configuración%20de%20Prettier%20y%20ESLint.md)
- [1.2.8 - Live Server y Desarrollo Local](1.2.%20Configuración%20del%20Entorno%20de%20Desarrollo/contenido/1.2.8%20-%20Live%20Server%20y%20Desarrollo%20Local.md)
- [1.2.9 - Estructura de Proyectos](1.2.%20Configuración%20del%20Entorno%20de%20Desarrollo/contenido/1.2.9%20-%20Estructura%20de%20Proyectos.md)
- [1.2.10 - Mejores Prácticas de Configuración](1.2.%20Configuración%20del%20Entorno%20de%20Desarrollo/contenido/1.2.10%20-%20Mejores%20Prácticas%20de%20Configuración.md)

---

### **[1.3. Herramientas Esenciales](1.3.%20Herramientas%20Esenciales/README.md)** ⭐⭐
**Tiempo:** 1-2 horas | **Subtemas:** 10

Domina las herramientas fundamentales que todo desarrollador JavaScript debe conocer.

**Contenido:**
- [1.3.1 - Navegadores y DevTools](1.3.%20Herramientas%20Esenciales/contenido/1.3.1%20-%20Navegadores%20y%20DevTools.md)
- [1.3.2 - Console del Navegador](1.3.%20Herramientas%20Esenciales/contenido/1.3.2%20-%20Console%20del%20Navegador.md)
- [1.3.3 - Inspector de Elementos](1.3.%20Herramientas%20Esenciales/contenido/1.3.3%20-%20Inspector%20de%20Elementos.md)
- [1.3.4 - Network Tab](1.3.%20Herramientas%20Esenciales/contenido/1.3.4%20-%20Network%20Tab.md)
- [1.3.5 - Sources y Debugging](1.3.%20Herramientas%20Esenciales/contenido/1.3.5%20-%20Sources%20y%20Debugging.md)
- [1.3.6 - Performance Tools](1.3.%20Herramientas%20Esenciales/contenido/1.3.6%20-%20Performance%20Tools.md)
- [1.3.7 - Application Tab](1.3.%20Herramientas%20Esenciales/contenido/1.3.7%20-%20Application%20Tab.md)
- [1.3.8 - Lighthouse y Auditorías](1.3.%20Herramientas%20Esenciales/contenido/1.3.8%20-%20Lighthouse%20y%20Auditorías.md)
- [1.3.9 - Herramientas Online](1.3.%20Herramientas%20Esenciales/contenido/1.3.9%20-%20Herramientas%20Online.md)
- [1.3.10 - Workflow de Desarrollo](1.3.%20Herramientas%20Esenciales/contenido/1.3.10%20-%20Workflow%20de%20Desarrollo.md)

---

### **[1.4. Primer Programa en JavaScript](1.4.%20Primer%20Programa%20en%20JavaScript/README.md)** ⭐⭐⭐
**Tiempo:** 2-3 horas | **Subtemas:** 10

Escribe y ejecuta tus primeros programas en JavaScript, tanto en el navegador como en Node.js.

**Contenido:**
- [1.4.1 - Hola Mundo en el Navegador](1.4.%20Primer%20Programa%20en%20JavaScript/contenido/1.4.1%20-%20Hola%20Mundo%20en%20el%20Navegador.md)
- [1.4.2 - Hola Mundo en Node.js](1.4.%20Primer%20Programa%20en%20JavaScript/contenido/1.4.2%20-%20Hola%20Mundo%20en%20Node.js.md)
- [1.4.3 - Estructura Básica HTML](1.4.%20Primer%20Programa%20en%20JavaScript/contenido/1.4.3%20-%20Estructura%20Básica%20HTML.md)
- [1.4.4 - Inclusión de JavaScript](1.4.%20Primer%20Programa%20en%20JavaScript/contenido/1.4.4%20-%20Inclusión%20de%20JavaScript.md)
- [1.4.5 - Console.log y Salida](1.4.%20Primer%20Programa%20en%20JavaScript/contenido/1.4.5%20-%20Console.log%20y%20Salida.md)
- [1.4.6 - Comentarios en JavaScript](1.4.%20Primer%20Programa%20en%20JavaScript/contenido/1.4.6%20-%20Comentarios%20en%20JavaScript.md)
- [1.4.7 - Sintaxis Básica](1.4.%20Primer%20Programa%20en%20JavaScript/contenido/1.4.7%20-%20Sintaxis%20Básica.md)
- [1.4.8 - Errores Comunes](1.4.%20Primer%20Programa%20en%20JavaScript/contenido/1.4.8%20-%20Errores%20Comunes.md)
- [1.4.9 - Buenas Prácticas Iniciales](1.4.%20Primer%20Programa%20en%20JavaScript/contenido/1.4.9%20-%20Buenas%20Prácticas%20Iniciales.md)
- [1.4.10 - Ejercicios Prácticos](1.4.%20Primer%20Programa%20en%20JavaScript/contenido/1.4.10%20-%20Ejercicios%20Prácticos.md)

---

### **[1.5. Debugging y Herramientas de Desarrollo](1.5.%20Debugging%20y%20Herramientas%20de%20Desarrollo/README.md)** ⭐⭐⭐⭐
**Tiempo:** 2-3 horas | **Subtemas:** 10

Aprende técnicas esenciales de debugging y resolución de problemas en JavaScript.

**Contenido:**
- [1.5.1 - Introducción al Debugging](1.5.%20Debugging%20y%20Herramientas%20de%20Desarrollo/contenido/1.5.1%20-%20Introducción%20al%20Debugging.md)
- [1.5.2 - Breakpoints y Step Debugging](1.5.%20Debugging%20y%20Herramientas%20de%20Desarrollo/contenido/1.5.2%20-%20Breakpoints%20y%20Step%20Debugging.md)
- [1.5.3 - Console Methods](1.5.%20Debugging%20y%20Herramientas%20de%20Desarrollo/contenido/1.5.3%20-%20Console%20Methods.md)
- [1.5.4 - Error Handling Básico](1.5.%20Debugging%20y%20Herramientas%20de%20Desarrollo/contenido/1.5.4%20-%20Error%20Handling%20Básico.md)
- [1.5.5 - Debugging en VS Code](1.5.%20Debugging%20y%20Herramientas%20de%20Desarrollo/contenido/1.5.5%20-%20Debugging%20en%20VS%20Code.md)
- [1.5.6 - Debugging en Chrome DevTools](1.5.%20Debugging%20y%20Herramientas%20de%20Desarrollo/contenido/1.5.6%20-%20Debugging%20en%20Chrome%20DevTools.md)
- [1.5.7 - Técnicas de Debugging](1.5.%20Debugging%20y%20Herramientas%20de%20Desarrollo/contenido/1.5.7%20-%20Técnicas%20de%20Debugging.md)
- [1.5.8 - Debugging de Performance](1.5.%20Debugging%20y%20Herramientas%20de%20Desarrollo/contenido/1.5.8%20-%20Debugging%20de%20Performance.md)
- [1.5.9 - Herramientas de Análisis](1.5.%20Debugging%20y%20Herramientas%20de%20Desarrollo/contenido/1.5.9%20-%20Herramientas%20de%20Análisis.md)
- [1.5.10 - Mejores Prácticas de Debugging](1.5.%20Debugging%20y%20Herramientas%20de%20Desarrollo/contenido/1.5.10%20-%20Mejores%20Prácticas%20de%20Debugging.md)

---

## **🎯 Rutas de Aprendizaje del Capítulo**

### **🚀 Ruta Rápida (4-6 horas)**
Enfoque en lo esencial para comenzar a programar.

**Temas recomendados:**
- 1.2 - Configuración del Entorno (subtemas 1.2.1, 1.2.2, 1.2.3)
- 1.4 - Primer Programa (subtemas 1.4.1, 1.4.5, 1.4.7)
- 1.5 - Debugging Básico (subtemas 1.5.1, 1.5.3)

### **📚 Ruta Completa (8-12 horas)**
Cobertura completa de todos los fundamentos.

**Incluye:**
- Todos los temas y subtemas
- Todos los ejercicios prácticos
- Proyectos del capítulo

### **🔬 Ruta Experto (12-16 horas)**
Para dominio completo y comprensión profunda.

**Incluye:**
- Ruta completa
- Ejercicios avanzados adicionales
- Investigación de herramientas alternativas
- Configuraciones personalizadas

---

## **📊 Progreso del Capítulo**

```
Tema 1.1: [░░░░░░░░░░] 0% completado
Tema 1.2: [░░░░░░░░░░] 0% completado
Tema 1.3: [░░░░░░░░░░] 0% completado
Tema 1.4: [░░░░░░░░░░] 0% completado
Tema 1.5: [░░░░░░░░░░] 0% completado

Progreso Total: [░░░░░░░░░░] 0% completado
```

## **🏆 Logros del Capítulo**

- 🔧 **Configurador**: Completar configuración del entorno
- 👋 **Primer Paso**: Escribir tu primer programa
- 🐛 **Detective**: Usar herramientas de debugging
- 📚 **Estudiante**: Completar todos los temas
- 🎯 **Experto**: Completar ruta experto

---

## **📝 Evaluación del Capítulo**

### **Quiz Final**
- 20 preguntas sobre conceptos clave
- Tiempo límite: 30 minutos
- Puntuación mínima: 80%

### **Proyecto Práctico**
- Configurar entorno completo
- Crear primer proyecto web
- Implementar debugging básico

---

## **🔗 Recursos Adicionales**

- [Documentación oficial de Node.js](https://nodejs.org/docs/)
- [Guía de VS Code](https://code.visualstudio.com/docs)
- [Chrome DevTools Documentation](https://developers.google.com/web/tools/chrome-devtools)
- [MDN JavaScript Guide](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide)

---

## **➡️ Próximo Paso**

Una vez completado este capítulo, continúa con:
**[Capítulo 2 - Variables y Tipos de Datos](../2%20-%20Variables%20y%20Tipos%20de%20Datos/README.md)**
