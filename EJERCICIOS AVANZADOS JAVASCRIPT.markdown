# **EJERCICIOS AVANZADOS JAVASCRIPT**

## **🚀 NIVEL AVANZADO**

### **Ejercicio 1: Sistema de Cache Inteligente**

**Problema:**
Implementa un sistema de cache avanzado que incluya:
- Cache con TTL (Time To Live)
- Cache LRU (Least Recently Used)
- Invalidación de cache por patrones
- Métricas de rendimiento
- Persistencia opcional

```javascript
// Ejemplo de uso:
// const cache = new SmartCache({ maxSize: 100, defaultTTL: 5000 });
// cache.set('user:123', userData, 10000); // TTL personalizado
// const user = cache.get('user:123');
// cache.invalidatePattern('user:*');
```

**Solución:**
```javascript
class SmartCache {
    constructor(options = {}) {
        this.maxSize = options.maxSize || 100;
        this.defaultTTL = options.defaultTTL || 60000; // 1 minuto
        this.enablePersistence = options.enablePersistence || false;
        this.storageKey = options.storageKey || 'smart-cache';
        
        // Estructuras de datos principales
        this.cache = new Map();
        this.accessOrder = new Map(); // Para LRU
        this.expirationTimes = new Map();
        
        // Métricas
        this.metrics = {
            hits: 0,
            misses: 0,
            sets: 0,
            evictions: 0,
            invalidations: 0
        };
        
        // Timers para limpieza automática
        this.cleanupInterval = setInterval(() => this.cleanup(), 30000);
        
        // Cargar datos persistidos si está habilitado
        if (this.enablePersistence) {
            this.loadFromStorage();
        }
    }
    
    set(key, value, ttl = this.defaultTTL) {
        // Verificar si necesitamos hacer espacio
        if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
            this.evictLRU();
        }
        
        // Establecer valor y metadatos
        this.cache.set(key, value);
        this.accessOrder.set(key, Date.now());
        this.expirationTimes.set(key, Date.now() + ttl);
        
        this.metrics.sets++;
        
        // Persistir si está habilitado
        if (this.enablePersistence) {
            this.saveToStorage();
        }
        
        return this;
    }
    
    get(key) {
        // Verificar si la clave existe
        if (!this.cache.has(key)) {
            this.metrics.misses++;
            return undefined;
        }
        
        // Verificar si ha expirado
        const expirationTime = this.expirationTimes.get(key);
        if (Date.now() > expirationTime) {
            this.delete(key);
            this.metrics.misses++;
            return undefined;
        }
        
        // Actualizar orden de acceso para LRU
        this.accessOrder.set(key, Date.now());
        this.metrics.hits++;
        
        return this.cache.get(key);
    }
    
    has(key) {
        if (!this.cache.has(key)) {
            return false;
        }
        
        // Verificar expiración
        const expirationTime = this.expirationTimes.get(key);
        if (Date.now() > expirationTime) {
            this.delete(key);
            return false;
        }
        
        return true;
    }
    
    delete(key) {
        const deleted = this.cache.delete(key);
        this.accessOrder.delete(key);
        this.expirationTimes.delete(key);
        
        if (this.enablePersistence) {
            this.saveToStorage();
        }
        
        return deleted;
    }
    
    clear() {
        this.cache.clear();
        this.accessOrder.clear();
        this.expirationTimes.clear();
        
        if (this.enablePersistence) {
            this.saveToStorage();
        }
    }
    
    // Invalidar por patrón (ej: 'user:*')
    invalidatePattern(pattern) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        let invalidated = 0;
        
        for (const key of this.cache.keys()) {
            if (regex.test(key)) {
                this.delete(key);
                invalidated++;
            }
        }
        
        this.metrics.invalidations += invalidated;
        return invalidated;
    }
    
    // Evicción LRU
    evictLRU() {
        let oldestKey = null;
        let oldestTime = Infinity;
        
        for (const [key, time] of this.accessOrder) {
            if (time < oldestTime) {
                oldestTime = time;
                oldestKey = key;
            }
        }
        
        if (oldestKey) {
            this.delete(oldestKey);
            this.metrics.evictions++;
        }
    }
    
    // Limpieza de elementos expirados
    cleanup() {
        const now = Date.now();
        const expiredKeys = [];
        
        for (const [key, expirationTime] of this.expirationTimes) {
            if (now > expirationTime) {
                expiredKeys.push(key);
            }
        }
        
        expiredKeys.forEach(key => this.delete(key));
        return expiredKeys.length;
    }
    
    // Métricas de rendimiento
    getMetrics() {
        const totalRequests = this.metrics.hits + this.metrics.misses;
        const hitRate = totalRequests > 0 ? (this.metrics.hits / totalRequests) * 100 : 0;
        
        return {
            ...this.metrics,
            hitRate: hitRate.toFixed(2) + '%',
            size: this.cache.size,
            maxSize: this.maxSize
        };
    }
    
    // Persistencia
    saveToStorage() {
        if (typeof localStorage === 'undefined') return;
        
        const data = {
            cache: Array.from(this.cache.entries()),
            accessOrder: Array.from(this.accessOrder.entries()),
            expirationTimes: Array.from(this.expirationTimes.entries()),
            metrics: this.metrics
        };
        
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(data));
        } catch (error) {
            console.warn('Failed to save cache to storage:', error);
        }
    }
    
    loadFromStorage() {
        if (typeof localStorage === 'undefined') return;
        
        try {
            const data = JSON.parse(localStorage.getItem(this.storageKey) || '{}');
            
            if (data.cache) {
                this.cache = new Map(data.cache);
                this.accessOrder = new Map(data.accessOrder);
                this.expirationTimes = new Map(data.expirationTimes);
                this.metrics = { ...this.metrics, ...data.metrics };
                
                // Limpiar elementos expirados después de cargar
                this.cleanup();
            }
        } catch (error) {
            console.warn('Failed to load cache from storage:', error);
        }
    }
    
    // Destructor
    destroy() {
        clearInterval(this.cleanupInterval);
        this.clear();
    }
}

// Pruebas del sistema de cache
const cache = new SmartCache({
    maxSize: 5,
    defaultTTL: 2000,
    enablePersistence: true
});

// Pruebas básicas
cache.set('user:1', { name: 'Juan', age: 30 });
cache.set('user:2', { name: 'María', age: 25 });
cache.set('product:1', { name: 'Laptop', price: 1000 });

console.log('Usuario 1:', cache.get('user:1'));
console.log('Métricas:', cache.getMetrics());

// Prueba de TTL
cache.set('temp', 'temporal', 1000); // Expira en 1 segundo
setTimeout(() => {
    console.log('Temp después de 1.5s:', cache.get('temp')); // undefined
}, 1500);

// Prueba de invalidación por patrón
cache.invalidatePattern('user:*');
console.log('Usuario 1 después de invalidación:', cache.get('user:1')); // undefined
console.log('Producto 1 después de invalidación:', cache.get('product:1')); // existe

// Prueba de LRU
for (let i = 3; i <= 8; i++) {
    cache.set(`item:${i}`, `value${i}`);
}
console.log('Métricas finales:', cache.getMetrics());
```

### **Ejercicio 2: Sistema de Observables Reactivos**

**Problema:**
Implementa un sistema de observables reactivos similar a RxJS pero simplificado, que incluya:
- Creación de observables
- Operadores de transformación (map, filter, reduce)
- Manejo de errores
- Subscripciones y unsubscripciones
- Hot y Cold observables

```javascript
// Ejemplo de uso:
// const observable = Observable.fromArray([1, 2, 3, 4, 5])
//   .map(x => x * 2)
//   .filter(x => x > 4)
//   .subscribe(value => console.log(value));
```

**Solución:**
```javascript
class Observable {
    constructor(subscribeFn) {
        this._subscribe = subscribeFn;
    }
    
    // Método principal de suscripción
    subscribe(observer) {
        // Normalizar observer
        const normalizedObserver = this._normalizeObserver(observer);
        
        // Crear subscription
        const subscription = new Subscription();
        
        try {
            // Ejecutar función de suscripción
            const teardown = this._subscribe(normalizedObserver);
            
            // Configurar teardown
            if (typeof teardown === 'function') {
                subscription.add(teardown);
            }
        } catch (error) {
            normalizedObserver.error(error);
        }
        
        return subscription;
    }
    
    // Operador map
    map(transformFn) {
        return new Observable(observer => {
            return this.subscribe({
                next: value => {
                    try {
                        const transformed = transformFn(value);
                        observer.next(transformed);
                    } catch (error) {
                        observer.error(error);
                    }
                },
                error: error => observer.error(error),
                complete: () => observer.complete()
            });
        });
    }
    
    // Operador filter
    filter(predicateFn) {
        return new Observable(observer => {
            return this.subscribe({
                next: value => {
                    try {
                        if (predicateFn(value)) {
                            observer.next(value);
                        }
                    } catch (error) {
                        observer.error(error);
                    }
                },
                error: error => observer.error(error),
                complete: () => observer.complete()
            });
        });
    }
    
    // Operador reduce
    reduce(reducerFn, initialValue) {
        return new Observable(observer => {
            let accumulator = initialValue;
            let hasValue = arguments.length >= 2;
            
            return this.subscribe({
                next: value => {
                    try {
                        if (hasValue) {
                            accumulator = reducerFn(accumulator, value);
                        } else {
                            accumulator = value;
                            hasValue = true;
                        }
                    } catch (error) {
                        observer.error(error);
                    }
                },
                error: error => observer.error(error),
                complete: () => {
                    if (hasValue) {
                        observer.next(accumulator);
                    }
                    observer.complete();
                }
            });
        });
    }
    
    // Operador take
    take(count) {
        return new Observable(observer => {
            let taken = 0;
            
            return this.subscribe({
                next: value => {
                    if (taken < count) {
                        observer.next(value);
                        taken++;
                        
                        if (taken === count) {
                            observer.complete();
                        }
                    }
                },
                error: error => observer.error(error),
                complete: () => observer.complete()
            });
        });
    }
    
    // Operador debounce
    debounce(delay) {
        return new Observable(observer => {
            let timeoutId;
            
            return this.subscribe({
                next: value => {
                    clearTimeout(timeoutId);
                    timeoutId = setTimeout(() => {
                        observer.next(value);
                    }, delay);
                },
                error: error => observer.error(error),
                complete: () => {
                    clearTimeout(timeoutId);
                    observer.complete();
                }
            });
        });
    }
    
    // Operador catch
    catch(errorHandler) {
        return new Observable(observer => {
            return this.subscribe({
                next: value => observer.next(value),
                error: error => {
                    try {
                        const fallback = errorHandler(error);
                        if (fallback instanceof Observable) {
                            fallback.subscribe(observer);
                        } else {
                            observer.next(fallback);
                            observer.complete();
                        }
                    } catch (handlerError) {
                        observer.error(handlerError);
                    }
                },
                complete: () => observer.complete()
            });
        });
    }
    
    // Normalizar observer
    _normalizeObserver(observer) {
        if (typeof observer === 'function') {
            return {
                next: observer,
                error: error => { throw error; },
                complete: () => {}
            };
        }
        
        return {
            next: observer.next || (() => {}),
            error: observer.error || (error => { throw error; }),
            complete: observer.complete || (() => {})
        };
    }
    
    // Métodos estáticos de creación
    static of(...values) {
        return new Observable(observer => {
            values.forEach(value => observer.next(value));
            observer.complete();
        });
    }
    
    static fromArray(array) {
        return new Observable(observer => {
            array.forEach(value => observer.next(value));
            observer.complete();
        });
    }
    
    static fromPromise(promise) {
        return new Observable(observer => {
            promise
                .then(value => {
                    observer.next(value);
                    observer.complete();
                })
                .catch(error => observer.error(error));
        });
    }
    
    static interval(period) {
        return new Observable(observer => {
            let count = 0;
            const intervalId = setInterval(() => {
                observer.next(count++);
            }, period);
            
            return () => clearInterval(intervalId);
        });
    }
    
    static fromEvent(element, eventName) {
        return new Observable(observer => {
            const handler = event => observer.next(event);
            element.addEventListener(eventName, handler);
            
            return () => element.removeEventListener(eventName, handler);
        });
    }
    
    // Combinar múltiples observables
    static merge(...observables) {
        return new Observable(observer => {
            const subscriptions = observables.map(obs => 
                obs.subscribe({
                    next: value => observer.next(value),
                    error: error => observer.error(error),
                    complete: () => {
                        // Complete solo cuando todos hayan completado
                        if (subscriptions.every(sub => sub.closed)) {
                            observer.complete();
                        }
                    }
                })
            );
            
            return () => subscriptions.forEach(sub => sub.unsubscribe());
        });
    }
}

// Clase Subscription para manejar unsubscripciones
class Subscription {
    constructor() {
        this.closed = false;
        this.teardowns = [];
    }
    
    add(teardown) {
        if (this.closed) {
            if (typeof teardown === 'function') {
                teardown();
            }
            return;
        }
        
        this.teardowns.push(teardown);
    }
    
    unsubscribe() {
        if (this.closed) return;
        
        this.closed = true;
        this.teardowns.forEach(teardown => {
            if (typeof teardown === 'function') {
                teardown();
            }
        });
        this.teardowns = [];
    }
}

// Ejemplos de uso
console.log('=== Ejemplos de Observables ===');

// Ejemplo 1: Observable básico
const numbers = Observable.fromArray([1, 2, 3, 4, 5])
    .map(x => x * 2)
    .filter(x => x > 4)
    .subscribe(value => console.log('Número filtrado:', value));

// Ejemplo 2: Observable de eventos (simulado)
const mockElement = {
    listeners: {},
    addEventListener(event, handler) {
        if (!this.listeners[event]) this.listeners[event] = [];
        this.listeners[event].push(handler);
    },
    removeEventListener(event, handler) {
        if (this.listeners[event]) {
            this.listeners[event] = this.listeners[event].filter(h => h !== handler);
        }
    },
    emit(event, data) {
        if (this.listeners[event]) {
            this.listeners[event].forEach(handler => handler(data));
        }
    }
};

const clicks = Observable.fromEvent(mockElement, 'click')
    .map(event => event.data)
    .debounce(300)
    .subscribe(data => console.log('Click debounced:', data));

// Simular clicks
setTimeout(() => mockElement.emit('click', { data: 'click1' }), 100);
setTimeout(() => mockElement.emit('click', { data: 'click2' }), 200);
setTimeout(() => mockElement.emit('click', { data: 'click3' }), 600);

// Ejemplo 3: Manejo de errores
const errorObservable = Observable.of(1, 2, 3)
    .map(x => {
        if (x === 2) throw new Error('Error en 2');
        return x * 2;
    })
    .catch(error => Observable.of('Error manejado'))
    .subscribe({
        next: value => console.log('Valor:', value),
        error: error => console.log('Error:', error.message),
        complete: () => console.log('Completado')
    });
```

Estos ejercicios avanzados demuestran conceptos complejos de JavaScript como gestión de memoria, programación reactiva, y patrones de diseño sofisticados que son fundamentales para el desarrollo de aplicaciones empresariales.
