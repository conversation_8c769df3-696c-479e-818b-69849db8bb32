<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <!-- Definiciones -->
  <defs>
    <!-- Gradientes -->
    <linearGradient id="successGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="retryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="timeoutGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="processGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <!-- Sombra -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
    
    <!-- Flechas -->
    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
    
    <marker id="successArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#10b981"/>
    </marker>
    
    <marker id="retryArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#f59e0b"/>
    </marker>
    
    <marker id="timeoutArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ef4444"/>
    </marker>
  </defs>

  <!-- Título -->
  <text x="600" y="30" text-anchor="middle" font-size="22" font-weight="bold" fill="#1f2937">
    Flujo de Ejecución: Promesas con Retry y Timeout
  </text>
  
  <!-- Timeline -->
  <line x1="100" y1="70" x2="1100" y2="70" stroke="#64748b" stroke-width="2"/>
  <text x="50" y="75" font-size="12" fill="#64748b">Tiempo</text>
  
  <!-- Marcadores de tiempo -->
  <line x1="200" y1="65" x2="200" y2="75" stroke="#64748b" stroke-width="2"/>
  <text x="200" y="90" text-anchor="middle" font-size="10" fill="#64748b">0ms</text>
  
  <line x1="400" y1="65" x2="400" y2="75" stroke="#64748b" stroke-width="2"/>
  <text x="400" y="90" text-anchor="middle" font-size="10" fill="#64748b">1000ms</text>
  
  <line x1="600" y1="65" x2="600" y2="75" stroke="#64748b" stroke-width="2"/>
  <text x="600" y="90" text-anchor="middle" font-size="10" fill="#64748b">3000ms</text>
  
  <line x1="800" y1="65" x2="800" y2="75" stroke="#64748b" stroke-width="2"/>
  <text x="800" y="90" text-anchor="middle" font-size="10" fill="#64748b">7000ms</text>
  
  <line x1="1000" y1="65" x2="1000" y2="75" stroke="#64748b" stroke-width="2"/>
  <text x="1000" y="90" text-anchor="middle" font-size="10" fill="#64748b">15000ms</text>
  
  <!-- Escenario 1: Éxito en primer intento -->
  <rect x="100" y="120" width="1000" height="120" fill="#f0fdf4" stroke="#16a34a" stroke-width="2" rx="10"/>
  <text x="110" y="145" font-size="16" font-weight="bold" fill="#16a34a">Escenario 1: Éxito en Primer Intento</text>
  
  <!-- Inicio de promesa -->
  <rect x="180" y="160" width="80" height="40" fill="url(#processGradient)" stroke="#1d4ed8" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="220" y="175" text-anchor="middle" font-size="10" font-weight="bold" fill="#fff">Inicio</text>
  <text x="220" y="190" text-anchor="middle" font-size="9" fill="#fff">createPromise()</text>
  
  <!-- Ejecución exitosa -->
  <rect x="300" y="160" width="80" height="40" fill="url(#successGradient)" stroke="#047857" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="340" y="175" text-anchor="middle" font-size="10" font-weight="bold" fill="#fff">Éxito</text>
  <text x="340" y="190" text-anchor="middle" font-size="9" fill="#fff">resolve()</text>
  
  <!-- Flecha de éxito -->
  <line x1="260" y1="180" x2="300" y2="180" stroke="#10b981" stroke-width="3" marker-end="url(#successArrow)"/>
  <text x="280" y="175" text-anchor="middle" font-size="9" fill="#10b981">800ms</text>
  
  <!-- Escenario 2: Retry exitoso -->
  <rect x="100" y="260" width="1000" height="160" fill="#fffbeb" stroke="#f59e0b" stroke-width="2" rx="10"/>
  <text x="110" y="285" font-size="16" font-weight="bold" fill="#f59e0b">Escenario 2: Éxito después de Retry</text>
  
  <!-- Primer intento fallido -->
  <rect x="180" y="300" width="80" height="40" fill="url(#processGradient)" stroke="#1d4ed8" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="220" y="315" text-anchor="middle" font-size="10" font-weight="bold" fill="#fff">Intento 1</text>
  <text x="220" y="330" text-anchor="middle" font-size="9" fill="#fff">Falla</text>
  
  <!-- Delay de retry -->
  <rect x="300" y="300" width="80" height="40" fill="url(#retryGradient)" stroke="#d97706" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="340" y="315" text-anchor="middle" font-size="10" font-weight="bold" fill="#fff">Delay</text>
  <text x="340" y="330" text-anchor="middle" font-size="9" fill="#fff">1000ms</text>
  
  <!-- Segundo intento exitoso -->
  <rect x="420" y="300" width="80" height="40" fill="url(#successGradient)" stroke="#047857" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="460" y="315" text-anchor="middle" font-size="10" font-weight="bold" fill="#fff">Intento 2</text>
  <text x="460" y="330" text-anchor="middle" font-size="9" fill="#fff">Éxito</text>
  
  <!-- Flechas de retry -->
  <line x1="260" y1="320" x2="300" y2="320" stroke="#f59e0b" stroke-width="3" marker-end="url(#retryArrow)"/>
  <line x1="380" y1="320" x2="420" y2="320" stroke="#10b981" stroke-width="3" marker-end="url(#successArrow)"/>
  
  <!-- Backoff exponencial -->
  <rect x="180" y="360" width="320" height="50" fill="#fef3c7" stroke="#f59e0b" stroke-width="1" rx="5"/>
  <text x="340" y="380" text-anchor="middle" font-size="12" font-weight="bold" fill="#92400e">Backoff Exponencial</text>
  <text x="340" y="395" text-anchor="middle" font-size="10" fill="#92400e">Intento 1: 1000ms, Intento 2: 2000ms, Intento 3: 4000ms</text>
  
  <!-- Escenario 3: Timeout -->
  <rect x="100" y="440" width="1000" height="120" fill="#fef2f2" stroke="#dc2626" stroke-width="2" rx="10"/>
  <text x="110" y="465" font-size="16" font-weight="bold" fill="#dc2626">Escenario 3: Timeout después de Múltiples Reintentos</text>
  
  <!-- Múltiples intentos -->
  <rect x="180" y="480" width="60" height="35" fill="url(#processGradient)" stroke="#1d4ed8" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="210" y="500" text-anchor="middle" font-size="9" font-weight="bold" fill="#fff">Int. 1</text>
  
  <rect x="260" y="480" width="60" height="35" fill="url(#retryGradient)" stroke="#d97706" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="290" y="500" text-anchor="middle" font-size="9" font-weight="bold" fill="#fff">Int. 2</text>
  
  <rect x="340" y="480" width="60" height="35" fill="url(#retryGradient)" stroke="#d97706" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="370" y="500" text-anchor="middle" font-size="9" font-weight="bold" fill="#fff">Int. 3</text>
  
  <!-- Timeout final -->
  <rect x="420" y="480" width="80" height="35" fill="url(#timeoutGradient)" stroke="#dc2626" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="460" y="495" text-anchor="middle" font-size="9" font-weight="bold" fill="#fff">TIMEOUT</text>
  <text x="460" y="508" text-anchor="middle" font-size="8" fill="#fff">30000ms</text>
  
  <!-- Flechas de timeout -->
  <line x1="240" y1="497" x2="260" y2="497" stroke="#f59e0b" stroke-width="2" marker-end="url(#retryArrow)"/>
  <line x1="320" y1="497" x2="340" y2="497" stroke="#f59e0b" stroke-width="2" marker-end="url(#retryArrow)"/>
  <line x1="400" y1="497" x2="420" y2="497" stroke="#ef4444" stroke-width="2" marker-end="url(#timeoutArrow)"/>
  
  <!-- Gestión de Concurrencia -->
  <rect x="100" y="580" width="1000" height="180" fill="#f8fafc" stroke="#64748b" stroke-width="2" rx="10"/>
  <text x="110" y="605" font-size="16" font-weight="bold" fill="#374151">Gestión de Concurrencia (maxConcurrency: 3)</text>
  
  <!-- Cola de promesas -->
  <rect x="120" y="620" width="200" height="80" fill="#e0e7ff" stroke="#3b82f6" stroke-width="2" rx="5"/>
  <text x="220" y="640" text-anchor="middle" font-size="12" font-weight="bold" fill="#1e40af">Cola de Espera</text>
  <rect x="130" y="650" width="40" height="20" fill="#bfdbfe" stroke="#3b82f6" stroke-width="1" rx="3"/>
  <text x="150" y="663" text-anchor="middle" font-size="9" fill="#1e40af">P4</text>
  <rect x="175" y="650" width="40" height="20" fill="#bfdbfe" stroke="#3b82f6" stroke-width="1" rx="3"/>
  <text x="195" y="663" text-anchor="middle" font-size="9" fill="#1e40af">P5</text>
  <rect x="220" y="650" width="40" height="20" fill="#bfdbfe" stroke="#3b82f6" stroke-width="1" rx="3"/>
  <text x="240" y="663" text-anchor="middle" font-size="9" fill="#1e40af">P6</text>
  <rect x="130" y="675" width="40" height="20" fill="#bfdbfe" stroke="#3b82f6" stroke-width="1" rx="3"/>
  <text x="150" y="688" text-anchor="middle" font-size="9" fill="#1e40af">P7</text>
  <rect x="175" y="675" width="40" height="20" fill="#bfdbfe" stroke="#3b82f6" stroke-width="1" rx="3"/>
  <text x="195" y="688" text-anchor="middle" font-size="9" fill="#1e40af">P8</text>
  
  <!-- Promesas activas -->
  <rect x="350" y="620" width="200" height="80" fill="#dcfce7" stroke="#16a34a" stroke-width="2" rx="5"/>
  <text x="450" y="640" text-anchor="middle" font-size="12" font-weight="bold" fill="#16a34a">Ejecutándose (3/3)</text>
  <rect x="360" y="650" width="50" height="20" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="385" y="663" text-anchor="middle" font-size="9" fill="#14532d">P1 ⚡</text>
  <rect x="420" y="650" width="50" height="20" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="445" y="663" text-anchor="middle" font-size="9" fill="#14532d">P2 ⚡</text>
  <rect x="480" y="650" width="50" height="20" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="505" y="663" text-anchor="middle" font-size="9" fill="#14532d">P3 ⚡</text>
  
  <!-- Promesas completadas -->
  <rect x="580" y="620" width="200" height="80" fill="#f0fdf4" stroke="#22c55e" stroke-width="2" rx="5"/>
  <text x="680" y="640" text-anchor="middle" font-size="12" font-weight="bold" fill="#16a34a">Completadas</text>
  <rect x="590" y="650" width="40" height="20" fill="#dcfce7" stroke="#22c55e" stroke-width="1" rx="3"/>
  <text x="610" y="663" text-anchor="middle" font-size="9" fill="#14532d">✓</text>
  <rect x="635" y="650" width="40" height="20" fill="#dcfce7" stroke="#22c55e" stroke-width="1" rx="3"/>
  <text x="655" y="663" text-anchor="middle" font-size="9" fill="#14532d">✓</text>
  <rect x="680" y="650" width="40" height="20" fill="#dcfce7" stroke="#22c55e" stroke-width="1" rx="3"/>
  <text x="700" y="663" text-anchor="middle" font-size="9" fill="#14532d">✓</text>
  <rect x="590" y="675" width="40" height="20" fill="#dcfce7" stroke="#22c55e" stroke-width="1" rx="3"/>
  <text x="610" y="688" text-anchor="middle" font-size="9" fill="#14532d">✓</text>
  <rect x="635" y="675" width="40" height="20" fill="#dcfce7" stroke="#22c55e" stroke-width="1" rx="3"/>
  <text x="655" y="688" text-anchor="middle" font-size="9" fill="#14532d">✓</text>
  
  <!-- Promesas fallidas -->
  <rect x="810" y="620" width="200" height="80" fill="#fee2e2" stroke="#dc2626" stroke-width="2" rx="5"/>
  <text x="910" y="640" text-anchor="middle" font-size="12" font-weight="bold" fill="#dc2626">Fallidas</text>
  <rect x="820" y="650" width="40" height="20" fill="#fca5a5" stroke="#dc2626" stroke-width="1" rx="3"/>
  <text x="840" y="663" text-anchor="middle" font-size="9" fill="#7f1d1d">✗</text>
  <rect x="865" y="650" width="40" height="20" fill="#fca5a5" stroke="#dc2626" stroke-width="1" rx="3"/>
  <text x="885" y="663" text-anchor="middle" font-size="9" fill="#7f1d1d">✗</text>
  
  <!-- Flechas de flujo de concurrencia -->
  <line x1="320" y1="660" x2="350" y2="660" stroke="#3b82f6" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="550" y1="660" x2="580" y2="660" stroke="#16a34a" stroke-width="2" marker-end="url(#successArrow)"/>
  <line x1="550" y1="680" x2="810" y2="680" stroke="#dc2626" stroke-width="2" marker-end="url(#timeoutArrow)" stroke-dasharray="5,5"/>
  
  <!-- Etiquetas de flujo -->
  <text x="335" y="655" text-anchor="middle" font-size="9" fill="#3b82f6">siguiente</text>
  <text x="565" y="655" text-anchor="middle" font-size="9" fill="#16a34a">éxito</text>
  <text x="680" y="675" text-anchor="middle" font-size="9" fill="#dc2626">fallo</text>
  
  <!-- Métricas en tiempo real -->
  <rect x="120" y="720" width="880" height="30" fill="#f1f5f9" stroke="#64748b" stroke-width="1" rx="5"/>
  <text x="130" y="740" font-size="11" font-weight="bold" fill="#374151">
    Métricas: Total: 15 | Activas: 3 | Completadas: 8 | Fallidas: 2 | Tasa de éxito: 80% | Tiempo promedio: 1.2s
  </text>
</svg>
