<svg xmlns="http://www.w3.org/2000/svg" width="1000" height="1800" viewBox="0 0 1000 1800">
  <!-- Definición de estilos -->
  <defs>
    <style>
      .title { font-family: Arial; font-size: 24px; font-weight: bold; }
      .subtitle { font-family: Arial; font-size: 18px; font-weight: bold; }
      .code { font-family: 'Courier New'; font-size: 12px; }
      .note { font-family: Arial; font-size: 12px; font-style: italic; }
      .box { stroke-width: 2px; rx: 5px; ry: 5px; }
      .arrow { stroke: #333; stroke-width: 2px; fill: none; marker-end: url(#arrowhead); }
      .connector { stroke: #666; stroke-width: 1.5px; stroke-dasharray: 5,3; fill: none; marker-end: url(#arrowhead); }
      .highlight { font-weight: bold; fill: #d32f2f; }
    </style>
    
    <!-- Definición de marcadores de flecha -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>

    <!-- Gradientes para fondos -->
    <linearGradient id="gradientError" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffcdd2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef9a9a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradientUndefined" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#fff9c4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fff59d;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradientInitialized" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#e8f5e9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c5e1a5;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradientTDZ" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#e1f5fe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#81d4fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradientNull" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#e1bee7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ce93d8;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Título principal -->
  <text x="500" y="40" text-anchor="middle" class="title">Flujo Completo: Estados de Variables en JavaScript</text>
  <text x="500" y="70" text-anchor="middle" class="subtitle">Undefined vs. No Inicializada</text>

  <!-- Leyenda de colores -->
  <rect x="50" y="90" width="900" height="60" fill="#f5f5f5" class="box" stroke="#ccc" />
  <text x="500" y="110" text-anchor="middle" class="subtitle">Leyenda de Estados</text>
  
  <rect x="70" y="120" width="120" height="20" fill="url(#gradientError)" class="box" stroke="#e57373" />
  <text x="130" y="135" text-anchor="middle">Error</text>
  
  <rect x="210" y="120" width="120" height="20" fill="url(#gradientUndefined)" class="box" stroke="#fff176" />
  <text x="270" y="135" text-anchor="middle">Undefined</text>
  
  <rect x="350" y="120" width="120" height="20" fill="url(#gradientInitialized)" class="box" stroke="#a5d6a7" />
  <text x="410" y="135" text-anchor="middle">Inicializado</text>
  
  <rect x="490" y="120" width="120" height="20" fill="url(#gradientTDZ)" class="box" stroke="#81d4fa" />
  <text x="550" y="135" text-anchor="middle">TDZ</text>
  
  <rect x="630" y="120" width="120" height="20" fill="url(#gradientNull)" class="box" stroke="#ce93d8" />
  <text x="690" y="135" text-anchor="middle">Null</text>

  <rect x="770" y="120" width="160" height="20" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="850" y="135" text-anchor="middle">Flujo de Ejecución</text>
  
  <!-- Sección 1: Variables No Declaradas -->
  <rect x="50" y="170" width="900" height="180" fill="#f5f5f5" class="box" stroke="#ccc" />
  <text x="500" y="190" text-anchor="middle" class="subtitle">1. Variables No Declaradas</text>
  
  <!-- Flujo de código para variables no declaradas -->
  <rect x="100" y="210" width="250" height="40" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="225" y="235" text-anchor="middle" class="code">// Intento de acceso</text>
  
  <rect x="100" y="280" width="250" height="50" fill="url(#gradientError)" class="box" stroke="#e57373" />
  <text x="225" y="305" text-anchor="middle" class="code">console.log(variableNoDeclarada);</text>
  <text x="225" y="320" text-anchor="middle" class="code">// ReferenceError</text>
  
  <path d="M225,250 L225,280" class="arrow" />
  
  <!-- Verificación de existencia -->
  <rect x="450" y="210" width="400" height="40" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="650" y="235" text-anchor="middle" class="code">// Verificación segura con typeof</text>
  
  <rect x="450" y="280" width="400" height="50" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="650" y="305" text-anchor="middle" class="code">if (typeof variableNoDeclarada === 'undefined') {</text>
  <text x="650" y="320" text-anchor="middle" class="code">  console.log('La variable no está declarada');</text>
  <text x="650" y="335" text-anchor="middle" class="code">}</text>
  
  <path d="M650,250 L650,280" class="arrow" />
  
  <!-- Sección 2: Variables con var -->
  <rect x="50" y="370" width="900" height="250" fill="#f5f5f5" class="box" stroke="#ccc" />
  <text x="500" y="390" text-anchor="middle" class="subtitle">2. Variables Declaradas con var</text>
  
  <!-- Hoisting con var -->
  <rect x="100" y="410" width="250" height="40" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="225" y="435" text-anchor="middle" class="code">// Acceso antes de declaración</text>
  
  <rect x="100" y="480" width="250" height="50" fill="url(#gradientUndefined)" class="box" stroke="#fff176" />
  <text x="225" y="505" text-anchor="middle" class="code">console.log(variableVar); // undefined</text>
  <text x="225" y="520" text-anchor="middle" class="code">// Hoisting en acción</text>
  
  <rect x="100" y="560" width="250" height="40" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="225" y="585" text-anchor="middle" class="code">var variableVar = 'valor';</text>
  
  <path d="M225,450 L225,480" class="arrow" />
  <path d="M225,530 L225,560" class="arrow" />
  
  <!-- Comportamiento de var -->
  <rect x="450" y="410" width="400" height="40" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="650" y="435" text-anchor="middle" class="code">// Comportamiento de var en bloques</text>
  
  <rect x="450" y="480" width="400" height="110" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="650" y="505" text-anchor="middle" class="code">{</text>
  <text x="650" y="520" text-anchor="middle" class="code">  var variableVarBloque = 'visible fuera';</text>
  <text x="650" y="535" text-anchor="middle" class="code">}</text>
  <text x="650" y="550" text-anchor="middle" class="code">console.log(variableVarBloque); // 'visible fuera'</text>
  <text x="650" y="565" text-anchor="middle" class="code">// var tiene scope de función, no de bloque</text>
  
  <path d="M650,450 L650,480" class="arrow" />
  
  <!-- Sección 3: Variables con let y TDZ -->
  <rect x="50" y="640" width="900" height="300" fill="#f5f5f5" class="box" stroke="#ccc" />
  <text x="500" y="660" text-anchor="middle" class="subtitle">3. Variables con let y Temporal Dead Zone (TDZ)</text>
  
  <!-- TDZ con let -->
  <rect x="100" y="680" width="250" height="40" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="225" y="705" text-anchor="middle" class="code">// Acceso antes de declaración</text>
  
  <rect x="100" y="750" width="250" height="50" fill="url(#gradientError)" class="box" stroke="#e57373" />
  <text x="225" y="775" text-anchor="middle" class="code">console.log(variableLet);</text>
  <text x="225" y="790" text-anchor="middle" class="code">// ReferenceError: TDZ</text>
  
  <rect x="100" y="830" width="250" height="40" fill="url(#gradientTDZ)" class="box" stroke="#81d4fa" />
  <text x="225" y="855" text-anchor="middle" class="code">let variableLet; // TDZ termina aquí</text>
  
  <rect x="100" y="900" width="250" height="40" fill="url(#gradientUndefined)" class="box" stroke="#fff176" />
  <text x="225" y="925" text-anchor="middle" class="code">console.log(variableLet); // undefined</text>
  
  <path d="M225,720 L225,750" class="arrow" />
  <path d="M225,800 L225,830" class="arrow" />
  <path d="M225,870 L225,900" class="arrow" />
  
  <!-- Comportamiento de let en bloques -->
  <rect x="450" y="680" width="400" height="40" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="650" y="705" text-anchor="middle" class="code">// Comportamiento de let en bloques</text>
  
  <rect x="450" y="750" width="400" height="110" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="650" y="775" text-anchor="middle" class="code">{</text>
  <text x="650" y="790" text-anchor="middle" class="code">  let variableLetBloque = 'solo visible aquí';</text>
  <text x="650" y="805" text-anchor="middle" class="code">}</text>
  <text x="650" y="820" text-anchor="middle" class="code">// console.log(variableLetBloque);</text>
  <text x="650" y="835" text-anchor="middle" class="code">// ReferenceError: let tiene scope de bloque</text>
  
  <path d="M650,720 L650,750" class="arrow" />
  
  <!-- Sección 4: Comparación de Estados -->
  <rect x="50" y="960" width="900" height="220" fill="#f5f5f5" class="box" stroke="#ccc" />
  <text x="500" y="980" text-anchor="middle" class="subtitle">4. Comparación de Estados</text>
  
  <!-- Tabla de comparación -->
  <rect x="100" y="1000" width="800" height="160" fill="#ffffff" class="box" stroke="#9e9e9e" />
  
  <!-- Encabezados -->
  <rect x="100" y="1000" width="200" height="30" fill="#e0e0e0" class="box" stroke="#9e9e9e" />
  <text x="200" y="1020" text-anchor="middle" class="subtitle">Estado</text>
  
  <rect x="300" y="1000" width="200" height="30" fill="#e0e0e0" class="box" stroke="#9e9e9e" />
  <text x="400" y="1020" text-anchor="middle" class="subtitle">typeof</text>
  
  <rect x="500" y="1000" width="200" height="30" fill="#e0e0e0" class="box" stroke="#9e9e9e" />
  <text x="600" y="1020" text-anchor="middle" class="subtitle">Comparación</text>
  
  <rect x="700" y="1000" width="200" height="30" fill="#e0e0e0" class="box" stroke="#9e9e9e" />
  <text x="800" y="1020" text-anchor="middle" class="subtitle">Resultado</text>
  
  <!-- Fila 1: No declarada -->
  <rect x="100" y="1030" width="200" height="30" fill="url(#gradientError)" class="box" stroke="#e57373" />
  <text x="200" y="1050" text-anchor="middle">No Declarada</text>
  
  <rect x="300" y="1030" width="200" height="30" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="400" y="1050" text-anchor="middle" class="code">typeof x === 'undefined'</text>
  
  <rect x="500" y="1030" width="200" height="30" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="600" y="1050" text-anchor="middle" class="code">x === undefined</text>
  
  <rect x="700" y="1030" width="200" height="30" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="800" y="1050" text-anchor="middle" class="code">true / ReferenceError</text>
  
  <!-- Fila 2: Declarada no inicializada -->
  <rect x="100" y="1060" width="200" height="30" fill="url(#gradientUndefined)" class="box" stroke="#fff176" />
  <text x="200" y="1080" text-anchor="middle">Declarada (let x;)</text>
  
  <rect x="300" y="1060" width="200" height="30" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="400" y="1080" text-anchor="middle" class="code">typeof x === 'undefined'</text>
  
  <rect x="500" y="1060" width="200" height="30" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="600" y="1080" text-anchor="middle" class="code">x === undefined</text>
  
  <rect x="700" y="1060" width="200" height="30" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="800" y="1080" text-anchor="middle" class="code">true / true</text>
  
  <!-- Fila 3: Inicializada con undefined -->
  <rect x="100" y="1090" width="200" height="30" fill="url(#gradientUndefined)" class="box" stroke="#fff176" />
  <text x="200" y="1110" text-anchor="middle">Inicializada (x = undefined)</text>
  
  <rect x="300" y="1090" width="200" height="30" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="400" y="1110" text-anchor="middle" class="code">typeof x === 'undefined'</text>
  
  <rect x="500" y="1090" width="200" height="30" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="600" y="1110" text-anchor="middle" class="code">x === undefined</text>
  
  <rect x="700" y="1090" width="200" height="30" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="800" y="1110" text-anchor="middle" class="code">true / true</text>
  
  <!-- Fila 4: Inicializada con null -->
  <rect x="100" y="1120" width="200" height="30" fill="url(#gradientNull)" class="box" stroke="#ce93d8" />
  <text x="200" y="1140" text-anchor="middle">Inicializada (x = null)</text>
  
  <rect x="300" y="1120" width="200" height="30" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="400" y="1140" text-anchor="middle" class="code">typeof x === 'object'</text>
  
  <rect x="500" y="1120" width="200" height="30" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="600" y="1140" text-anchor="middle" class="code">x == undefined</text>
  
  <rect x="700" y="1120" width="200" height="30" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="800" y="1140" text-anchor="middle" class="code">true / true</text>
  
  <!-- Sección 5: Verificación de Existencia -->
  <rect x="50" y="1200" width="900" height="220" fill="#f5f5f5" class="box" stroke="#ccc" />
  <text x="500" y="1220" text-anchor="middle" class="subtitle">5. Verificación de Existencia</text>
  
  <!-- Métodos de verificación -->
  <rect x="100" y="1240" width="350" height="160" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="275" y="1260" text-anchor="middle" class="subtitle">Verificación Segura</text>
  
  <text x="120" y="1285" class="code">// Para variables que podrían no estar declaradas</text>
  <text x="120" y="1305" class="code">if (typeof variable === 'undefined') {</text>
  <text x="120" y="1325" class="code">  console.log('Variable no disponible');</text>
  <text x="120" y="1345" class="code">}</text>
  
  <text x="120" y="1375" class="code">// Para propiedades de objetos</text>
  <text x="120" y="1395" class="code">if (obj && obj.prop === undefined) { ... }</text>
  
  <!-- Patrones de inicialización segura -->
  <rect x="550" y="1240" width="350" height="160" fill="#ffffff" class="box" stroke="#9e9e9e" />
  <text x="725" y="1260" text-anchor="middle" class="subtitle">Inicialización Segura</text>
  
  <text x="570" y="1285" class="code">// Valores por defecto (ES6+)</text>
  <text x="570" y="1305" class="code">const valor = param ?? valorPorDefecto;</text>
  
  <text x="570" y="1335" class="code">// Desestructuración con valores por defecto</text>
  <text x="570" y="1355" class="code">const { prop = 'default' } = objeto;</text>
  
  <text x="570" y="1385" class="code">// Verificación de parámetros de función</text>
  <text x="570" y="1405" class="code">function fn(param = 'default') { ... }</text>
  
  <!-- Sección 6: Depuración de Estados -->
  <rect x="50" y="1440" width="900" height="220" fill="#f5f5f5" class="box" stroke="#ccc" />
  <text x="500" y="1460" text-anchor="middle" class="subtitle">6. Depuración de Estados</text>
  
  <!-- Técnicas de depuración -->
  <rect x="100" y="1480" width="800" height="160" fill="#ffffff" class="box" stroke="#9e9e9e" />
  
  <text x="120" y="1505" class="code">// Inspección de variables</text>
  <text x="120" y="1525" class="code">console.log('Variable:', variable);</text>
  <text x="120" y="1545" class="code">console.log('Tipo:', typeof variable);</text>
  
  <text x="120" y="1575" class="code">// Verificación de estado</text>
  <text x="120" y="1595" class="code">console.log('Es undefined?', variable === undefined);</text>
  <text x="120" y="1615" class="code">console.log('Es null?', variable === null);</text>
  <text x="120" y="1635" class="code">console.log('Es null o undefined?', variable == null);</text>
  
  <text x="500" y="1505" class="code">// Depuración con operador opcional</text>
  <text x="500" y="1525" class="code">const valor = objeto?.propiedad?.subpropiedad;</text>
  
  <text x="500" y="1555" class="code">// Depuración con operador nullish</text>
  <text x="500" y="1575" class="code">const resultado = valor ?? 'Valor por defecto';</text>
  
  <text x="500" y="1605" class="code">// Verificación de propiedades</text>
  <text x="500" y="1625" class="code">if ('propiedad' in objeto) { ... }</text>
  
  <!-- Notas y referencias -->
  <text x="500" y="1700" text-anchor="middle" class="note">© JavaScript Avanzado - Variables y Declaraciones - Maestría Completa</text>
  <text x="500" y="1720" text-anchor="middle" class="note">Diagrama de flujo completo del proceso de código</text>
</svg>