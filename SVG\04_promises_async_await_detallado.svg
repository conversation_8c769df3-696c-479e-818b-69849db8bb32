<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .code { font-family: 'Courier New', monospace; font-size: 11px; fill: #e74c3c; }
      .pending { fill: #f39c12; stroke: #d68910; stroke-width: 2; }
      .fulfilled { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .rejected { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .async-func { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .microtask { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .flow-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title">JavaScript Promises y Async/Await - Sistema Asíncrono Completo</text>
  
  <!-- Promise States -->
  <rect x="50" y="60" width="1300" height="200" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
  <text x="700" y="85" text-anchor="middle" class="subtitle">🔄 PROMISE STATES - Ciclo de Vida Completo</text>
  
  <!-- Pending State -->
  <rect x="100" y="110" width="300" height="120" class="pending" rx="10"/>
  <text x="250" y="135" text-anchor="middle" class="subtitle">⏳ PENDING</text>
  <text x="110" y="155" class="text">Initial state</text>
  <text x="110" y="170" class="code">const promise = new Promise((resolve, reject) => {</text>
  <text x="120" y="185" class="code">// Asynchronous operation</text>
  <text x="120" y="200" class="code">setTimeout(() => resolve("Done"), 1000)</text>
  <text x="110" y="215" class="code">})</text>
  
  <!-- Fulfilled State -->
  <rect x="450" y="110" width="300" height="120" class="fulfilled" rx="10"/>
  <text x="600" y="135" text-anchor="middle" class="subtitle">✅ FULFILLED</text>
  <text x="460" y="155" class="text">Operation completed successfully</text>
  <text x="460" y="170" class="code">promise.then(value => {</text>
  <text x="470" y="185" class="code">console.log(value) // "Done"</text>
  <text x="470" y="200" class="code">// Handle success</text>
  <text x="460" y="215" class="code">})</text>
  
  <!-- Rejected State -->
  <rect x="800" y="110" width="300" height="120" class="rejected" rx="10"/>
  <text x="950" y="135" text-anchor="middle" class="subtitle">❌ REJECTED</text>
  <text x="810" y="155" class="text">Operation failed</text>
  <text x="810" y="170" class="code">promise.catch(error => {</text>
  <text x="820" y="185" class="code">console.error(error)</text>
  <text x="820" y="200" class="code">// Handle error</text>
  <text x="810" y="215" class="code">})</text>
  
  <!-- Promise Constructor Deep Dive -->
  <rect x="50" y="300" width="650" height="250" fill="#ffffff" stroke="#3498db" stroke-width="2" rx="10"/>
  <text x="375" y="325" text-anchor="middle" class="subtitle">🏗️ PROMISE CONSTRUCTOR - Internal Mechanics</text>
  
  <text x="60" y="350" class="code">const promise = new Promise((resolve, reject) => {</text>
  <text x="70" y="365" class="code">// Executor function runs immediately</text>
  <text x="70" y="380" class="code">// State: PENDING</text>
  <text x="70" y="395" class="code">// [[PromiseState]]: "pending"</text>
  <text x="70" y="410" class="code">// [[PromiseResult]]: undefined</text>
  <text x="70" y="425" class="code">// [[PromiseFulfillReactions]]: []</text>
  <text x="70" y="440" class="code">// [[PromiseRejectReactions]]: []</text>
  <text x="70" y="460" class="code">if (Math.random() > 0.5) {</text>
  <text x="80" y="475" class="code">resolve("Success!") // Transition to FULFILLED</text>
  <text x="70" y="490" class="code">} else {</text>
  <text x="80" y="505" class="code">reject(new Error("Failed!")) // Transition to REJECTED</text>
  <text x="70" y="520" class="code">}</text>
  <text x="60" y="535" class="code">})</text>
  
  <!-- Promise Methods -->
  <rect x="750" y="300" width="600" height="250" fill="#ffffff" stroke="#27ae60" stroke-width="2" rx="10"/>
  <text x="1050" y="325" text-anchor="middle" class="subtitle">🛠️ PROMISE METHODS - Complete API</text>
  
  <text x="760" y="350" class="text">Instance Methods:</text>
  <text x="770" y="365" class="code">.then(onFulfilled, onRejected)</text>
  <text x="770" y="380" class="code">.catch(onRejected) // Sugar for .then(null, onRejected)</text>
  <text x="770" y="395" class="code">.finally(onFinally) // ES2018</text>
  
  <text x="760" y="415" class="text">Static Methods:</text>
  <text x="770" y="430" class="code">Promise.resolve(value)</text>
  <text x="770" y="445" class="code">Promise.reject(reason)</text>
  <text x="770" y="460" class="code">Promise.all(iterable) // Fail-fast</text>
  <text x="770" y="475" class="code">Promise.allSettled(iterable) // ES2020</text>
  <text x="770" y="490" class="code">Promise.race(iterable) // First to settle</text>
  <text x="770" y="505" class="code">Promise.any(iterable) // ES2021</text>
  
  <text x="760" y="525" class="text">Advanced:</text>
  <text x="770" y="540" class="code">Promise.withResolvers() // ES2024</text>
  
  <!-- Async/Await Deep Dive -->
  <rect x="50" y="580" width="1300" height="300" fill="#f8f9fa" stroke="#9b59b6" stroke-width="2" rx="10"/>
  <text x="700" y="605" text-anchor="middle" class="subtitle">🚀 ASYNC/AWAIT - Syntactic Sugar Over Promises</text>
  
  <!-- Async Function -->
  <rect x="70" y="630" width="400" height="220" class="async-func" rx="5"/>
  <text x="270" y="655" text-anchor="middle" class="subtitle">⚡ Async Function</text>
  <text x="80" y="675" class="code">async function fetchData() {</text>
  <text x="90" y="690" class="code">// Returns Promise automatically</text>
  <text x="90" y="705" class="code">// [[AsyncFunction]]: true</text>
  <text x="90" y="720" class="code">try {</text>
  <text x="100" y="735" class="code">const response = await fetch('/api')</text>
  <text x="100" y="750" class="code">// Pauses execution here</text>
  <text x="100" y="765" class="code">// Yields control to event loop</text>
  <text x="100" y="780" class="code">const data = await response.json()</text>
  <text x="100" y="795" class="code">return data // Resolves promise</text>
  <text x="90" y="810" class="code">} catch (error) {</text>
  <text x="100" y="825" class="code">throw error // Rejects promise</text>
  <text x="90" y="840" class="code">}</text>
  <text x="80" y="855" class="code">}</text>
  
  <!-- Await Mechanism -->
  <rect x="500" y="630" width="400" height="220" fill="#ffffff" stroke="#3498db" stroke-width="2" rx="5"/>
  <text x="700" y="655" text-anchor="middle" class="subtitle">⏸️ Await Mechanism</text>
  <text x="510" y="675" class="text">When await is encountered:</text>
  <text x="520" y="690" class="code">1. Expression is evaluated</text>
  <text x="520" y="705" class="code">2. If not Promise, wrap in Promise.resolve()</text>
  <text x="520" y="720" class="code">3. Suspend function execution</text>
  <text x="520" y="735" class="code">4. Return control to event loop</text>
  <text x="520" y="750" class="code">5. When promise settles:</text>
  <text x="530" y="765" class="code">- Fulfilled: Resume with value</text>
  <text x="530" y="780" class="code">- Rejected: Throw error</text>
  <text x="510" y="800" class="text">Internal:</text>
  <text x="520" y="815" class="code">[[AsyncContext]]: Preserved</text>
  <text x="520" y="830" class="code">[[SuspendedYield]]: true</text>
  <text x="520" y="845" class="code">[[GeneratorState]]: "suspendedYield"</text>
  
  <!-- Error Handling -->
  <rect x="930" y="630" width="400" height="220" fill="#ffffff" stroke="#e74c3c" stroke-width="2" rx="5"/>
  <text x="1130" y="655" text-anchor="middle" class="subtitle">🚨 Error Handling</text>
  <text x="940" y="675" class="text">Try-Catch with Async/Await:</text>
  <text x="950" y="690" class="code">async function handleErrors() {</text>
  <text x="960" y="705" class="code">try {</text>
  <text x="970" y="720" class="code">const result = await riskyOperation()</text>
  <text x="970" y="735" class="code">return result</text>
  <text x="960" y="750" class="code">} catch (error) {</text>
  <text x="970" y="765" class="code">// Handles both sync and async errors</text>
  <text x="970" y="780" class="code">console.error('Error:', error.message)</text>
  <text x="970" y="795" class="code">throw new CustomError(error)</text>
  <text x="960" y="810" class="code">} finally {</text>
  <text x="970" y="825" class="code">// Always executes</text>
  <text x="970" y="840" class="code">cleanup()</text>
  <text x="960" y="855" class="code">}</text>
  <text x="950" y="870" class="code">}</text>
  
  <!-- Microtask Queue Integration -->
  <rect x="50" y="920" width="1300" height="200" fill="#ffffff" stroke="#3498db" stroke-width="2" rx="10"/>
  <text x="700" y="945" text-anchor="middle" class="subtitle">🔄 MICROTASK QUEUE INTEGRATION</text>
  
  <!-- Promise Resolution -->
  <rect x="70" y="970" width="300" height="120" class="microtask" rx="5"/>
  <text x="220" y="995" text-anchor="middle" class="subtitle">📋 Promise Resolution</text>
  <text x="80" y="1015" class="code">promise.then(callback)</text>
  <text x="80" y="1030" class="text">↓</text>
  <text x="80" y="1045" class="code">Microtask Queue</text>
  <text x="80" y="1060" class="text">↓</text>
  <text x="80" y="1075" class="code">Execute callback</text>
  
  <!-- Async Function Continuation -->
  <rect x="400" y="970" width="300" height="120" class="microtask" rx="5"/>
  <text x="550" y="995" text-anchor="middle" class="subtitle">⚡ Async Continuation</text>
  <text x="410" y="1015" class="code">await promise</text>
  <text x="410" y="1030" class="text">↓</text>
  <text x="410" y="1045" class="code">Microtask Queue</text>
  <text x="410" y="1060" class="text">↓</text>
  <text x="410" y="1075" class="code">Resume execution</text>
  
  <!-- Event Loop Priority -->
  <rect x="730" y="970" width="300" height="120" fill="#ffffff" stroke="#f39c12" stroke-width="2" rx="5"/>
  <text x="880" y="995" text-anchor="middle" class="subtitle">🎯 Priority Order</text>
  <text x="740" y="1015" class="text">1. Call Stack (Synchronous)</text>
  <text x="740" y="1030" class="text">2. Microtasks (Promises)</text>
  <text x="740" y="1045" class="text">3. Macrotasks (setTimeout)</text>
  <text x="740" y="1060" class="text">4. Render (if needed)</text>
  <text x="740" y="1075" class="code">Microtasks drain completely!</text>
  
  <!-- Performance Considerations -->
  <rect x="1060" y="970" width="290" height="120" fill="#ffffff" stroke="#27ae60" stroke-width="2" rx="5"/>
  <text x="1205" y="995" text-anchor="middle" class="subtitle">⚡ Performance</text>
  <text x="1070" y="1015" class="text">Memory:</text>
  <text x="1080" y="1030" class="code">Promise: ~100 bytes</text>
  <text x="1080" y="1045" class="code">Async function: ~200 bytes</text>
  <text x="1070" y="1065" class="text">Speed:</text>
  <text x="1080" y="1080" class="code">Native promises are fast</code>
  
  <!-- Advanced Patterns -->
  <rect x="50" y="1150" width="1300" height="120" fill="#f8f9fa" stroke="#9b59b6" stroke-width="2" rx="10"/>
  <text x="700" y="1175" text-anchor="middle" class="subtitle">🎨 ADVANCED PATTERNS</text>
  
  <!-- Parallel Execution -->
  <rect x="70" y="1190" width="250" height="70" fill="#ffffff" stroke="#3498db" stroke-width="1" rx="3"/>
  <text x="195" y="1210" text-anchor="middle" class="text">Parallel Execution</text>
  <text x="80" y="1225" class="code">const [a, b, c] = await Promise.all([</text>
  <text x="90" y="1240" class="code">fetchA(), fetchB(), fetchC()</text>
  <text x="80" y="1255" class="code">])</text>
  
  <!-- Sequential Execution -->
  <rect x="340" y="1190" width="250" height="70" fill="#ffffff" stroke="#e74c3c" stroke-width="1" rx="3"/>
  <text x="465" y="1210" text-anchor="middle" class="text">Sequential Execution</text>
  <text x="350" y="1225" class="code">const a = await fetchA()</text>
  <text x="350" y="1240" class="code">const b = await fetchB(a)</text>
  <text x="350" y="1255" class="code">const c = await fetchC(b)</text>
  
  <!-- Error Recovery -->
  <rect x="610" y="1190" width="250" height="70" fill="#ffffff" stroke="#f39c12" stroke-width="1" rx="3"/>
  <text x="735" y="1210" text-anchor="middle" class="text">Error Recovery</text>
  <text x="620" y="1225" class="code">const result = await promise</text>
  <text x="630" y="1240" class="code">.catch(err => defaultValue)</text>
  <text x="620" y="1255" class="code">// Never throws</text>
  
  <!-- Timeout Pattern -->
  <rect x="880" y="1190" width="250" height="70" fill="#ffffff" stroke="#9b59b6" stroke-width="1" rx="3"/>
  <text x="1005" y="1210" text-anchor="middle" class="text">Timeout Pattern</text>
  <text x="890" y="1225" class="code">const result = await Promise.race([</text>
  <text x="900" y="1240" class="code">fetchData(), timeout(5000)</text>
  <text x="890" y="1255" class="code">])</text>
  
  <!-- Arrows showing state transitions -->
  <path d="M 400 170 L 450 170" class="flow-arrow"/>
  <path d="M 250 230 Q 300 280 450 170" class="flow-arrow dashed"/>
  <path d="M 250 230 Q 300 300 800 170" class="flow-arrow dashed"/>
  
  <!-- Arrows showing async flow -->
  <path d="M 470 750 L 500 750" class="arrow"/>
  <path d="M 900 750 L 930 750" class="arrow"/>
  
  <!-- Microtask flow arrows -->
  <path d="M 220 970 L 220 920" class="arrow"/>
  <path d="M 550 970 L 550 920" class="arrow"/>
</svg>
