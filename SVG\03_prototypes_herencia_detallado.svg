<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .code { font-family: 'Courier New', monospace; font-size: 11px; fill: #e74c3c; }
      .object { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .prototype { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .constructor { fill: #f39c12; stroke: #d68910; stroke-width: 2; }
      .instance { fill: #1abc9c; stroke: #16a085; stroke-width: 2; }
      .chain { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .proto-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title">JavaScript Prototypes y Herencia Prototipal - Sistema Completo</text>
  
  <!-- Object.prototype (Root) -->
  <rect x="550" y="60" width="300" height="120" class="prototype" rx="10"/>
  <text x="700" y="85" text-anchor="middle" class="subtitle">🏛️ Object.prototype (Root)</text>
  <text x="560" y="105" class="text">Methods:</text>
  <text x="570" y="120" class="code">toString(), valueOf(), hasOwnProperty()</text>
  <text x="570" y="135" class="code">isPrototypeOf(), propertyIsEnumerable()</text>
  <text x="570" y="150" class="code">__defineGetter__, __defineSetter__</text>
  <text x="560" y="165" class="small-text">[[Prototype]]: null (End of chain)</text>
  
  <!-- Function.prototype -->
  <rect x="50" y="220" width="300" height="140" class="prototype" rx="10"/>
  <text x="200" y="245" text-anchor="middle" class="subtitle">⚙️ Function.prototype</text>
  <text x="60" y="265" class="text">Methods:</text>
  <text x="70" y="280" class="code">call(), apply(), bind()</text>
  <text x="70" y="295" class="code">toString(), valueOf()</text>
  <text x="60" y="315" class="text">Properties:</text>
  <text x="70" y="330" class="code">length, name, constructor</text>
  <text x="60" y="350" class="small-text">[[Prototype]]: Object.prototype</text>
  
  <!-- Array.prototype -->
  <rect x="400" y="220" width="300" height="140" class="prototype" rx="10"/>
  <text x="550" y="245" text-anchor="middle" class="subtitle">📚 Array.prototype</text>
  <text x="410" y="265" class="text">Methods:</text>
  <text x="420" y="280" class="code">push(), pop(), shift(), unshift()</text>
  <text x="420" y="295" class="code">map(), filter(), reduce(), forEach()</text>
  <text x="420" y="310" class="code">slice(), splice(), concat(), join()</text>
  <text x="410" y="330" class="text">Properties:</text>
  <text x="420" y="345" class="code">length, constructor</text>
  <text x="410" y="355" class="small-text">[[Prototype]]: Object.prototype</text>
  
  <!-- String.prototype -->
  <rect x="750" y="220" width="300" height="140" class="prototype" rx="10"/>
  <text x="900" y="245" text-anchor="middle" class="subtitle">📝 String.prototype</text>
  <text x="760" y="265" class="text">Methods:</text>
  <text x="770" y="280" class="code">charAt(), substring(), slice()</text>
  <text x="770" y="295" class="code">indexOf(), replace(), split()</text>
  <text x="770" y="310" class="code">toUpperCase(), toLowerCase()</text>
  <text x="760" y="330" class="text">Properties:</text>
  <text x="770" y="345" class="code">length, constructor</text>
  <text x="760" y="355" class="small-text">[[Prototype]]: Object.prototype</text>
  
  <!-- Custom Constructor Function -->
  <rect x="1100" y="220" width="250" height="140" class="constructor" rx="10"/>
  <text x="1225" y="245" text-anchor="middle" class="subtitle">🏗️ Person Constructor</text>
  <text x="1110" y="265" class="code">function Person(name, age) {</text>
  <text x="1120" y="280" class="code">this.name = name;</text>
  <text x="1120" y="295" class="code">this.age = age;</text>
  <text x="1110" y="310" class="code">}</text>
  <text x="1110" y="330" class="text">Properties:</text>
  <text x="1120" y="345" class="code">prototype, length, name</text>
  <text x="1110" y="355" class="small-text">[[Prototype]]: Function.prototype</text>
  
  <!-- Person.prototype -->
  <rect x="1100" y="400" width="250" height="120" class="prototype" rx="10"/>
  <text x="1225" y="425" text-anchor="middle" class="subtitle">👤 Person.prototype</text>
  <text x="1110" y="445" class="code">Person.prototype.greet = function() {</text>
  <text x="1120" y="460" class="code">return `Hi, I'm ${this.name}`;</text>
  <text x="1110" y="475" class="code">}</text>
  <text x="1110" y="495" class="text">Properties:</text>
  <text x="1120" y="510" class="code">constructor: Person</text>
  
  <!-- Person Instances -->
  <rect x="800" y="580" width="200" height="120" class="instance" rx="10"/>
  <text x="900" y="605" text-anchor="middle" class="subtitle">👨 john (Instance)</text>
  <text x="810" y="625" class="text">Own Properties:</text>
  <text x="820" y="640" class="code">name: "John"</text>
  <text x="820" y="655" class="code">age: 30</text>
  <text x="810" y="675" class="text">Inherited Methods:</text>
  <text x="820" y="690" class="code">greet() from Person.prototype</text>
  
  <rect x="1050" y="580" width="200" height="120" class="instance" rx="10"/>
  <text x="1150" y="605" text-anchor="middle" class="subtitle">👩 jane (Instance)</text>
  <text x="1060" y="625" class="text">Own Properties:</text>
  <text x="1070" y="640" class="code">name: "Jane"</text>
  <text x="1070" y="655" class="code">age: 25</text>
  <text x="1060" y="675" class="text">Inherited Methods:</text>
  <text x="1070" y="690" class="code">greet() from Person.prototype</text>
  
  <!-- Prototype Chain Visualization -->
  <rect x="50" y="750" width="1300" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
  <text x="700" y="775" text-anchor="middle" class="subtitle">🔗 PROTOTYPE CHAIN - Lookup Mechanism</text>
  
  <!-- Chain Level 1: Instance -->
  <rect x="100" y="800" width="250" height="80" class="instance" rx="5"/>
  <text x="225" y="825" text-anchor="middle" class="subtitle">Level 1: Instance Object</text>
  <text x="110" y="845" class="text">john = new Person("John", 30)</text>
  <text x="110" y="860" class="code">john.name → "John" ✅ Found</text>
  <text x="110" y="875" class="code">john.greet → Not found, check [[Prototype]]</text>
  
  <!-- Chain Level 2: Constructor Prototype -->
  <rect x="400" y="800" width="250" height="80" class="prototype" rx="5"/>
  <text x="525" y="825" text-anchor="middle" class="subtitle">Level 2: Person.prototype</text>
  <text x="410" y="845" class="text">Person.prototype</text>
  <text x="410" y="860" class="code">greet() → ✅ Found</text>
  <text x="410" y="875" class="code">toString → Not found, check [[Prototype]]</text>
  
  <!-- Chain Level 3: Object Prototype -->
  <rect x="700" y="800" width="250" height="80" class="prototype" rx="5"/>
  <text x="825" y="825" text-anchor="middle" class="subtitle">Level 3: Object.prototype</text>
  <text x="710" y="845" class="text">Object.prototype</text>
  <text x="710" y="860" class="code">toString() → ✅ Found</text>
  <text x="710" y="875" class="code">[[Prototype]] → null (End)</text>
  
  <!-- Chain Level 4: null -->
  <rect x="1000" y="800" width="250" height="80" fill="#ecf0f1" stroke="#95a5a6" stroke-width="2" rx="5"/>
  <text x="1125" y="825" text-anchor="middle" class="subtitle">Level 4: null</text>
  <text x="1010" y="845" class="text">End of prototype chain</text>
  <text x="1010" y="860" class="code">Property not found → undefined</text>
  <text x="1010" y="875" class="code">Method not found → TypeError</text>
  
  <!-- Property Lookup Algorithm -->
  <rect x="100" y="920" width="500" height="200" fill="#ffffff" stroke="#3498db" stroke-width="2" rx="5"/>
  <text x="350" y="945" text-anchor="middle" class="subtitle">🔍 Property Lookup Algorithm</text>
  <text x="110" y="965" class="text">1. Check own properties of object</text>
  <text x="120" y="980" class="code">if (obj.hasOwnProperty(prop)) return obj[prop]</text>
  <text x="110" y="1000" class="text">2. Check [[Prototype]] chain</text>
  <text x="120" y="1015" class="code">let current = obj.__proto__</text>
  <text x="120" y="1030" class="code">while (current !== null) {</text>
  <text x="130" y="1045" class="code">if (current.hasOwnProperty(prop))</text>
  <text x="140" y="1060" class="code">return current[prop]</text>
  <text x="130" y="1075" class="code">current = current.__proto__</text>
  <text x="120" y="1090" class="code">}</text>
  <text x="110" y="1110" class="text">3. Return undefined if not found</text>
  
  <!-- Performance & Memory -->
  <rect x="650" y="920" width="350" height="200" fill="#ffffff" stroke="#e74c3c" stroke-width="2" rx="5"/>
  <text x="825" y="945" text-anchor="middle" class="subtitle">⚡ Performance & Memory</text>
  <text x="660" y="965" class="text">Lookup Performance:</text>
  <text x="670" y="980" class="code">Own Property: O(1) - Hash table</text>
  <text x="670" y="995" class="code">Prototype Chain: O(n) - Linear search</text>
  <text x="660" y="1015" class="text">Memory Sharing:</text>
  <text x="670" y="1030" class="code">Methods shared across instances</text>
  <text x="670" y="1045" class="code">Prototype: 1 copy for all instances</text>
  <text x="660" y="1065" class="text">Optimization:</text>
  <text x="670" y="1080" class="code">V8 uses Hidden Classes</text>
  <text x="670" y="1095" class="code">Inline Caching for hot paths</text>
  <text x="670" y="1110" class="code">Shape-based optimization</text>
  
  <!-- Modern ES6 Classes -->
  <rect x="1050" y="920" width="300" height="200" fill="#ffffff" stroke="#27ae60" stroke-width="2" rx="5"/>
  <text x="1200" y="945" text-anchor="middle" class="subtitle">🆕 ES6 Classes (Syntactic Sugar)</text>
  <text x="1060" y="965" class="code">class Person {</text>
  <text x="1070" y="980" class="code">constructor(name, age) {</text>
  <text x="1080" y="995" class="code">this.name = name;</text>
  <text x="1080" y="1010" class="code">this.age = age;</text>
  <text x="1070" y="1025" class="code">}</text>
  <text x="1070" y="1045" class="code">greet() {</text>
  <text x="1080" y="1060" class="code">return `Hi, I'm ${this.name}`;</text>
  <text x="1070" y="1075" class="code">}</text>
  <text x="1060" y="1090" class="code">}</text>
  <text x="1060" y="1110" class="small-text">Still uses prototypes internally!</text>
  
  <!-- Arrows showing prototype chain -->
  <path d="M 700 180 L 550 220" class="proto-arrow"/>
  <path d="M 700 180 L 550 220" class="proto-arrow"/>
  <path d="M 700 180 L 900 220" class="proto-arrow"/>
  <path d="M 200 360 Q 300 400 550 180" class="proto-arrow dashed"/>
  <path d="M 550 360 L 700 180" class="proto-arrow"/>
  <path d="M 900 360 Q 800 400 700 180" class="proto-arrow dashed"/>
  <path d="M 1225 360 L 1225 400" class="arrow"/>
  <path d="M 1225 520 Q 1100 550 900 580" class="proto-arrow"/>
  <path d="M 1225 520 Q 1200 550 1150 580" class="proto-arrow"/>
  
  <!-- Chain arrows -->
  <path d="M 350 840 L 400 840" class="proto-arrow"/>
  <path d="M 650 840 L 700 840" class="proto-arrow"/>
  <path d="M 950 840 L 1000 840" class="proto-arrow"/>
  
  <!-- Constructor relationship -->
  <path d="M 1225 360 Q 1300 300 1300 180 Q 1300 100 700 120" class="arrow dashed"/>
  
  <!-- Additional Info Box -->
  <rect x="50" y="400" width="300" height="320" fill="#ffffff" stroke="#9b59b6" stroke-width="2" rx="5"/>
  <text x="200" y="425" text-anchor="middle" class="subtitle">🧠 Key Concepts</text>
  
  <text x="60" y="445" class="text">1. __proto__ vs prototype:</text>
  <text x="70" y="460" class="code">obj.__proto__ → [[Prototype]]</text>
  <text x="70" y="475" class="code">Constructor.prototype → template</text>
  
  <text x="60" y="495" class="text">2. Constructor Property:</text>
  <text x="70" y="510" class="code">obj.constructor === Constructor</text>
  <text x="70" y="525" class="code">Points back to constructor function</text>
  
  <text x="60" y="545" class="text">3. instanceof Operator:</text>
  <text x="70" y="560" class="code">obj instanceof Constructor</text>
  <text x="70" y="575" class="code">Checks prototype chain</text>
  
  <text x="60" y="595" class="text">4. Object.create():</text>
  <text x="70" y="610" class="code">Object.create(proto)</text>
  <text x="70" y="625" class="code">Creates object with specific prototype</text>
  
  <text x="60" y="645" class="text">5. Prototype Pollution:</text>
  <text x="70" y="660" class="code">Modifying Object.prototype</text>
  <text x="70" y="675" class="code">Affects ALL objects!</text>
  
  <text x="60" y="695" class="text">6. Performance Tips:</text>
  <text x="70" y="710" class="code">Cache prototype methods</text>
  <text x="70" y="725" class="code">Avoid deep prototype chains</text>
</svg>
