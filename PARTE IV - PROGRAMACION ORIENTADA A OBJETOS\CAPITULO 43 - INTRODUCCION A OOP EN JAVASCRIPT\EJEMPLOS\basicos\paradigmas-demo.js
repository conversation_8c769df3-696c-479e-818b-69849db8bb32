/**
 * DEMOSTRACIÓN PRÁCTICA DE PARADIGMAS DE PROGRAMACIÓN
 * 
 * Este ejemplo muestra cómo usar el ParadigmComparator para analizar
 * diferentes implementaciones del mismo problema usando distintos paradigmas.
 * 
 * Problema: Sistema de procesamiento de pedidos de e-commerce
 * - Calcular total de pedidos
 * - Aplicar descuentos
 * - Calcular impuestos
 * - Generar resumen
 */

const ParadigmComparator = require('../CODIGO/paradigmas/ParadigmComparator');

// ===== DATOS DE PRUEBA =====
const pedidosEjemplo = [
    {
        id: 1,
        productos: [
            { nombre: 'Laptop', precio: 1200, cantidad: 1, categoria: 'electronica' },
            { nombre: 'Mouse', precio: 25, cantidad: 2, categoria: 'electronica' }
        ],
        cliente: { tipo: 'premium', descuento: 0.1 },
        region: 'nacional'
    },
    {
        id: 2,
        productos: [
            { nombre: 'Libro', precio: 15, cantidad: 3, categoria: 'libros' },
            { nombre: 'Cuaderno', precio: 5, cantidad: 5, categoria: 'papeleria' }
        ],
        cliente: { tipo: 'regular', descuento: 0.05 },
        region: 'internacional'
    },
    {
        id: 3,
        productos: [
            { nombre: 'Camisa', precio: 40, cantidad: 2, categoria: 'ropa' },
            { nombre: 'Pantalon', precio: 60, cantidad: 1, categoria: 'ropa' }
        ],
        cliente: { tipo: 'vip', descuento: 0.15 },
        region: 'nacional'
    }
];

// ===== IMPLEMENTACIÓN IMPERATIVA =====
/**
 * Procesamiento de pedidos usando paradigma imperativo
 * 
 * Características:
 * - Control explícito del flujo
 * - Variables mutables
 * - Bucles tradicionales
 * - Modificación directa de estado
 */
function procesarPedidosImperativo(pedidos) {
    console.log('🔄 Procesando pedidos con paradigma imperativo...');
    
    // Variables mutables para acumular resultados
    let totalGeneral = 0;
    let totalDescuentos = 0;
    let totalImpuestos = 0;
    let pedidosProcesados = [];
    
    // Configuración de impuestos por región
    let impuestosPorRegion = {
        'nacional': 0.16,
        'internacional': 0.08
    };
    
    // Procesar cada pedido con bucle imperativo
    for (let i = 0; i < pedidos.length; i++) {
        let pedido = pedidos[i];
        let subtotal = 0;
        
        // Calcular subtotal de productos
        for (let j = 0; j < pedido.productos.length; j++) {
            let producto = pedido.productos[j];
            subtotal += producto.precio * producto.cantidad;
        }
        
        // Aplicar descuento del cliente
        let descuento = subtotal * pedido.cliente.descuento;
        let totalConDescuento = subtotal - descuento;
        
        // Calcular impuestos
        let impuesto = totalConDescuento * impuestosPorRegion[pedido.region];
        let totalFinal = totalConDescuento + impuesto;
        
        // Acumular totales
        totalGeneral += totalFinal;
        totalDescuentos += descuento;
        totalImpuestos += impuesto;
        
        // Agregar pedido procesado
        pedidosProcesados.push({
            id: pedido.id,
            subtotal: subtotal,
            descuento: descuento,
            impuesto: impuesto,
            total: totalFinal,
            cliente: pedido.cliente.tipo
        });
    }
    
    return {
        pedidos: pedidosProcesados,
        resumen: {
            totalGeneral: totalGeneral,
            totalDescuentos: totalDescuentos,
            totalImpuestos: totalImpuestos,
            cantidadPedidos: pedidos.length
        }
    };
}

// ===== IMPLEMENTACIÓN FUNCIONAL =====
/**
 * Procesamiento de pedidos usando paradigma funcional
 * 
 * Características:
 * - Funciones puras
 * - Inmutabilidad
 * - Composición de funciones
 * - Transformaciones declarativas
 */

// Funciones puras para cálculos específicos
const calcularSubtotal = (productos) => 
    productos.reduce((total, producto) => total + (producto.precio * producto.cantidad), 0);

const aplicarDescuento = (subtotal, descuento) => subtotal * (1 - descuento);

const calcularImpuesto = (monto, region) => {
    const impuestos = { 'nacional': 0.16, 'internacional': 0.08 };
    return monto * impuestos[region];
};

const procesarPedido = (pedido) => {
    const subtotal = calcularSubtotal(pedido.productos);
    const montoConDescuento = aplicarDescuento(subtotal, pedido.cliente.descuento);
    const impuesto = calcularImpuesto(montoConDescuento, pedido.region);
    const total = montoConDescuento + impuesto;
    
    return {
        id: pedido.id,
        subtotal,
        descuento: subtotal - montoConDescuento,
        impuesto,
        total,
        cliente: pedido.cliente.tipo
    };
};

function procesarPedidosFuncional(pedidos) {
    console.log('⚡ Procesando pedidos con paradigma funcional...');
    
    const pedidosProcesados = pedidos.map(procesarPedido);
    
    const resumen = pedidosProcesados.reduce((acc, pedido) => ({
        totalGeneral: acc.totalGeneral + pedido.total,
        totalDescuentos: acc.totalDescuentos + pedido.descuento,
        totalImpuestos: acc.totalImpuestos + pedido.impuesto,
        cantidadPedidos: acc.cantidadPedidos + 1
    }), {
        totalGeneral: 0,
        totalDescuentos: 0,
        totalImpuestos: 0,
        cantidadPedidos: 0
    });
    
    return {
        pedidos: pedidosProcesados,
        resumen
    };
}

// ===== IMPLEMENTACIÓN ORIENTADA A OBJETOS =====
/**
 * Procesamiento de pedidos usando paradigma orientado a objetos
 * 
 * Características:
 * - Encapsulación de datos y comportamiento
 * - Abstracción de conceptos del dominio
 * - Polimorfismo para diferentes tipos de clientes
 * - Herencia para reutilización
 */

// Clase base para clientes
class Cliente {
    constructor(tipo, descuento) {
        this.tipo = tipo;
        this.descuento = descuento;
    }
    
    calcularDescuento(subtotal) {
        return subtotal * this.descuento;
    }
    
    obtenerBeneficios() {
        return [`Descuento del ${(this.descuento * 100)}%`];
    }
}

// Clases específicas de clientes con comportamiento especializado
class ClientePremium extends Cliente {
    constructor() {
        super('premium', 0.1);
    }
    
    obtenerBeneficios() {
        return [...super.obtenerBeneficios(), 'Envío gratis', 'Soporte prioritario'];
    }
}

class ClienteVIP extends Cliente {
    constructor() {
        super('vip', 0.15);
    }
    
    calcularDescuento(subtotal) {
        // VIP tiene descuento adicional en compras grandes
        const descuentoBase = super.calcularDescuento(subtotal);
        return subtotal > 100 ? descuentoBase * 1.2 : descuentoBase;
    }
    
    obtenerBeneficios() {
        return [...super.obtenerBeneficios(), 'Acceso exclusivo', 'Regalos especiales'];
    }
}

class ClienteRegular extends Cliente {
    constructor() {
        super('regular', 0.05);
    }
}

// Clase para productos
class Producto {
    constructor(nombre, precio, cantidad, categoria) {
        this.nombre = nombre;
        this.precio = precio;
        this.cantidad = cantidad;
        this.categoria = categoria;
    }
    
    calcularSubtotal() {
        return this.precio * this.cantidad;
    }
    
    aplicarDescuentoCategoria() {
        const descuentosPorCategoria = {
            'electronica': 0.02,
            'libros': 0.05,
            'ropa': 0.03
        };
        
        return this.calcularSubtotal() * (descuentosPorCategoria[this.categoria] || 0);
    }
}

// Clase principal para pedidos
class Pedido {
    constructor(id, productos, cliente, region) {
        this.id = id;
        this.productos = productos.map(p => new Producto(p.nombre, p.precio, p.cantidad, p.categoria));
        this.cliente = this.crearCliente(cliente);
        this.region = region;
        this.impuestosPorRegion = {
            'nacional': 0.16,
            'internacional': 0.08
        };
    }
    
    crearCliente(clienteData) {
        switch (clienteData.tipo) {
            case 'premium': return new ClientePremium();
            case 'vip': return new ClienteVIP();
            case 'regular': return new ClienteRegular();
            default: return new ClienteRegular();
        }
    }
    
    calcularSubtotal() {
        return this.productos.reduce((total, producto) => total + producto.calcularSubtotal(), 0);
    }
    
    calcularDescuentoTotal() {
        const subtotal = this.calcularSubtotal();
        const descuentoCliente = this.cliente.calcularDescuento(subtotal);
        const descuentoCategoria = this.productos.reduce((total, producto) => 
            total + producto.aplicarDescuentoCategoria(), 0);
        
        return descuentoCliente + descuentoCategoria;
    }
    
    calcularImpuesto() {
        const subtotal = this.calcularSubtotal();
        const descuento = this.calcularDescuentoTotal();
        const montoConDescuento = subtotal - descuento;
        
        return montoConDescuento * this.impuestosPorRegion[this.region];
    }
    
    calcularTotal() {
        const subtotal = this.calcularSubtotal();
        const descuento = this.calcularDescuentoTotal();
        const impuesto = this.calcularImpuesto();
        
        return subtotal - descuento + impuesto;
    }
    
    procesar() {
        return {
            id: this.id,
            subtotal: this.calcularSubtotal(),
            descuento: this.calcularDescuentoTotal(),
            impuesto: this.calcularImpuesto(),
            total: this.calcularTotal(),
            cliente: this.cliente.tipo,
            beneficios: this.cliente.obtenerBeneficios()
        };
    }
}

// Procesador principal
class ProcesadorPedidos {
    constructor() {
        this.pedidosProcesados = [];
    }
    
    procesar(pedidosData) {
        console.log('🎯 Procesando pedidos con paradigma orientado a objetos...');
        
        this.pedidosProcesados = pedidosData.map(pedidoData => {
            const pedido = new Pedido(
                pedidoData.id,
                pedidoData.productos,
                pedidoData.cliente,
                pedidoData.region
            );
            return pedido.procesar();
        });
        
        return this.generarResultado();
    }
    
    generarResultado() {
        const resumen = this.pedidosProcesados.reduce((acc, pedido) => ({
            totalGeneral: acc.totalGeneral + pedido.total,
            totalDescuentos: acc.totalDescuentos + pedido.descuento,
            totalImpuestos: acc.totalImpuestos + pedido.impuesto,
            cantidadPedidos: acc.cantidadPedidos + 1
        }), {
            totalGeneral: 0,
            totalDescuentos: 0,
            totalImpuestos: 0,
            cantidadPedidos: 0
        });
        
        return {
            pedidos: this.pedidosProcesados,
            resumen
        };
    }
}

function procesarPedidosOOP(pedidos) {
    const procesador = new ProcesadorPedidos();
    return procesador.procesar(pedidos);
}

// ===== DEMOSTRACIÓN PRINCIPAL =====
async function ejecutarDemo() {
    console.log('🚀 INICIANDO DEMOSTRACIÓN DE PARADIGMAS DE PROGRAMACIÓN\n');
    
    // Crear instancia del comparador
    const comparador = new ParadigmComparator();
    
    // Registrar implementaciones
    comparador.registerImplementation('imperativo', procesarPedidosImperativo, {
        description: 'Implementación imperativa con bucles y variables mutables',
        complexity: 'medium',
        maintainability: 'low',
        performance: 'high',
        readability: 'medium'
    });
    
    comparador.registerImplementation('funcional', procesarPedidosFuncional, {
        description: 'Implementación funcional con funciones puras e inmutabilidad',
        complexity: 'low',
        maintainability: 'high',
        performance: 'medium',
        readability: 'high'
    });
    
    comparador.registerImplementation('oop', procesarPedidosOOP, {
        description: 'Implementación OOP con clases, herencia y polimorfismo',
        complexity: 'high',
        maintainability: 'high',
        performance: 'medium',
        readability: 'high'
    });
    
    // Ejecutar comparación
    const resultados = await comparador.executeAll(pedidosEjemplo);
    
    // Mostrar resultados
    console.log(comparador.generateReport(resultados));
    
    // Mostrar resultados específicos
    console.log('\n📋 RESULTADOS ESPECÍFICOS:');
    for (const [paradigma, resultado] of Object.entries(resultados.results)) {
        if (resultado.status === 'success') {
            console.log(`\n${paradigma.toUpperCase()}:`);
            console.log(`Total general: $${resultado.result.resumen.totalGeneral.toFixed(2)}`);
            console.log(`Total descuentos: $${resultado.result.resumen.totalDescuentos.toFixed(2)}`);
            console.log(`Total impuestos: $${resultado.result.resumen.totalImpuestos.toFixed(2)}`);
            console.log(`Pedidos procesados: ${resultado.result.resumen.cantidadPedidos}`);
        }
    }
    
    return resultados;
}

// Ejecutar demo si el archivo se ejecuta directamente
if (require.main === module) {
    ejecutarDemo().catch(console.error);
}

module.exports = {
    ejecutarDemo,
    procesarPedidosImperativo,
    procesarPedidosFuncional,
    procesarPedidosOOP,
    pedidosEjemplo
};
