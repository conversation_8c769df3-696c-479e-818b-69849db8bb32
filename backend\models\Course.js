/**
 * MODELO DE CURSO
 * ===============
 * 
 * Modelo completo para cursos de programación en CodeCraft Academy
 * Soporta múltiples lenguajes, niveles y tipos de contenido
 */

const mongoose = require('mongoose');

const courseSchema = new mongoose.Schema({
    // Información básica
    title: {
        type: String,
        required: [true, 'El título del curso es requerido'],
        trim: true,
        maxlength: [100, 'El título no puede exceder 100 caracteres']
    },
    
    slug: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true
    },
    
    description: {
        type: String,
        required: [true, 'La descripción es requerida'],
        maxlength: [1000, 'La descripción no puede exceder 1000 caracteres']
    },
    
    shortDescription: {
        type: String,
        required: [true, 'La descripción corta es requerida'],
        maxlength: [200, 'La descripción corta no puede exceder 200 caracteres']
    },
    
    // Categorización
    language: {
        type: String,
        required: [true, 'El lenguaje de programación es requerido'],
        enum: [
            'javascript',
            'python',
            'java',
            'csharp',
            'cpp',
            'go',
            'rust',
            'php',
            'ruby',
            'swift',
            'kotlin',
            'typescript',
            'dart',
            'scala',
            'r',
            'matlab',
            'sql',
            'html-css',
            'react',
            'vue',
            'angular',
            'nodejs',
            'django',
            'flask',
            'spring',
            'laravel',
            'rails'
        ]
    },
    
    category: {
        type: String,
        required: [true, 'La categoría es requerida'],
        enum: [
            'programming-fundamentals',
            'web-development',
            'mobile-development',
            'data-science',
            'machine-learning',
            'artificial-intelligence',
            'cybersecurity',
            'cloud-computing',
            'devops',
            'game-development',
            'blockchain',
            'iot',
            'desktop-development',
            'database',
            'testing',
            'algorithms',
            'system-design'
        ]
    },
    
    subcategory: {
        type: String,
        enum: [
            'frontend',
            'backend',
            'fullstack',
            'mobile-native',
            'mobile-hybrid',
            'data-analysis',
            'deep-learning',
            'computer-vision',
            'nlp',
            'web-security',
            'network-security',
            'aws',
            'azure',
            'gcp',
            'ci-cd',
            'containerization',
            'unity',
            'unreal',
            'ethereum',
            'bitcoin',
            'arduino',
            'raspberry-pi',
            'windows',
            'macos',
            'linux',
            'mysql',
            'postgresql',
            'mongodb',
            'unit-testing',
            'integration-testing',
            'sorting',
            'searching',
            'microservices',
            'monolith'
        ]
    },
    
    // Nivel y dificultad
    level: {
        type: String,
        required: [true, 'El nivel es requerido'],
        enum: ['beginner', 'intermediate', 'advanced', 'expert']
    },
    
    difficulty: {
        type: Number,
        required: true,
        min: 1,
        max: 10
    },
    
    // Prerrequisitos
    prerequisites: [{
        courseId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Course'
        },
        title: String,
        required: {
            type: Boolean,
            default: true
        }
    }],
    
    // Contenido del curso
    structure: {
        totalParts: {
            type: Number,
            default: 0
        },
        totalChapters: {
            type: Number,
            default: 0
        },
        totalLessons: {
            type: Number,
            default: 0
        },
        totalExercises: {
            type: Number,
            default: 0
        },
        totalProjects: {
            type: Number,
            default: 0
        },
        estimatedHours: {
            type: Number,
            required: [true, 'Las horas estimadas son requeridas']
        }
    },
    
    // Partes del curso
    parts: [{
        partNumber: {
            type: Number,
            required: true
        },
        title: {
            type: String,
            required: true
        },
        description: String,
        estimatedHours: Number,
        chapters: [{
            chapterNumber: {
                type: Number,
                required: true
            },
            title: {
                type: String,
                required: true
            },
            description: String,
            estimatedHours: Number,
            lessons: [{
                lessonNumber: {
                    type: Number,
                    required: true
                },
                title: {
                    type: String,
                    required: true
                },
                description: String,
                type: {
                    type: String,
                    enum: ['theory', 'practice', 'project', 'quiz', 'video', 'interactive'],
                    default: 'theory'
                },
                content: {
                    markdown: String,
                    videoUrl: String,
                    interactiveUrl: String,
                    codeExamples: [{
                        title: String,
                        code: String,
                        language: String,
                        explanation: String
                    }],
                    exercises: [{
                        title: String,
                        description: String,
                        difficulty: {
                            type: Number,
                            min: 1,
                            max: 5
                        },
                        starterCode: String,
                        solution: String,
                        tests: [{
                            input: String,
                            expectedOutput: String,
                            description: String
                        }],
                        hints: [String]
                    }]
                },
                estimatedMinutes: {
                    type: Number,
                    default: 30
                },
                isPublished: {
                    type: Boolean,
                    default: false
                },
                publishedAt: Date
            }]
        }]
    }],
    
    // Objetivos de aprendizaje
    learningObjectives: [{
        type: String,
        required: true
    }],
    
    // Habilidades que se aprenden
    skills: [{
        name: {
            type: String,
            required: true
        },
        level: {
            type: String,
            enum: ['basic', 'intermediate', 'advanced'],
            default: 'basic'
        }
    }],
    
    // Tecnologías utilizadas
    technologies: [{
        name: {
            type: String,
            required: true
        },
        version: String,
        category: {
            type: String,
            enum: ['language', 'framework', 'library', 'tool', 'database', 'platform']
        }
    }],
    
    // Proyectos del curso
    projects: [{
        title: {
            type: String,
            required: true
        },
        description: String,
        difficulty: {
            type: Number,
            min: 1,
            max: 5
        },
        estimatedHours: Number,
        technologies: [String],
        repository: String,
        liveDemo: String,
        requirements: [String],
        deliverables: [String]
    }],
    
    // Multimedia
    media: {
        thumbnail: {
            type: String,
            required: [true, 'La imagen de portada es requerida']
        },
        banner: String,
        trailer: String,
        gallery: [String]
    },
    
    // Instructor
    instructor: {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        name: String,
        bio: String,
        avatar: String,
        socialLinks: {
            github: String,
            linkedin: String,
            twitter: String,
            website: String
        }
    },
    
    // Colaboradores
    collaborators: [{
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        role: {
            type: String,
            enum: ['co-instructor', 'content-creator', 'reviewer', 'translator']
        },
        permissions: [{
            type: String,
            enum: ['edit-content', 'review-submissions', 'moderate-discussions', 'view-analytics']
        }]
    }],
    
    // Configuración del curso
    settings: {
        isPublished: {
            type: Boolean,
            default: false
        },
        publishedAt: Date,
        isFree: {
            type: Boolean,
            default: true
        },
        price: {
            type: Number,
            default: 0,
            min: 0
        },
        currency: {
            type: String,
            default: 'USD',
            enum: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'MXN', 'BRL', 'ARS']
        },
        enrollmentLimit: {
            type: Number,
            default: null // null = sin límite
        },
        allowDiscussions: {
            type: Boolean,
            default: true
        },
        allowDownloads: {
            type: Boolean,
            default: false
        },
        certificateEnabled: {
            type: Boolean,
            default: true
        },
        completionThreshold: {
            type: Number,
            default: 80, // Porcentaje mínimo para completar
            min: 50,
            max: 100
        }
    },
    
    // Estadísticas
    stats: {
        totalEnrollments: {
            type: Number,
            default: 0
        },
        totalCompletions: {
            type: Number,
            default: 0
        },
        averageRating: {
            type: Number,
            default: 0,
            min: 0,
            max: 5
        },
        totalReviews: {
            type: Number,
            default: 0
        },
        totalViews: {
            type: Number,
            default: 0
        },
        completionRate: {
            type: Number,
            default: 0
        }
    },
    
    // SEO y metadatos
    seo: {
        metaTitle: String,
        metaDescription: String,
        keywords: [String],
        ogImage: String
    },
    
    // Fechas importantes
    launchDate: Date,
    lastUpdated: {
        type: Date,
        default: Date.now
    },
    
    // Estado
    status: {
        type: String,
        enum: ['draft', 'review', 'published', 'archived', 'suspended'],
        default: 'draft'
    },
    
    // Versioning
    version: {
        type: String,
        default: '1.0.0'
    },
    
    // Idiomas disponibles
    availableLanguages: [{
        code: {
            type: String,
            enum: ['es', 'en', 'fr', 'de', 'pt', 'it', 'ja', 'ko', 'zh']
        },
        name: String,
        completionPercentage: {
            type: Number,
            default: 0
        }
    }],
    
    // Tags para búsqueda
    tags: [String]

}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Índices para optimización
courseSchema.index({ language: 1, category: 1 });
courseSchema.index({ level: 1, difficulty: 1 });
courseSchema.index({ 'settings.isPublished': 1, status: 1 });
courseSchema.index({ 'stats.averageRating': -1 });
courseSchema.index({ 'stats.totalEnrollments': -1 });
courseSchema.index({ createdAt: -1 });
courseSchema.index({ slug: 1 });
courseSchema.index({ tags: 1 });
courseSchema.index({ 'instructor.userId': 1 });

// Índice de texto para búsqueda
courseSchema.index({
    title: 'text',
    description: 'text',
    'skills.name': 'text',
    tags: 'text'
});

// Virtuals
courseSchema.virtual('completionRate').get(function() {
    if (this.stats.totalEnrollments === 0) return 0;
    return Math.round((this.stats.totalCompletions / this.stats.totalEnrollments) * 100);
});

courseSchema.virtual('isPopular').get(function() {
    return this.stats.totalEnrollments > 1000 && this.stats.averageRating >= 4.0;
});

courseSchema.virtual('isTrending').get(function() {
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    return this.createdAt > oneMonthAgo && this.stats.totalEnrollments > 100;
});

courseSchema.virtual('url').get(function() {
    return `/courses/${this.language}/${this.slug}`;
});

// Middleware pre-save
courseSchema.pre('save', function(next) {
    // Generar slug si no existe
    if (!this.slug && this.title) {
        this.slug = this.title
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '');
    }
    
    // Actualizar lastUpdated
    this.lastUpdated = new Date();
    
    // Calcular totales de estructura
    this.structure.totalParts = this.parts.length;
    this.structure.totalChapters = this.parts.reduce((total, part) => total + part.chapters.length, 0);
    this.structure.totalLessons = this.parts.reduce((total, part) => 
        total + part.chapters.reduce((chapterTotal, chapter) => chapterTotal + chapter.lessons.length, 0), 0
    );
    
    next();
});

// Métodos de instancia
courseSchema.methods.updateStats = function() {
    // Este método se llamará desde el controlador cuando se actualicen las estadísticas
    this.stats.completionRate = this.completionRate;
    return this.save();
};

courseSchema.methods.addEnrollment = function() {
    this.stats.totalEnrollments += 1;
    return this.save();
};

courseSchema.methods.addCompletion = function() {
    this.stats.totalCompletions += 1;
    this.stats.completionRate = this.completionRate;
    return this.save();
};

courseSchema.methods.updateRating = function(newRating, isNewReview = true) {
    if (isNewReview) {
        const totalRating = this.stats.averageRating * this.stats.totalReviews;
        this.stats.totalReviews += 1;
        this.stats.averageRating = (totalRating + newRating) / this.stats.totalReviews;
    } else {
        // Actualizar rating existente - requiere lógica más compleja
        // Por simplicidad, recalcular desde la base de datos
    }
    return this.save();
};

courseSchema.methods.incrementViews = function() {
    this.stats.totalViews += 1;
    return this.save();
};

courseSchema.methods.publish = function() {
    this.settings.isPublished = true;
    this.settings.publishedAt = new Date();
    this.status = 'published';
    return this.save();
};

courseSchema.methods.unpublish = function() {
    this.settings.isPublished = false;
    this.status = 'draft';
    return this.save();
};

// Métodos estáticos
courseSchema.statics.findPublished = function() {
    return this.find({ 
        'settings.isPublished': true, 
        status: 'published' 
    });
};

courseSchema.statics.findByLanguage = function(language) {
    return this.find({ 
        language, 
        'settings.isPublished': true, 
        status: 'published' 
    });
};

courseSchema.statics.findByCategory = function(category) {
    return this.find({ 
        category, 
        'settings.isPublished': true, 
        status: 'published' 
    });
};

courseSchema.statics.findByLevel = function(level) {
    return this.find({ 
        level, 
        'settings.isPublished': true, 
        status: 'published' 
    });
};

courseSchema.statics.findPopular = function(limit = 10) {
    return this.find({ 
        'settings.isPublished': true, 
        status: 'published' 
    })
    .sort({ 'stats.totalEnrollments': -1, 'stats.averageRating': -1 })
    .limit(limit);
};

courseSchema.statics.findTrending = function(limit = 10) {
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    
    return this.find({ 
        'settings.isPublished': true, 
        status: 'published',
        createdAt: { $gte: oneMonthAgo }
    })
    .sort({ 'stats.totalEnrollments': -1 })
    .limit(limit);
};

courseSchema.statics.searchCourses = function(query, filters = {}) {
    const searchQuery = {
        $text: { $search: query },
        'settings.isPublished': true,
        status: 'published'
    };
    
    // Aplicar filtros
    if (filters.language) searchQuery.language = filters.language;
    if (filters.category) searchQuery.category = filters.category;
    if (filters.level) searchQuery.level = filters.level;
    if (filters.isFree !== undefined) searchQuery['settings.isFree'] = filters.isFree;
    
    return this.find(searchQuery)
        .sort({ score: { $meta: 'textScore' }, 'stats.averageRating': -1 });
};

module.exports = mongoose.model('Course', courseSchema);
