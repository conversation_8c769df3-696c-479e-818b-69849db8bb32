# 4.5.6 - Palabras reservadas por versión

## Introducción
Las palabras reservadas por versión en JavaScript varían según la edición de ECMAScript, como 'let' introducido en ES6 o 'await' en ES2017, lo que requiere atención a la compatibilidad al desarrollar código que deba ejecutarse en entornos con diferentes motores, evitando usos que rompan en versiones antiguas y asegurando migraciones suaves. Entender estas variaciones es crucial para mantener código portable, especialmente en proyectos legacy o polyfills, donde linters y transpilers ayudan a detectar incompatibilidades, promoviendo prácticas que equilibren innovación con soporte amplio en aplicaciones web globales.

## Ejemplo de Código
```javascript
// ES6: let varName = 'ES6';
// En ES5: var varName = 'ES5'; // 'let' no reservada en ES5
console.log(varName);
```

## Resultados en Consola
ES5 (o ES6 dependiendo de la versión)

## Diagrama de Flujo de Código
<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg"><rect x="10" y="10" width="280" height="180" fill="none" stroke="black"/><text x="150" y="50" text-anchor="middle">Versión ECMAScript</text><line x1="150" y1="60" x2="150" y2="100" stroke="black"/><text x="150" y="130" text-anchor="middle">Reserved words aplicadas</text><line x1="150" y1="140" x2="150" y2="180" stroke="black"/></svg>

## Visualización Conceptual
<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg"><path d="M10 10 L190 10 L100 190 Z" fill="lightyellow"/><text x="100" y="100" text-anchor="middle">Reserved por Versión</text></svg>

## Casos de Uso
- Usar 'let' en ES6+ para scoping en bucles.
- Implementar 'await' en ES2017 para async functions.
- Aplicar 'class' en ES6 para OOP.
- Emplear 'import' en ES6 para módulos.
- Utilizar 'yield' en ES6 para generators.

## Errores Comunes
- Usar keywords de ES6 en entornos ES5, causando SyntaxError.
- No polyfill keywords nuevas en browsers antiguos.
- Ignorar diferencias de versiones en equipos distribuidos.
- Confundir reserved words entre ES versions en migraciones.
- Olvidar configurar transpilers para target versions.

## Recomendaciones
- Especificar target ECMAScript en Babel configs.
- Usar caniuse.com para chequear soporte de keywords.
- Implementar tests cross-version.
- Documentar requirements de versión en proyectos.
- Actualizar gradualmente a nuevas versions con polyfills.