<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Definiciones -->
  <defs>
    <!-- Gradientes -->
    <linearGradient id="outerFunctionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="privateVarsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="privateFunctionsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ec4899;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#be185d;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="publicApiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    
    <!-- Sombra -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="3" dy="3" stdDeviation="4" flood-color="#000" flood-opacity="0.3"/>
    </filter>
    
    <!-- Flecha -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6366f1"/>
    </marker>
  </defs>

  <!-- Título -->
  <text x="400" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2563eb">
    Anatomía de un Closure: Sistema de Gestión de Estado
  </text>
  
  <!-- Función Externa -->
  <rect x="50" y="60" width="700" height="520" fill="#f8fafc" stroke="#2563eb" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="70" y="85" font-size="16" font-weight="bold" fill="#2563eb">
    function crearGestorEstado(estadoInicial)
  </text>
  
  <!-- Variables Privadas -->
  <rect x="80" y="100" width="300" height="150" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="90" y="125" font-size="14" font-weight="bold" fill="#92400e">Variables Privadas (Closure Scope)</text>
  <text x="90" y="145" font-size="12" fill="#92400e">• let estado = {...estadoInicial}</text>
  <text x="90" y="165" font-size="12" fill="#92400e">• let historial = [...]</text>
  <text x="90" y="185" font-size="12" fill="#92400e">• let contadorTransacciones = 0</text>
  <text x="90" y="205" font-size="12" fill="#92400e">• function validarCambio()</text>
  <text x="90" y="225" font-size="12" fill="#92400e">• function registrarCambio()</text>
  
  <!-- Funciones Privadas -->
  <rect x="400" y="100" width="320" height="150" fill="#fce7f3" stroke="#ec4899" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="410" y="125" font-size="14" font-weight="bold" fill="#be185d">Funciones Privadas</text>
  <text x="410" y="145" font-size="12" fill="#be185d">• validarCambio(nuevoEstado)</text>
  <text x="410" y="165" font-size="12" fill="#be185d">• registrarCambio(anterior, nuevo)</text>
  <text x="410" y="185" font-size="12" fill="#be185d">• calcularDiferencias(ant, nuevo)</text>
  <text x="410" y="205" font-size="12" fill="#be185d">Solo accesibles dentro del closure</text>
  
  <!-- Objeto Retornado -->
  <rect x="80" y="280" width="640" height="280" fill="#ecfdf5" stroke="#10b981" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="90" y="305" font-size="16" font-weight="bold" fill="#047857">Objeto Retornado (API Pública)</text>
  
  <!-- Métodos Públicos -->
  <rect x="100" y="320" width="180" height="220" fill="#ffffff" stroke="#10b981" stroke-width="1" rx="3"/>
  <text x="110" y="340" font-size="12" font-weight="bold" fill="#047857">Métodos de Lectura</text>
  <text x="110" y="360" font-size="11" fill="#047857">• obtenerEstado()</text>
  <text x="110" y="380" font-size="11" fill="#047857">• obtenerHistorial()</text>
  <text x="110" y="400" font-size="11" fill="#047857">• obtenerEstadisticas()</text>
  
  <rect x="300" y="320" width="180" height="220" fill="#ffffff" stroke="#10b981" stroke-width="1" rx="3"/>
  <text x="310" y="340" font-size="12" font-weight="bold" fill="#047857">Métodos de Escritura</text>
  <text x="310" y="360" font-size="11" fill="#047857">• actualizarEstado()</text>
  <text x="310" y="380" font-size="11" fill="#047857">• establecerEstado()</text>
  <text x="310" y="400" font-size="11" fill="#047857">• deshacer()</text>
  
  <rect x="500" y="320" width="180" height="220" fill="#ffffff" stroke="#10b981" stroke-width="1" rx="3"/>
  <text x="510" y="340" font-size="12" font-weight="bold" fill="#047857">Métodos Reactivos</text>
  <text x="510" y="360" font-size="11" fill="#047857">• suscribirse(callback)</text>
  <text x="510" y="380" font-size="11" fill="#047857">• verificarCambios()</text>
  
  <!-- Flechas indicando acceso a variables privadas -->
  <line x1="190" y1="250" x2="190" y2="320" stroke="#6366f1" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="390" y1="250" x2="390" y2="320" stroke="#6366f1" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="590" y1="250" x2="590" y2="320" stroke="#6366f1" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <text x="200" y="290" font-size="10" fill="#6366f1">Acceso via Closure</text>
  <text x="400" y="290" font-size="10" fill="#6366f1">Acceso via Closure</text>
  <text x="600" y="290" font-size="10" fill="#6366f1">Acceso via Closure</text>
</svg>
