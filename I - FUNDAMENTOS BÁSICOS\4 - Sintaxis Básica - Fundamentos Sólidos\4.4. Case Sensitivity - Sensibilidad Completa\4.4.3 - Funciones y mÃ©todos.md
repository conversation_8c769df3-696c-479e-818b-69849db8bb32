# 4.4.3 - Funciones y métodos

## Introducción
En JavaScript, la sensibilidad a mayúsculas y minúsculas se extiende a la definición y llamada de funciones y métodos, lo que significa que el lenguaje distingue estrictamente entre letras mayúsculas y minúsculas en sus nombres. Esto implica que una función nombrada 'calcular' es completamente diferente de 'Calcular' o 'CALCULAR', y cualquier discrepancia en el case al invocarlas resultará en errores de referencia. Esta característica es fundamental para mantener la precisión en el código, evitando confusiones y promoviendo convenciones de nomenclatura consistentes como camelCase para funciones regulares y PascalCase para constructores. Entender esta sensibilidad ayuda a los desarrolladores a depurar problemas comunes relacionados con nombres inconsistentes, especialmente en equipos grandes donde múltiples personas contribuyen al código, y asegura que el código sea más legible y mantenible a largo plazo.

## Ejemplo de Código
```javascript
function saludar(nombre) {
  return `Hola, ${nombre}`;
}

console.log(saludar('Mundo')); // Correcto
console.log(Saludar('Mundo')); // Error: Saludar is not defined
```

## Resultados en Consola
Hola, Mundo
Uncaught ReferenceError: Saludar is not defined

## Diagrama de Flujo de Código
[Flujo: Definición de función en minúsculas -> Llamada correcta -> Éxito; Llamada con mayúscula -> Error]

## Visualización Conceptual
Imagina dos cajas idénticas pero con etiquetas 'funcion' y 'Funcion' – JavaScript las trata como entidades separadas, requiriendo coincidencia exacta para acceso.

## Casos de Uso
La sensibilidad al case en funciones es crucial en escenarios como el desarrollo de APIs donde los métodos deben coincidir exactamente con la documentación para evitar fallos en integraciones de terceros. Por ejemplo, en bibliotecas como React, métodos como componentDidMount deben escribirse con el case preciso para que el ciclo de vida funcione correctamente, previniendo bugs sutiles en aplicaciones de gran escala. En entornos de programación colaborativa, esta regla enforces convenciones de nomenclatura estandarizadas, reduciendo errores en merges de código y facilitando revisiones. Además, en scripts de automatización o herramientas CLI, nombres de funciones case-sensitive aseguran que comandos personalizados se ejecuten sin ambigüedades, mejorando la robustez en pipelines de CI/CD.

## Errores Comunes
Un error frecuente es declarar una función en camelCase pero invocarla con variaciones como snake_case o todo en mayúsculas, lo que lleva a ReferenceErrors inesperados, especialmente en código copiado de fuentes con diferentes estilos. Otro problema común ocurre en herencia de prototipos donde métodos sobrescritos no coinciden en case, causando que se llamen versiones incorrectas y produciendo comportamientos erráticos en objetos. En módulos importados, discrepancias en el case de nombres exportados pueden fallar silenciosamente en algunos entornos case-insensitive como Windows, pero explotar en producción en sistemas Linux, destacando la importancia de pruebas cross-platform exhaustivas.

## Recomendaciones
Para mitigar problemas de case sensitivity, adopta convenciones consistentes como camelCase para funciones y PascalCase para clases, y utiliza linters como ESLint con reglas específicas para enforzar estas normas en todo el equipo. Siempre revisa el case al refactorizar código o integrar bibliotecas externas, y considera herramientas de autocompletado en editores como VS Code para reducir errores tipográficos. En proyectos grandes, implementa revisiones de código obligatorias enfocadas en consistencia de nomenclatura, y documenta las convenciones en un style guide compartido para onboard nuevos desarrolladores eficientemente, minimizando bugs relacionados con case en el largo plazo.