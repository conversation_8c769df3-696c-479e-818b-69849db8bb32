## 4.4.2. Variables y case sensitivity

### Introducción
Variables en JS son case-sensitive.  
'myVar' y 'myvar' son distintas.  
Afecta declaración y acceso.  
Evita confusiones.  
Usa conventions consistentes.  
¿Distingues case en variables?

### Código de Ejemplo
```javascript
let myVar = 'hello';
let myvar = 'world';  // Diferentes
console.log(myVar);  // 'hello'
console.log(myvar);  // 'world'

// Error común
let User = { name: 'John' };
console.log(user.name);  // ReferenceError: user is not defined

// En scope
if (true) {
  let ScopeVar = 1;
}
console.log(scopevar);  // ReferenceError

// Global vs local
window.Global = 10;
console.log(global);  // undefined
```
### Resultados en Consola
- 'hello' para myVar
- 'world' para myvar
- ReferenceError para user
- ReferenceError para scopevar
- undefined para global

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> DeclareVar["Paso 1: Declarar variable <br> - Específico case"]
    DeclareVar --> AccessVar["Paso 2: Acceder <br> - Match case?"]
    AccessVar -->|Sí| Success["Paso 3: Éxito <br> - Valor obtenido"]
    AccessVar -->|No| Error["Paso 4: Error <br> - Reference/Undefined"]
    Error --> DebugCase["Paso 5: Debug <br> - Verificar case"]
    DebugCase --> FixCase["Paso 6: Corregir <br> - Ajustar case"]
    FixCase --> CorrectAccess["Paso 7: Acceso correcto <br> - Funciona"]
    CorrectAccess --> Fin["Paso 8: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Conventions["Usar naming conventions <br> - Consistente"]
    AccessVar --> NotaSensitive["Nota: Case-sensitive"]
    Error --> NotaError["Nota: Errores comunes"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style DeclareVar fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style AccessVar fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Success fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Error fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style DebugCase fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style FixCase fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CorrectAccess fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Conventions fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaSensitive fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaError fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> VarDeclare["Declarar var <br> - Case específico"]
        VarDeclare --> VarAccess["Acceder var <br> - Case match"]
        VarAccess -->|Sí| Value["Valor <br> - Correcto"]
        VarAccess -->|No| Fail["Falla <br> - Error"]
        Fail --> Check["Verificar <br> - Case"]
        Check --> Adjust["Ajustar <br> - Case"]
        Adjust --> Value
        Inicio --> Desarrollador(("Desarrollador")) --> Consistent["Mantener consistencia <br> - Naming"]
        VarDeclare --> NotaVar["Nota: Variables case-sensitive"]
        Fail --> NotaFail["Nota: ReferenceError común"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style VarDeclare fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style VarAccess fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Value fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fail fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Check fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Adjust fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Consistent fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaVar fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaFail fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Declaración de variables.
2. Acceso en funciones.
3. En objetos globales.
4. Scope blocks.
5. En modules.

### Errores comunes
1. Case mismatch en acceso.
2. Confundir global/local.
3. Copiar con case wrong.
4. Ignorar en strings.
5. No usar linters.

### Recomendaciones
1. Usa camelCase.
2. Verifica case siempre.
3. Emplea autocomplete.
4. Documenta variables.
5. Test exhaustivo.