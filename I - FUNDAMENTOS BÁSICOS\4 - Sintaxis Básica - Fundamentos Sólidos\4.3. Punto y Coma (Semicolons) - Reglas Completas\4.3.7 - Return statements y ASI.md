## 4.3.7. Return statements y ASI

### Introducción
ASI afecta return statements multilínea.  
Inserta ; después return si newline.  
Retorna undefined en vez de valor.  
Problema común en funciones.  
Requiere cuidado en formatting.  
¿Manejas returns con ASI?

### Código de Ejemplo
```javascript
// Bad: ASI inserta ;
function badReturn() {
  return
  {
    a: 1
  };
}  // Retorna undefined

// Good: Sin newline después return
function goodReturn() {
  return {
    a: 1
  };
}  // Retorna {a:1}

// Con array
function arrayReturn() {
  return
  [1,2,3];
}  // undefined, ASI inserta ;

// Con expresión
function exprReturn() {
  return
  1 + 2;
}  // undefined
```
### Resultados en Consola
- undefined para badReturn()
- {a:1} para goodReturn()
- undefined para arrayReturn()
- undefined para exprReturn()

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> WriteReturn["Paso 1: Escribir return <br> - Con newline?"]
    WriteReturn -->|Sí| ASIInsert["Paso 2: ASI inserta ; <br> - Después return"]
    ASIInsert --> ReturnUndef["Paso 3: Retorna undefined <br> - Valor perdido"]
    WriteReturn -->|No| ReturnValue["Paso 4: Retorna valor <br> - Objeto/array/expr"]
    ReturnUndef --> Debug["Paso 5: Debug <br> - Verificar formatting"]
    Debug --> FixFormat["Paso 6: Fix <br> - Eliminar newline"]
    FixFormat --> CorrectReturn["Paso 7: Return correcto <br> - Valor esperado"]
    CorrectReturn --> Fin["Paso 8: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> BestPractice["Usar same-line return"]
    ASIInsert --> NotaASI["Nota: Regla ASI para return"]
    ReturnUndef --> NotaBug["Nota: Bug común"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style WriteReturn fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ASIInsert fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ReturnUndef fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ReturnValue fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Debug fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style FixFormat fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CorrectReturn fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style BestPractice fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaASI fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaBug fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> ReturnStmt["Return statement <br> - Con newline"]
        ReturnStmt --> ASI["ASI <br> - Inserta ;"]
        ASI --> Undefined["Retorna undefined <br> - Bug"]
        ReturnStmt -->|Sin newline| Value["Retorna valor <br> - Correcto"]
        Undefined --> Identify["Identificar issue <br> - Debug"]
        Identify --> Adjust["Ajustar formatting <br> - Same line"]
        Adjust --> Success["Éxito <br> - Return correcto"]
        Inicio --> Desarrollador(("Desarrollador")) --> Lint["Usar linter <br> - Detectar"]
        ASI --> NotaReturn["Nota: Return sensible a ASI"]
        Undefined --> NotaCommon["Nota: Error frecuente"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ReturnStmt fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ASI fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Undefined fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Value fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Identify fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Adjust fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Success fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Lint fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaReturn fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaCommon fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Funciones retornando objetos.
2. Returns de arrays.
3. Expresiones complejas.
4. En arrow functions.
5. En async functions.

### Errores comunes
1. Newline después return.
2. No probar returns.
3. Ignorar linter warnings.
4. Multilínea sin parens.
5. ASI en exports.

### Recomendaciones
1. Return en same line.
2. Usa parens para multilínea.
3. Configura linter rules.
4. Test funciones.
5. Review código.