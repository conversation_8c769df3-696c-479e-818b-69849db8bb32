## 4.1.6. Estructura de módulos ES6

### Introducción
La estructura de módulos ES6 permite organizar código en archivos reutilizables.  
Usa import y export para compartir funcionalidades entre módulos.  
Esto es vital para proyectos grandes y mantenibles.  
Además, promueve la modularidad y reduce el código global.  
Entenderlo facilita el desarrollo colaborativo.  
¿ Cómo organizas tu código en módulos reutilizables?

### Código de Ejemplo
```javascript
// modulo.js
export const saludo = 'Hola desde módulo';
export function sumar(a, b) {
    return a + b;
}

// main.js
import { saludo, sumar } from './modulo.js';
console.log(saludo);
console.log(sumar(3, 4));
```
### Resultados en Consola
- `Hola desde módulo`
- `7`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> Export1["Paso 1: Exportar constante <br> - Nombre: saludo <br> - Valor: 'Hola desde módulo'"]
    Export1 --> Export2["Paso 2: Exportar función <br> - Nombre: sumar(a, b)"]
    Export2 --> Import["Paso 3: Importar {saludo, sumar} <br> - Desde: './modulo.js'"]
    Import --> Log1["Paso 4: console.log(saludo) <br> - Resultado: Hola desde módulo"]
    Log1 --> Llamada["Paso 5: Llamar sumar(3, 4)"]
    Llamada --> Log2["Paso 6: console.log(sumar(3, 4)) <br> - Resultado: 7"]
    Log2 --> Fin["Paso 7: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Modulo["Módulo"]
    Export2 --> NotaExport["Nota: Compartir código"]
    Import --> NotaImport["Nota: Usar en otro archivo"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Export1 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Export2 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Import fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log1 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Llamada fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:12px
    style Log2 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Modulo fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaExport fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaImport fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> ModuloExport["Módulo Export <br> - Exportar saludo, sumar"]
        ModuloExport --> ModuloImport["Módulo Import <br> - Importar {saludo, sumar}"]
        ModuloImport --> Usar["Usar <br> - Log saludo, sumar(3,4)"]
        Usar --> Resultados["Resultados <br> - Hola..., 7"]
        Inicio --> Desarrollador(("Desarrollador")) --> Modularidad["Modularidad"]
        ModuloExport --> NotaModular["Nota: Organizar código"]
        ModuloImport --> NotaReutilizar["Nota: Reutilizar funcionalidades"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ModuloExport fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ModuloImport fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Usar fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Resultados fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Modularidad fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaModular fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaReutilizar fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Separar lógica en módulos para proyectos escalables.
2. Exportar funciones reutilizables en bibliotecas internas.
3. Importar componentes en aplicaciones web modulares.
4. Gestionar dependencias en entornos de desarrollo grandes.
5. Facilitar pruebas unitarias al aislar módulos.

### Errores comunes
1. Olvidar la extensión .js en imports relativos.
2. Exportar sin default cuando se espera named export.
3. Ciclos de dependencias entre módulos.
4. No manejar correctamente paths relativos.
5. Usar modules en entornos no compatibles sin transpilers.

### Recomendaciones
1. Usa named exports para claridad en imports.
2. Organiza módulos en directorios lógicos.
3. Evita exports default a menos que sea un solo ítem.
4. Prueba imports en diferentes entornos.
5. Integra con bundlers como Webpack para producción.