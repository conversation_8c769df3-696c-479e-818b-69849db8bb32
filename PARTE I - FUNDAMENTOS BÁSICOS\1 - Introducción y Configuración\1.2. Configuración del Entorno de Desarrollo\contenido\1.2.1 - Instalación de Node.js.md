# 1.2.1 - Instalación de Node.js

📍 **Ubicación:** Parte I > Capítulo 1 > Configuración del Entorno > Instalación de Node.js  
⬅️ **Anterior:** [1.1.10 - Casos de Uso Modernos](../../1.1.%20Historia%20y%20Evolución%20de%20JavaScript/contenido/1.1.10%20-%20Casos%20de%20Uso%20Modernos.md)  
➡️ **Siguiente:** [1.2.2 - Configuración de Visual Studio Code](1.2.2%20-%20Configuración%20de%20Visual%20Studio%20Code.md)  
🏠 **Índice:** [Volver al tema de Configuración del Entorno](../README.md)

## 🎯 Objetivos de Aprendizaje
- [ ] Comprender qué es Node.js y por qué es esencial para el desarrollo JavaScript
- [ ] Instalar Node.js correctamente en diferentes sistemas operativos
- [ ] Verificar la instalación y configuración inicial
- [ ] Conocer las herramientas incluidas (npm, npx)

## 📈 Progreso del Tema
[▓▓░░░░░░░░] 20% completado

### En este subtema:
- [x] Introducción a Node.js ← **Estás aquí**
- [ ] Instalación paso a paso
- [ ] Verificación de la instalación
- [ ] Configuración inicial

## 📚 Introducción

Node.js es un entorno de ejecución de JavaScript construido sobre el motor V8 de Chrome que permite ejecutar JavaScript fuera del navegador, revolucionando el desarrollo web al permitir usar JavaScript tanto en el frontend como en el backend. Su arquitectura basada en eventos y su modelo de I/O no bloqueante lo hacen extremadamente eficiente para aplicaciones en tiempo real, APIs, herramientas de desarrollo y automatización de tareas. Para el desarrollo moderno de JavaScript, Node.js es esencial no solo para ejecutar código del lado del servidor, sino también para usar herramientas de desarrollo como bundlers, transpiladores, linters y gestores de paquetes que forman parte del ecosistema actual de desarrollo web.

### Conceptos Clave
- **Runtime Environment**: Entorno que permite ejecutar JavaScript fuera del navegador
- **V8 Engine**: Motor de JavaScript de Google Chrome que impulsa Node.js
- **npm**: Gestor de paquetes incluido con Node.js
- **Event-driven**: Arquitectura basada en eventos para alta concurrencia

## 💻 Código de Ejemplo

### Ejemplo Básico - Verificación de Instalación
```javascript
// Verificar versión de Node.js
console.log('Versión de Node.js:', process.version);
console.log('Versión de npm:', process.env.npm_version);

// Información del sistema
console.log('Sistema operativo:', process.platform);
console.log('Arquitectura:', process.arch);
console.log('Directorio actual:', process.cwd());
```

### Ejemplo Intermedio - Primer Script
```javascript
// archivo: hola-node.js
const os = require('os');
const path = require('path');

console.log('¡Hola desde Node.js!');
console.log('===================');
console.log(`Usuario: ${os.userInfo().username}`);
console.log(`Sistema: ${os.type()} ${os.release()}`);
console.log(`Memoria total: ${Math.round(os.totalmem() / 1024 / 1024)} MB`);
console.log(`Directorio home: ${os.homedir()}`);
console.log(`Archivo actual: ${path.basename(__filename)}`);
```

### Ejemplo Avanzado - Servidor HTTP Básico
```javascript
// archivo: servidor-basico.js
const http = require('http');
const url = require('url');

const servidor = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const ruta = parsedUrl.pathname;
    
    // Configurar headers
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    
    // Routing básico
    switch(ruta) {
        case '/':
            res.writeHead(200);
            res.end('<h1>¡Bienvenido a Node.js!</h1><p>Tu instalación funciona correctamente.</p>');
            break;
        case '/info':
            res.writeHead(200);
            res.end(`
                <h2>Información del Servidor</h2>
                <p>Node.js: ${process.version}</p>
                <p>Plataforma: ${process.platform}</p>
                <p>Uptime: ${Math.round(process.uptime())} segundos</p>
            `);
            break;
        default:
            res.writeHead(404);
            res.end('<h1>404 - Página no encontrada</h1>');
    }
});

const puerto = 3000;
servidor.listen(puerto, () => {
    console.log(`Servidor ejecutándose en http://localhost:${puerto}`);
    console.log('Presiona Ctrl+C para detener el servidor');
});
```

## 🖥️ Resultados en Consola

```
// Ejemplo básico
Versión de Node.js: v18.17.0
Versión de npm: 9.6.7
Sistema operativo: win32
Arquitectura: x64
Directorio actual: C:\Users\<USER>\mi-proyecto

// Ejemplo intermedio
¡Hola desde Node.js!
===================
Usuario: Usuario
Sistema: Windows_NT 10.0.22621
Memoria total: 16384 MB
Directorio home: C:\Users\<USER>