# **PROYECTO: E-COMMERCE FRONTEND - IMPLEMENTACIÓN COMPLETA**

## **🛒 DESCRIPCIÓN DEL PROYECTO**

Una aplicación de e-commerce frontend completa con las siguientes características:
- 🏪 Catálogo de productos con filtros avanzados
- 🛒 Carrito de compras funcional
- 🔍 Búsqueda y filtrado inteligente
- 💳 Proceso de checkout completo
- 👤 Gestión de usuarios y perfiles
- 📱 Diseño responsive y moderno
- ⚡ Performance optimizada
- 🎨 UI/UX profesional

## **🏗️ ARQUITECTURA DEL PROYECTO**

```
ecommerce-frontend/
├── index.html
├── pages/
│   ├── products.html
│   ├── cart.html
│   ├── checkout.html
│   └── profile.html
├── css/
│   ├── main.css
│   ├── components.css
│   └── responsive.css
├── js/
│   ├── models/
│   │   ├── Product.js
│   │   ├── Cart.js
│   │   └── User.js
│   ├── services/
│   │   ├── ProductService.js
│   │   ├── CartService.js
│   │   └── APIService.js
│   ├── components/
│   │   ├── ProductCard.js
│   │   ├── CartItem.js
│   │   ├── FilterPanel.js
│   │   └── SearchBar.js
│   ├── utils/
│   │   ├── helpers.js
│   │   └── validators.js
│   └── app.js
├── assets/
│   ├── images/
│   └── icons/
└── data/
    └── products.json
```

## **📄 HTML PRINCIPAL - index.html**

```html
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechStore - Tu Tienda de Tecnología</title>
    <meta name="description" content="Encuentra los mejores productos de tecnología al mejor precio">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <!-- Logo -->
                <div class="logo">
                    <a href="index.html">
                        <i class="fas fa-laptop-code"></i>
                        <span>TechStore</span>
                    </a>
                </div>

                <!-- Navigation -->
                <nav class="nav">
                    <ul class="nav-list">
                        <li><a href="#home" class="nav-link active">Inicio</a></li>
                        <li><a href="#products" class="nav-link">Productos</a></li>
                        <li><a href="#categories" class="nav-link">Categorías</a></li>
                        <li><a href="#deals" class="nav-link">Ofertas</a></li>
                        <li><a href="#contact" class="nav-link">Contacto</a></li>
                    </ul>
                </nav>

                <!-- Search Bar -->
                <div class="search-container">
                    <div class="search-box">
                        <input type="text" id="search-input" placeholder="Buscar productos...">
                        <button class="search-btn" id="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div class="search-suggestions" id="search-suggestions"></div>
                </div>

                <!-- User Actions -->
                <div class="user-actions">
                    <button class="action-btn" id="wishlist-btn" title="Lista de deseos">
                        <i class="fas fa-heart"></i>
                        <span class="badge" id="wishlist-count">0</span>
                    </button>
                    
                    <button class="action-btn" id="cart-btn" title="Carrito de compras">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="badge" id="cart-count">0</span>
                    </button>
                    
                    <div class="user-menu">
                        <button class="action-btn" id="user-btn">
                            <i class="fas fa-user"></i>
                        </button>
                        <div class="dropdown-menu" id="user-dropdown">
                            <a href="#profile">Mi Perfil</a>
                            <a href="#orders">Mis Pedidos</a>
                            <a href="#settings">Configuración</a>
                            <hr>
                            <a href="#logout">Cerrar Sesión</a>
                        </div>
                    </div>
                </div>

                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1>Descubre la Tecnología del Futuro</h1>
                    <p>Los mejores productos de tecnología con la calidad que mereces y los precios más competitivos del mercado.</p>
                    <div class="hero-actions">
                        <button class="btn btn-primary" id="shop-now-btn">
                            Comprar Ahora
                        </button>
                        <button class="btn btn-outline" id="learn-more-btn">
                            Conocer Más
                        </button>
                    </div>
                </div>
                <div class="hero-image">
                    <img src="assets/images/hero-tech.jpg" alt="Tecnología moderna" loading="lazy">
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="categories" id="categories">
        <div class="container">
            <div class="section-header">
                <h2>Explora por Categorías</h2>
                <p>Encuentra exactamente lo que necesitas</p>
            </div>
            
            <div class="categories-grid" id="categories-grid">
                <!-- Categories will be loaded dynamically -->
            </div>
        </div>
    </section>

    <!-- Featured Products -->
    <section class="featured-products" id="products">
        <div class="container">
            <div class="section-header">
                <h2>Productos Destacados</h2>
                <p>Los más vendidos y mejor valorados</p>
            </div>

            <!-- Filter Panel -->
            <div class="filter-panel">
                <div class="filter-group">
                    <label>Categoría:</label>
                    <select id="category-filter">
                        <option value="">Todas las categorías</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>Precio:</label>
                    <div class="price-range">
                        <input type="range" id="price-min" min="0" max="5000" value="0">
                        <input type="range" id="price-max" min="0" max="5000" value="5000">
                        <div class="price-display">
                            <span id="price-min-display">$0</span> - 
                            <span id="price-max-display">$5000</span>
                        </div>
                    </div>
                </div>
                
                <div class="filter-group">
                    <label>Ordenar por:</label>
                    <select id="sort-filter">
                        <option value="featured">Destacados</option>
                        <option value="price-low">Precio: Menor a Mayor</option>
                        <option value="price-high">Precio: Mayor a Menor</option>
                        <option value="rating">Mejor Valorados</option>
                        <option value="newest">Más Recientes</option>
                    </select>
                </div>
                
                <button class="btn btn-secondary" id="clear-filters">
                    Limpiar Filtros
                </button>
            </div>

            <!-- Products Grid -->
            <div class="products-grid" id="products-grid">
                <!-- Products will be loaded dynamically -->
            </div>

            <!-- Load More -->
            <div class="load-more-container">
                <button class="btn btn-outline" id="load-more-btn">
                    Cargar Más Productos
                </button>
            </div>
        </div>
    </section>

    <!-- Deals Section -->
    <section class="deals" id="deals">
        <div class="container">
            <div class="section-header">
                <h2>Ofertas Especiales</h2>
                <p>Aprovecha estos descuentos por tiempo limitado</p>
            </div>
            
            <div class="deals-grid" id="deals-grid">
                <!-- Deals will be loaded dynamically -->
            </div>
        </div>
    </section>

    <!-- Newsletter -->
    <section class="newsletter">
        <div class="container">
            <div class="newsletter-content">
                <div class="newsletter-text">
                    <h3>Mantente al día con las últimas ofertas</h3>
                    <p>Suscríbete a nuestro newsletter y recibe descuentos exclusivos</p>
                </div>
                <form class="newsletter-form" id="newsletter-form">
                    <input type="email" placeholder="Tu email" required>
                    <button type="submit" class="btn btn-primary">Suscribirse</button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>TechStore</h4>
                    <p>Tu tienda de confianza para productos de tecnología de alta calidad.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Productos</h4>
                    <ul>
                        <li><a href="#">Laptops</a></li>
                        <li><a href="#">Smartphones</a></li>
                        <li><a href="#">Tablets</a></li>
                        <li><a href="#">Accesorios</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Soporte</h4>
                    <ul>
                        <li><a href="#">Centro de Ayuda</a></li>
                        <li><a href="#">Contacto</a></li>
                        <li><a href="#">Garantías</a></li>
                        <li><a href="#">Devoluciones</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Empresa</h4>
                    <ul>
                        <li><a href="#">Sobre Nosotros</a></li>
                        <li><a href="#">Carreras</a></li>
                        <li><a href="#">Prensa</a></li>
                        <li><a href="#">Términos</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 TechStore. Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <!-- Cart Sidebar -->
    <div class="cart-sidebar" id="cart-sidebar">
        <div class="cart-header">
            <h3>Carrito de Compras</h3>
            <button class="close-cart" id="close-cart">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="cart-items" id="cart-items">
            <!-- Cart items will be loaded dynamically -->
        </div>
        
        <div class="cart-footer">
            <div class="cart-total">
                <div class="total-line">
                    <span>Subtotal:</span>
                    <span id="cart-subtotal">$0.00</span>
                </div>
                <div class="total-line">
                    <span>Envío:</span>
                    <span id="cart-shipping">$0.00</span>
                </div>
                <div class="total-line total-final">
                    <span>Total:</span>
                    <span id="cart-total">$0.00</span>
                </div>
            </div>
            
            <div class="cart-actions">
                <button class="btn btn-outline" id="view-cart-btn">
                    Ver Carrito
                </button>
                <button class="btn btn-primary" id="checkout-btn">
                    Proceder al Pago
                </button>
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loading-spinner">
        <div class="spinner"></div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toast-container"></div>

    <!-- Scripts -->
    <script type="module" src="js/app.js"></script>
</body>
</html>
```

## **🎨 CSS PRINCIPAL - main.css**

```css
/* Variables CSS */
:root {
    /* Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* Neutrals */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Border radius */
    --radius-sm: 0.25rem;
    --radius: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 150ms ease;
    --transition: 200ms ease;
    --transition-slow: 300ms ease;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1040;
    --z-popover: 1050;
    --z-tooltip: 1060;
}

/* Reset y base */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

@media (min-width: 768px) {
    .container {
        padding: 0 var(--spacing-6);
    }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-4);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-4);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    border: 1px solid transparent;
    border-radius: var(--radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--white);
}

.btn-secondary {
    background-color: var(--gray-100);
    color: var(--gray-700);
    border-color: var(--gray-200);
}

.btn-secondary:hover {
    background-color: var(--gray-200);
    color: var(--gray-800);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* Header */
.header {
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-200);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4) 0;
}

/* Logo */
.logo a {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.logo i {
    font-size: var(--font-size-2xl);
}

/* Navigation */
.nav-list {
    display: flex;
    list-style: none;
    gap: var(--spacing-8);
}

.nav-link {
    font-weight: 500;
    color: var(--gray-600);
    padding: var(--spacing-2) 0;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -var(--spacing-4);
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary-color);
}

/* Search */
.search-container {
    position: relative;
    flex: 1;
    max-width: 400px;
    margin: 0 var(--spacing-8);
}

.search-box {
    position: relative;
    display: flex;
}

.search-box input {
    flex: 1;
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius) 0 0 var(--radius);
    font-size: var(--font-size-sm);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-btn {
    padding: var(--spacing-3) var(--spacing-4);
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 0 var(--radius) var(--radius) 0;
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background-color: var(--primary-dark);
}

/* User Actions */
.user-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.action-btn {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: transparent;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition);
}

.action-btn:hover {
    background-color: var(--gray-100);
    color: var(--gray-800);
}

.badge {
    position: absolute;
    top: -8px;
    right: -8px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    background-color: var(--error-color);
    color: var(--white);
    font-size: var(--font-size-xs);
    font-weight: 600;
    border-radius: 10px;
    padding: 0 var(--spacing-1);
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-2);
}

.mobile-menu-toggle span {
    width: 24px;
    height: 2px;
    background-color: var(--gray-600);
    transition: var(--transition);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    padding: var(--spacing-16) 0;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-12);
    align-items: center;
}

.hero-text h1 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
}

.hero-text p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin-bottom: var(--spacing-8);
}

.hero-actions {
    display: flex;
    gap: var(--spacing-4);
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
}

/* Responsive */
@media (max-width: 768px) {
    .nav {
        display: none;
    }
    
    .search-container {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-text h1 {
        font-size: var(--font-size-3xl);
    }
    
    .hero-actions {
        justify-content: center;
    }
}
```

Este proyecto de E-commerce Frontend demuestra una implementación profesional con arquitectura moderna, diseño responsive y mejores prácticas de desarrollo web.
