# 5.1.1 - Conceptos básicos de variables

### Introducción

Las variables en JavaScript son contenedores que almacenan valores de datos.
Son fundamentales para cualquier programa, permitiendo guardar y manipular información.
Una variable actúa como una etiqueta que referencia una ubicación en memoria.
Pueden contener diferentes tipos de datos: números, strings, objetos, funciones.
Su valor puede cambiar durante la ejecución del programa (excepto const).
¿Comprendes qué es una variable y por qué es esencial en programación?

### Código de Ejemplo
```javascript
// Conceptos básicos de variables

// 1. Declaración de variables
let nombre; // Variable declarada pero no inicializada
var edad; // Variable con var (legacy)
const PI = 3.14159; // Constante, debe inicializarse

// 2. Inicialización
nombre = "Juan"; // Asignación de valor
edad = 25;

// 3. Declaración e inicialización en una línea
let apellido = "Pérez";
let activo = true;
let salario = 50000.50;

// 4. Variables como contenedores
let contenedor1 = "Texto";
let contenedor2 = 42;
let contenedor3 = { nombre: "Ana", edad: 30 };
let contenedor4 = [1, 2, 3, 4, 5];
let contenedor5 = function() { return "Hola"; };

// 5. Reasignación de variables
let variable = "Valor inicial";
console.log("Antes:", variable);
variable = "Nuevo valor";
console.log("Después:", variable);

// 6. Variables como referencias
let objeto1 = { valor: 10 };
let objeto2 = objeto1; // Ambas variables apuntan al mismo objeto
objeto2.valor = 20;
console.log("objeto1.valor:", objeto1.valor); // 20
console.log("objeto2.valor:", objeto2.valor); // 20

// 7. Tipos de datos en variables
let numero = 42;
let texto = "Hola mundo";
let booleano = true;
let indefinido = undefined;
let nulo = null;
let simbolo = Symbol("id");
let bigint = 123456789012345678901234567890n;

// 8. Variables y memoria
let primitivo = 5;
let referencia = { id: 1 };

// Mostrar información de las variables
console.log("=== INFORMACIÓN DE VARIABLES ===");
console.log("nombre:", nombre, "- tipo:", typeof nombre);
console.log("edad:", edad, "- tipo:", typeof edad);
console.log("PI:", PI, "- tipo:", typeof PI);
console.log("apellido:", apellido, "- tipo:", typeof apellido);
console.log("activo:", activo, "- tipo:", typeof activo);
console.log("salario:", salario, "- tipo:", typeof salario);
console.log("contenedor3:", contenedor3, "- tipo:", typeof contenedor3);
console.log("contenedor4:", contenedor4, "- tipo:", typeof contenedor4);
console.log("contenedor5:", contenedor5, "- tipo:", typeof contenedor5);
console.log("simbolo:", simbolo, "- tipo:", typeof simbolo);
console.log("bigint:", bigint, "- tipo:", typeof bigint);
```

### Salida en Consola
```
Antes: Valor inicial
Después: Nuevo valor
objeto1.valor: 20
objeto2.valor: 20
=== INFORMACIÓN DE VARIABLES ===
nombre: Juan - tipo: string
edad: 25 - tipo: number
PI: 3.14159 - tipo: number
apellido: Pérez - tipo: string
activo: true - tipo: boolean
salario: 50000.5 - tipo: number
contenedor3: { nombre: 'Ana', edad: 30 } - tipo: object
contenedor4: [ 1, 2, 3, 4, 5 ] - tipo: object
contenedor5: [Function: contenedor5] - tipo: function
simbolo: Symbol(id) - tipo: symbol
bigint: 123456789012345678901234567890n - tipo: bigint
```

### Diagrama de Flujo del Código
```mermaid
flowchart TD
    A[Inicio del Programa] --> B[Declaración de Variables]
    B --> C[let nombre - sin inicializar]
    B --> D[var edad - sin inicializar]
    B --> E[const PI = 3.14159]
    
    C --> F[Inicialización: nombre = 'Juan']
    D --> G[Inicialización: edad = 25]
    
    F --> H[Declaración e Inicialización Directa]
    G --> H
    E --> H
    
    H --> I[let apellido = 'Pérez']
    H --> J[Variables como Contenedores]
    
    J --> K[Contenedor de String]
    J --> L[Contenedor de Number]
    J --> M[Contenedor de Object]
    J --> N[Contenedor de Array]
    J --> O[Contenedor de Function]
    
    K --> P[Reasignación de Variables]
    L --> P
    M --> P
    N --> P
    O --> P
    
    P --> Q[variable = 'Valor inicial']
    Q --> R[variable = 'Nuevo valor']
    
    R --> S[Variables como Referencias]
    S --> T[objeto1 = {valor: 10}]
    T --> U[objeto2 = objeto1]
    U --> V[objeto2.valor = 20]
    
    V --> W[Tipos de Datos]
    W --> X[Primitivos: number, string, boolean]
    W --> Y[Especiales: undefined, null, symbol, bigint]
    
    X --> Z[Mostrar Información]
    Y --> Z
    Z --> AA[Fin del Programa]
    
    style A fill:#4CAF50,stroke:#000,stroke-width:3px,color:#fff
    style E fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff
    style P fill:#FFD700,stroke:#000,stroke-width:3px,color:#000
    style S fill:#87CEEB,stroke:#000,stroke-width:3px,color:#000
    style AA fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph "Variable como Contenedor"
        A1[Variable: nombre]
        A2[Valor: 'Juan']
        A3[Tipo: string]
        A4[Memoria: 0x001]
    end
    
    subgraph "Tipos de Variables"
        B1[Primitivas]
        B2[Referencia]
        B3[number, string, boolean]
        B4[object, array, function]
    end
    
    subgraph "Estados de Variable"
        C1[Declarada]
        C2[Inicializada]
        C3[Reasignada]
        C4[undefined → valor → nuevo valor]
    end
    
    subgraph "Características"
        D1[Mutabilidad]
        D2[Scope]
        D3[Lifetime]
        D4[let/const: mutable/immutable]
    end
    
    A1 --> A2
    A2 --> A3
    A3 --> A4
    
    B1 --> B3
    B2 --> B4
    
    C1 --> C2
    C2 --> C3
    C3 --> C4
    
    D1 --> D4
    D2 --> D4
    D3 --> D4
    
    style A1 fill:#98FB98,stroke:#000,stroke-width:2px
    style B1 fill:#FFB6C1,stroke:#000,stroke-width:2px
    style B2 fill:#87CEEB,stroke:#000,stroke-width:2px
    style C1 fill:#FFD700,stroke:#000,stroke-width:2px
    style D1 fill:#DDA0DD,stroke:#000,stroke-width:2px
```

### Casos de uso
1. **Almacenamiento de datos de usuario**: Guardar nombre, edad, email en variables.
2. **Contadores y acumuladores**: Variables para loops y cálculos iterativos.
3. **Configuración de aplicación**: Variables para settings y parámetros.
4. **Estado de la aplicación**: Variables que mantienen el estado actual.
5. **Resultados de cálculos**: Almacenar resultados de operaciones matemáticas.
6. **Referencias a elementos DOM**: Variables que apuntan a elementos HTML.
7. **Datos temporales**: Variables para procesamiento intermedio.
8. **Flags y banderas**: Variables booleanas para control de flujo.
9. **Cacheo de valores**: Variables para evitar recálculos costosos.
10. **Comunicación entre funciones**: Variables para pasar datos entre funciones.

### Errores comunes
1. **No inicializar variables**: Usar variables sin asignar valor inicial.
2. **Confundir declaración con inicialización**: No entender la diferencia.
3. **Reasignar constantes**: Intentar cambiar el valor de const.
4. **Nombres de variables confusos**: Usar nombres no descriptivos.
5. **Mezclar tipos sin control**: Cambiar tipos de datos inesperadamente.
6. **Variables globales innecesarias**: Contaminar el scope global.
7. **No entender referencias**: Confundir copia de valor con referencia.
8. **Usar var en lugar de let/const**: Usar sintaxis obsoleta.
9. **Variables no utilizadas**: Declarar variables que nunca se usan.
10. **Scope confusion**: No entender dónde está disponible la variable.

### Recomendaciones
1. **Usa nombres descriptivos**: Las variables deben explicar su propósito.
2. **Inicializa siempre que sea posible**: Evita variables undefined.
3. **Prefiere const por defecto**: Usa let solo cuando necesites reasignar.
4. **Evita var completamente**: Usa let/const en código moderno.
5. **Agrupa variables relacionadas**: Organiza declaraciones lógicamente.
6. **Documenta variables complejas**: Añade comentarios explicativos.
7. **Usa tipos consistentes**: Evita cambios de tipo inesperados.
8. **Minimiza el scope**: Declara variables en el scope más pequeño posible.
9. **Valida tipos cuando sea necesario**: Usa typeof para verificaciones.
10. **Limpia variables no utilizadas**: Elimina código muerto regularmente.

---

**Siguiente**: [5.1.2 - Inicialización vs declaración](./5.1.2%20-%20Inicialización%20vs%20declaración.md)  
**Anterior**: [5.1 - Declaración de Variables - Fundamentos](../5.1%20-%20Declaración%20de%20Variables%20-%20Fundamentos.md)  
**Índice**: [Variables y Declaraciones - Maestría Completa](../README.md)