/**
 * PLATAFORMA DE APRENDIZAJE JAVASCRIPT
 * ====================================
 * 
 * Script principal para la interactividad de la plataforma
 */

// Estado global de la aplicación
const AppState = {
    user: null,
    currentCourse: null,
    progress: {},
    preferences: {
        theme: 'dark',
        language: 'es'
    }
};

// Utilidades
const Utils = {
    // Selector de elementos
    $(selector) {
        return document.querySelector(selector);
    },

    // Selector múltiple
    $$(selector) {
        return document.querySelectorAll(selector);
    },

    // Añadir event listener
    on(element, event, handler) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.addEventListener(event, handler);
        }
    },

    // Crear elemento
    createElement(tag, className, content) {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (content) element.textContent = content;
        return element;
    },

    // Animación de scroll suave
    smoothScroll(target) {
        const element = typeof target === 'string' ? this.$(target) : target;
        if (element) {
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    },

    // Formatear tiempo
    formatTime(hours) {
        if (hours < 1) {
            return `${Math.round(hours * 60)} min`;
        }
        return `${hours}h`;
    },

    // Guardar en localStorage
    saveToStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (error) {
            console.warn('No se pudo guardar en localStorage:', error);
        }
    },

    // Cargar de localStorage
    loadFromStorage(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.warn('No se pudo cargar de localStorage:', error);
            return null;
        }
    }
};

// Gestión de navegación
const Navigation = {
    init() {
        this.setupSmoothScrolling();
        this.setupActiveNavigation();
        this.setupMobileMenu();
    },

    setupSmoothScrolling() {
        Utils.$$('a[href^="#"]').forEach(link => {
            Utils.on(link, 'click', (e) => {
                e.preventDefault();
                const target = link.getAttribute('href');
                if (target !== '#') {
                    Utils.smoothScroll(target);
                }
            });
        });
    },

    setupActiveNavigation() {
        const navLinks = Utils.$$('.nav-link');
        const sections = Utils.$$('section[id]');

        window.addEventListener('scroll', () => {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (window.pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${current}`) {
                    link.classList.add('active');
                }
            });
        });
    },

    setupMobileMenu() {
        // TODO: Implementar menú móvil
        console.log('Mobile menu setup - TODO');
    }
};

// Gestión de progreso
const ProgressManager = {
    init() {
        this.loadProgress();
        this.setupProgressTracking();
    },

    loadProgress() {
        const savedProgress = Utils.loadFromStorage('courseProgress');
        if (savedProgress) {
            AppState.progress = savedProgress;
            this.updateProgressDisplay();
        }
    },

    saveProgress() {
        Utils.saveToStorage('courseProgress', AppState.progress);
    },

    updatePartProgress(partId, progress) {
        AppState.progress[partId] = progress;
        this.saveProgress();
        this.updateProgressDisplay();
    },

    updateProgressDisplay() {
        Utils.$$('.part-card').forEach(card => {
            const partId = card.dataset.part;
            const progress = AppState.progress[partId] || 0;
            
            const progressFill = card.querySelector('.progress-fill');
            const progressText = card.querySelector('.progress-text');
            
            if (progressFill && progressText) {
                progressFill.style.width = `${progress}%`;
                progressText.textContent = `${progress}% completado`;
            }
        });
    },

    setupProgressTracking() {
        // Simular progreso para demo
        setTimeout(() => {
            this.updatePartProgress('1', 15);
            this.updatePartProgress('2', 8);
            this.updatePartProgress('3', 3);
        }, 2000);
    }
};

// Gestión de cursos
const CourseManager = {
    init() {
        this.setupCourseInteractions();
        this.setupLearningPaths();
    },

    setupCourseInteractions() {
        // Click en tarjetas de partes
        Utils.$$('.part-card').forEach(card => {
            Utils.on(card, 'click', () => {
                const partId = card.dataset.part;
                this.openPart(partId);
            });
        });

        // Botón de comenzar curso
        Utils.on('#startCourseBtn', 'click', () => {
            this.startCourse();
        });

        // Botón de vista previa
        Utils.on('#previewBtn', 'click', () => {
            this.showPreview();
        });

        // Ver todas las partes
        Utils.on('#viewAllPartsBtn', 'click', () => {
            this.showAllParts();
        });
    },

    setupLearningPaths() {
        Utils.$$('.path-card button').forEach(button => {
            Utils.on(button, 'click', (e) => {
                const pathCard = e.target.closest('.path-card');
                const pathName = pathCard.querySelector('h3').textContent;
                this.selectLearningPath(pathName);
            });
        });
    },

    openPart(partId) {
        console.log(`Abriendo parte ${partId}`);
        // TODO: Implementar navegación a la parte específica
        this.showModal(`Parte ${partId}`, `Contenido de la parte ${partId} se cargará aquí.`);
    },

    startCourse() {
        console.log('Iniciando curso');
        // TODO: Implementar inicio de curso
        this.showModal('¡Bienvenido!', 'Tu viaje de aprendizaje está a punto de comenzar. Selecciona una ruta de aprendizaje para continuar.');
    },

    showPreview() {
        console.log('Mostrando vista previa');
        // TODO: Implementar vista previa
        this.showModal('Vista Previa', 'Aquí podrás ver una muestra del contenido del curso.');
    },

    showAllParts() {
        console.log('Mostrando todas las partes');
        // TODO: Implementar vista de todas las partes
        Utils.smoothScroll('#curso');
    },

    selectLearningPath(pathName) {
        console.log(`Ruta seleccionada: ${pathName}`);
        // TODO: Implementar selección de ruta
        this.showModal('Ruta Seleccionada', `Has seleccionado la ruta: ${pathName}. ¡Excelente elección!`);
    },

    showModal(title, content) {
        // Modal simple para demostraciones
        const modal = Utils.createElement('div', 'modal-overlay');
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <p>${content}</p>
                </div>
                <div class="modal-footer">
                    <button class="btn-primary modal-ok">Entendido</button>
                </div>
            </div>
        `;

        // Estilos del modal
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;

        const modalContent = modal.querySelector('.modal-content');
        modalContent.style.cssText = `
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 12px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            color: #fff;
        `;

        document.body.appendChild(modal);

        // Cerrar modal
        const closeModal = () => {
            document.body.removeChild(modal);
        };

        Utils.on(modal.querySelector('.modal-close'), 'click', closeModal);
        Utils.on(modal.querySelector('.modal-ok'), 'click', closeModal);
        Utils.on(modal, 'click', (e) => {
            if (e.target === modal) closeModal();
        });
    }
};

// Gestión de autenticación
const AuthManager = {
    init() {
        this.setupAuthButtons();
        this.checkAuthState();
    },

    setupAuthButtons() {
        Utils.on('#loginBtn', 'click', () => {
            this.showLoginModal();
        });
    },

    checkAuthState() {
        const savedUser = Utils.loadFromStorage('user');
        if (savedUser) {
            this.setUser(savedUser);
        }
    },

    setUser(user) {
        AppState.user = user;
        this.updateUI();
    },

    updateUI() {
        const loginBtn = Utils.$('#loginBtn');
        if (AppState.user) {
            loginBtn.textContent = AppState.user.name;
            loginBtn.onclick = () => this.showUserMenu();
        } else {
            loginBtn.textContent = 'Iniciar Sesión';
        }
    },

    showLoginModal() {
        CourseManager.showModal('Iniciar Sesión', 'Funcionalidad de login en desarrollo. Por ahora puedes explorar el contenido libremente.');
    },

    showUserMenu() {
        CourseManager.showModal('Menú de Usuario', 'Perfil, configuración y progreso del usuario.');
    }
};

// Efectos visuales y animaciones
const VisualEffects = {
    init() {
        this.setupScrollAnimations();
        this.setupHoverEffects();
        this.setupCodeAnimation();
    },

    setupScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observar elementos para animación
        Utils.$$('.part-card, .path-card, .feature-card').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
    },

    setupHoverEffects() {
        // Efecto de partículas en hover (simplificado)
        Utils.$$('.part-card').forEach(card => {
            Utils.on(card, 'mouseenter', () => {
                card.style.boxShadow = '0 10px 30px rgba(247, 223, 30, 0.2)';
            });

            Utils.on(card, 'mouseleave', () => {
                card.style.boxShadow = '';
            });
        });
    },

    setupCodeAnimation() {
        // Animación de escritura de código
        const codeElement = Utils.$('pre code');
        if (codeElement) {
            const originalText = codeElement.textContent;
            codeElement.textContent = '';
            
            let i = 0;
            const typeWriter = () => {
                if (i < originalText.length) {
                    codeElement.textContent += originalText.charAt(i);
                    i++;
                    setTimeout(typeWriter, 20);
                }
            };

            // Iniciar animación después de un delay
            setTimeout(typeWriter, 1000);
        }
    }
};

// Analytics y tracking
const Analytics = {
    init() {
        this.setupEventTracking();
        this.trackPageView();
    },

    setupEventTracking() {
        // Track clicks en elementos importantes
        Utils.$$('button, .part-card, .path-card').forEach(element => {
            Utils.on(element, 'click', (e) => {
                this.trackEvent('click', {
                    element: e.target.tagName,
                    className: e.target.className,
                    text: e.target.textContent?.substring(0, 50)
                });
            });
        });
    },

    trackPageView() {
        this.trackEvent('page_view', {
            url: window.location.href,
            timestamp: new Date().toISOString()
        });
    },

    trackEvent(eventName, data) {
        console.log(`Analytics: ${eventName}`, data);
        // TODO: Enviar a servicio de analytics real
    }
};

// Inicialización de la aplicación
class App {
    constructor() {
        this.init();
    }

    init() {
        // Esperar a que el DOM esté listo
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.start());
        } else {
            this.start();
        }
    }

    start() {
        console.log('🚀 Iniciando Plataforma de JavaScript Maestría');

        // Inicializar módulos
        Navigation.init();
        ProgressManager.init();
        CourseManager.init();
        AuthManager.init();
        VisualEffects.init();
        Analytics.init();

        // Configurar manejo de errores
        this.setupErrorHandling();

        console.log('✅ Plataforma inicializada correctamente');
    }

    setupErrorHandling() {
        window.addEventListener('error', (e) => {
            console.error('Error en la aplicación:', e.error);
            Analytics.trackEvent('error', {
                message: e.error?.message,
                filename: e.filename,
                lineno: e.lineno
            });
        });

        window.addEventListener('unhandledrejection', (e) => {
            console.error('Promise rechazada:', e.reason);
            Analytics.trackEvent('unhandled_rejection', {
                reason: e.reason?.toString()
            });
        });
    }
}

// Inicializar aplicación
new App();

// Exportar para uso global si es necesario
window.JavaScriptMaestria = {
    AppState,
    Utils,
    Navigation,
    ProgressManager,
    CourseManager,
    AuthManager,
    VisualEffects,
    Analytics
};
