# **CURSO COMPLETO DE JAVASCRIPT - ÍNDICE MAESTRO**

## **ESTRUCTURA GENERAL DEL CURSO**

### **PARTE I: FUNDAMENTOS BÁSICOS** *(Capítulos 1-14)*
- Introducción a JavaScript
- Configuración del Entorno de Desarrollo
- Primeros Pasos en JavaScript
- Sintaxis Básica
- Variables y Declaraciones
- Tipos de Datos Primitivos
- Conversión de Tipos
- Operadores Básicos
- Operadores de Comparación y Lógicos
- Condicionales
- Bucles Básicos
- Bucles Avanzados
- Control de Flujo
- Manejo de Errores Básico

### **PARTE II: ESTRUCTURAS DE DATOS** *(Capítulos 15-29)*
- Arrays - Fundamentos
- Arrays - Métodos Básicos
- Arrays - Métodos Avanzados
- Arrays - Iteración y Transformación
- Arrays - Performance y Optimización
- Objetos - Fundamentos
- Objetos - Propiedades y Métodos
- Objetos - Prototipos y Herencia
- Objetos - Métodos Avanzados
- Maps y WeakMaps
- Sets y WeakSets
- Strings - Métodos Avanzados
- Números y Math
- Fechas y Tiempo
- Estructuras de Datos Avanzadas

### **PARTE III: FUNCIONES** *(Capítulos 30-42)*
- Introducción a las Funciones
- Declaración y Expresiones de Función
- Arrow Functions
- Parámetros y Argumentos
- Scope y Closures
- Hoisting y Temporal Dead Zone
- Funciones de Orden Superior
- Callbacks y Programación Asíncrona
- Recursión
- Funciones Generadoras
- Async/Await
- Programación Funcional
- Optimización de Funciones

### **PARTE IV: PROGRAMACIÓN ORIENTADA A OBJETOS** *(Capítulos 43-55)*
- Introducción a OOP en JavaScript
- Objetos y Prototipos
- Clases ES6+
- Herencia y Polimorfismo
- Encapsulación y Abstracción
- Métodos Estáticos y de Instancia
- Getters y Setters
- Símbolos y Propiedades Privadas
- Mixins y Composición
- Patrones de Diseño OOP
- Testing en OOP
- Performance en OOP
- Migración de Prototipos a Clases

### **PARTE V: PROGRAMACIÓN ASÍNCRONA** *(Capítulos 56-68)*
- Introducción a la Programación Asíncrona
- Event Loop y Call Stack
- Callbacks y Callback Hell
- Promises - Fundamentos
- Promises - Métodos Avanzados
- Async/Await - Fundamentos
- Async/Await - Patrones Avanzados
- Manejo de Errores Asíncronos
- Concurrencia y Paralelismo
- Web Workers
- Service Workers
- Streams y Observables
- Performance Asíncrona

### **PARTE VI: DOM Y EVENTOS** *(Capítulos 69-81)*
- Introducción al DOM
- Selección y Manipulación de Elementos
- Creación y Modificación de Elementos
- Estilos y CSS desde JavaScript
- Eventos - Fundamentos
- Eventos - Tipos y Manejo
- Event Delegation y Bubbling
- Formularios y Validación
- Animaciones con JavaScript
- Intersection Observer
- Mutation Observer
- Performance en DOM
- Accesibilidad y ARIA

### **PARTE VII: APIS DEL NAVEGADOR** *(Capítulos 82-94)*
- Fetch API y AJAX
- Local Storage y Session Storage
- IndexedDB
- Geolocation API
- File API
- Canvas API
- WebGL Básico
- Audio y Video APIs
- Notifications API
- History API
- Clipboard API
- Performance APIs
- Security APIs

### **PARTE VIII: MÓDULOS Y BUNDLING** *(Capítulos 95-107)*
- Módulos ES6
- CommonJS y AMD
- Webpack - Configuración
- Webpack - Optimización
- Vite y Herramientas Modernas
- Rollup y Parcel
- Tree Shaking
- Code Splitting
- Module Federation
- Micro Frontends
- Package Management
- Monorepos
- Build Optimization

### **PARTE IX: TESTING** *(Capítulos 108-120)*
- Introducción al Testing
- Unit Testing con Jest
- Integration Testing
- End-to-End Testing
- Test-Driven Development
- Behavior-Driven Development
- Mocking y Stubbing
- Testing Asíncrono
- Performance Testing
- Visual Regression Testing
- Accessibility Testing
- Cross-Browser Testing
- CI/CD para Testing

### **PARTE X: PERFORMANCE Y OPTIMIZACIÓN** *(Capítulos 121-133)*
- Fundamentos de Performance
- Memory Management
- Garbage Collection
- Profiling y Debugging
- Optimización de Algoritmos
- Lazy Loading
- Caching Strategies
- Bundle Optimization
- Runtime Performance
- Network Optimization
- Core Web Vitals
- Performance Monitoring
- Advanced Optimization

### **PARTE XI: SEGURIDAD** *(Capítulos 134-146)*
- Fundamentos de Seguridad Web
- XSS Prevention
- CSRF Protection
- Content Security Policy
- HTTPS y TLS
- Authentication y Authorization
- JWT y Session Management
- Input Validation
- Secure Coding Practices
- OWASP Top 10
- Security Headers
- Penetration Testing
- Security Monitoring

### **PARTE XII: FRAMEWORKS Y LIBRERÍAS** *(Capítulos 147-159)*
- Introducción a Frameworks
- React - Fundamentos
- React - Avanzado
- Vue.js - Fundamentos
- Vue.js - Avanzado
- Angular - Fundamentos
- Angular - Avanzado
- Svelte y SvelteKit
- Node.js - Fundamentos
- Express.js
- Database Integration
- API Development
- Full-Stack Development

### **PARTE XIII: HERRAMIENTAS DE DESARROLLO** *(Capítulos 160-172)*
- Git y Control de Versiones
- NPM y Package Management
- Linting y Formatting
- TypeScript Fundamentals
- TypeScript Advanced
- Debugging Tools
- Browser DevTools
- VS Code y Extensions
- Docker para JavaScript
- CI/CD Pipelines
- Deployment Strategies
- Monitoring y Logging
- Development Workflows

### **PARTE XIV: PROYECTOS PRÁCTICOS** *(Capítulos 173-185)*
- Proyecto: Todo App Avanzada
- Proyecto: E-commerce Frontend
- Proyecto: Chat Application
- Proyecto: Data Visualization
- Proyecto: Game Development
- Proyecto: Progressive Web App
- Proyecto: API REST Completa
- Proyecto: Real-time Dashboard
- Proyecto: Mobile App con JS
- Proyecto: Desktop App con Electron
- Proyecto: Chrome Extension
- Proyecto: Microservices
- Portfolio y Deployment

### **PARTE XV: JAVASCRIPT AVANZADO** *(Capítulos 186-200)*
- Metaprogramming
- Proxies y Reflect
- Symbols Avanzados
- WeakRefs y FinalizationRegistry
- Temporal API
- Pattern Matching
- Decorators
- Private Fields
- Top-level Await
- Import Maps
- Dynamic Imports
- WebAssembly Integration
- Performance APIs Avanzadas
- Future JavaScript Features
- Migración y Modernización

## **RECURSOS ADICIONALES**
- Glosario de Términos
- Cheat Sheets
- Best Practices Guide
- Common Patterns
- Troubleshooting Guide
- Performance Checklist
- Security Checklist
- Accessibility Guide
- Browser Compatibility
- Learning Resources
