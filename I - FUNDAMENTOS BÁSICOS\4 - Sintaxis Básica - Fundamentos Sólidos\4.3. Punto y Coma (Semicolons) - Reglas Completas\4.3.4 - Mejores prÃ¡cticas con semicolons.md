## 4.3.4. Mejores prácticas con semicolons

### Introducción
Usar semicolons explícitamente previene errores ASI.  
Mejora legibilidad y consistencia.  
Sigue estándares de estilo.  
Facilita minificación.  
Evita bugs sutiles.  
¿Adoptas un estilo consistente en tu equipo?

### Código de Ejemplo
```javascript
// Con semicolons
let a = 1;
let b = 2;
console.log(a + b);

function returnValue() {
  return 42;
}
console.log(returnValue());

// Sin semicolons (riesgoso)
let c = 1
d = 2  // ASI inserta ;
console.log(c + d)

// Mejora con ;
let e = 1;
[2].forEach(x => console.log(x));
```
### Resultados en Consola
- `3`
- `42`
- `3`
- `2`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> WithSemi["Paso 1: Declaraciones con ; <br> - Se<PERSON><PERSON>"]
    WithSemi --> LogSum["Paso 2: console.log(a + b) <br> - 3"]
    LogSum --> ReturnSemi["Paso 3: return 42; <br> - Correcto"]
    ReturnSemi --> LogReturn["Paso 4: console.log(returnValue()) <br> - 42"]
    LogReturn --> WithoutSemi["Paso 5: c =1 d=2 <br> - ASI trabaja"]
    WithoutSemi --> LogCD["Paso 6: console.log(c + d) <br> - 3"]
    LogCD --> Improved["Paso 7: e=1; [2].forEach <br> - Seguro con ;"]
    Improved --> LogArray["Paso 8: console.log(x) <br> - 2"]
    LogArray --> Fin["Paso 9: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Consistent["Estilo consistente"]
    WithoutSemi --> NotaRisk["Nota: Riesgoso sin ;"]
    Improved --> NotaSafe["Nota: Previene errores"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style WithSemi fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogSum fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ReturnSemi fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogReturn fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style WithoutSemi fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogCD fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Improved fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogArray fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Consistent fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaRisk fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaSafe fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> ChooseStyle["Elegir estilo <br> - Con o sin ;"]
        ChooseStyle --> With["Con ; <br> - Explícito"]
        ChooseStyle --> Without["Sin ; <br> - Depende ASI"]
        With --> Benefits["Beneficios <br> - Seguro, legible"]
        Without --> Risks["Riesgos <br> - Bugs, inconsistencia"]
        Benefits --> Team["Equipo <br> - Consistencia"]
        Risks --> Linter["Linter <br> - Enforce rules"]
        Team --> BestPractice["Mejor práctica <br> - Usar ;"]
        Linter --> BestPractice
        Inicio --> Desarrollador(("Desarrollador")) --> Adopt["Adoptar guías de estilo"]
        With --> NotaWith["Nota: Previene ASI fails"]
        Without --> NotaWithout["Nota: Requiere cuidado"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ChooseStyle fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style With fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Without fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Benefits fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Risks fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Team fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Linter fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style BestPractice fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Adopt fill:#32CD32,stroke:#000,stroke-width3px,color:#fff,font-weight:bold,font-size:14px
    style NotaWith fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaWithout fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Proyectos grandes con equipo.
2. Código minificado.
3. Entornos con diferentes parsers.
4. Código legacy maintenance.
5. Enseñanza y onboarding.

### Errores comunes
1. Inconsistencia en uso de ;.
2. Depender de ASI.
3. Ignorar linter warnings.
4. No documentar estilo.
5. Mezclar estilos en archivos.

### Recomendaciones
1. Usa semicolons siempre.
2. Configura ESLint/Prettier.
3. Adopta guía de estilo (Airbnb, Google).
4. Educa al equipo.
5. Automatiza con hooks.