<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulador de Código JavaScript - Maestría</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #fff;
            height: 100vh;
            overflow: hidden;
        }

        .simulator-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 60px 1fr 200px;
            height: 100vh;
            gap: 1px;
            background: #333;
        }

        .header {
            grid-column: 1 / -1;
            background: #2d2d2d;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1rem;
            border-bottom: 1px solid #444;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #f7df1e;
            font-weight: bold;
        }

        .controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            background: #f7df1e;
            color: #000;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn:hover {
            background: #ffeb3b;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: transparent;
            color: #f7df1e;
            border: 1px solid #f7df1e;
        }

        .btn-secondary:hover {
            background: #f7df1e;
            color: #000;
        }

        .editor-panel {
            background: #1e1e1e;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            background: #2d2d2d;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #444;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .output-panel {
            background: #1a1a1a;
            display: flex;
            flex-direction: column;
        }

        .editor-container {
            flex: 1;
            position: relative;
        }

        .CodeMirror {
            height: 100% !important;
            font-size: 14px;
            line-height: 1.5;
        }

        .output-container {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }

        .console-panel {
            grid-column: 1 / -1;
            background: #0d1117;
            display: flex;
            flex-direction: column;
        }

        .console-output {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }

        .console-input {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            background: #161b22;
            border-top: 1px solid #30363d;
        }

        .console-prompt {
            color: #f7df1e;
            margin-right: 0.5rem;
            font-family: 'Courier New', monospace;
        }

        .console-input input {
            flex: 1;
            background: transparent;
            border: none;
            color: #fff;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            outline: none;
        }

        .log-entry {
            margin-bottom: 0.25rem;
            padding: 0.25rem 0;
        }

        .log-info {
            color: #58a6ff;
        }

        .log-warn {
            color: #f85149;
        }

        .log-error {
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            padding: 0.5rem;
            border-radius: 4px;
            border-left: 3px solid #ff6b6b;
        }

        .log-success {
            color: #56d364;
        }

        .examples-dropdown {
            position: relative;
        }

        .examples-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: #2d2d2d;
            border: 1px solid #444;
            border-radius: 4px;
            min-width: 200px;
            z-index: 1000;
            display: none;
        }

        .examples-menu.show {
            display: block;
        }

        .example-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid #444;
            transition: background 0.2s;
        }

        .example-item:hover {
            background: #3d3d3d;
        }

        .example-item:last-child {
            border-bottom: none;
        }

        .example-title {
            font-weight: 600;
            color: #f7df1e;
        }

        .example-description {
            font-size: 0.85rem;
            color: #ccc;
            margin-top: 0.25rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #56d364;
        }

        .status-dot.error {
            background: #ff6b6b;
        }

        .resize-handle {
            width: 4px;
            background: #444;
            cursor: col-resize;
            position: relative;
        }

        .resize-handle:hover {
            background: #f7df1e;
        }

        @media (max-width: 768px) {
            .simulator-container {
                grid-template-columns: 1fr;
                grid-template-rows: 60px 1fr 1fr 150px;
            }
        }
    </style>
</head>
<body>
    <div class="simulator-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <span>🚀</span>
                <span>JavaScript Simulator</span>
            </div>
            <div class="controls">
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">Listo</span>
                </div>
                <div class="examples-dropdown">
                    <button class="btn-secondary" id="examplesBtn">Ejemplos</button>
                    <div class="examples-menu" id="examplesMenu">
                        <div class="example-item" data-example="variables">
                            <div class="example-title">Variables y Tipos</div>
                            <div class="example-description">Declaración y uso de variables</div>
                        </div>
                        <div class="example-item" data-example="functions">
                            <div class="example-title">Funciones</div>
                            <div class="example-description">Declaración y llamada de funciones</div>
                        </div>
                        <div class="example-item" data-example="arrays">
                            <div class="example-title">Arrays</div>
                            <div class="example-description">Manipulación de arrays</div>
                        </div>
                        <div class="example-item" data-example="objects">
                            <div class="example-title">Objetos</div>
                            <div class="example-description">Creación y uso de objetos</div>
                        </div>
                        <div class="example-item" data-example="async">
                            <div class="example-title">Async/Await</div>
                            <div class="example-description">Programación asíncrona</div>
                        </div>
                        <div class="example-item" data-example="classes">
                            <div class="example-title">Clases ES6</div>
                            <div class="example-description">Programación orientada a objetos</div>
                        </div>
                    </div>
                </div>
                <button class="btn" id="runBtn">▶ Ejecutar</button>
                <button class="btn-secondary" id="clearBtn">🗑 Limpiar</button>
                <button class="btn-secondary" id="shareBtn">📤 Compartir</button>
            </div>
        </div>

        <!-- Editor Panel -->
        <div class="editor-panel">
            <div class="panel-header">
                <span>📝 Editor de Código</span>
                <span class="status-indicator">
                    <span id="lineCount">Líneas: 1</span>
                </span>
            </div>
            <div class="editor-container">
                <textarea id="codeEditor">// ¡Bienvenido al Simulador de JavaScript!
// Escribe tu código aquí y presiona "Ejecutar" para ver los resultados

console.log('¡Hola, JavaScript!');

// Ejemplo: Variables y tipos
let nombre = 'JavaScript';
let año = 2024;
let esGenial = true;

console.log(`${nombre} en ${año} es genial: ${esGenial}`);

// Ejemplo: Función simple
function saludar(nombre) {
    return `¡Hola, ${nombre}!`;
}

console.log(saludar('Desarrollador'));

// Ejemplo: Array y métodos
const numeros = [1, 2, 3, 4, 5];
const duplicados = numeros.map(n => n * 2);
console.log('Números originales:', numeros);
console.log('Números duplicados:', duplicados);

// ¡Experimenta con tu propio código!</textarea>
            </div>
        </div>

        <!-- Output Panel -->
        <div class="output-panel">
            <div class="panel-header">
                <span>📊 Resultado</span>
                <button class="btn-secondary" id="clearOutputBtn">Limpiar</button>
            </div>
            <div class="output-container" id="outputContainer">
                <div class="log-entry log-info">
                    Simulador listo. Ejecuta tu código para ver los resultados aquí.
                </div>
            </div>
        </div>

        <!-- Console Panel -->
        <div class="console-panel">
            <div class="panel-header">
                <span>💻 Consola Interactiva</span>
                <button class="btn-secondary" id="clearConsoleBtn">Limpiar</button>
            </div>
            <div class="console-output" id="consoleOutput">
                <div class="log-entry log-success">
                    JavaScript Simulator v1.0 - Listo para usar
                </div>
                <div class="log-entry log-info">
                    Tip: Puedes escribir comandos JavaScript directamente en la consola
                </div>
            </div>
            <div class="console-input">
                <span class="console-prompt">></span>
                <input type="text" id="consoleInput" placeholder="Escribe comandos JavaScript aquí...">
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/closebrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/matchbrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/selection/active-line.min.js"></script>
    
    <script>
        // Inicializar CodeMirror
        const editor = CodeMirror.fromTextArea(document.getElementById('codeEditor'), {
            mode: 'javascript',
            theme: 'monokai',
            lineNumbers: true,
            autoCloseBrackets: true,
            matchBrackets: true,
            styleActiveLine: true,
            indentUnit: 2,
            tabSize: 2,
            lineWrapping: true
        });

        // Referencias a elementos
        const runBtn = document.getElementById('runBtn');
        const clearBtn = document.getElementById('clearBtn');
        const shareBtn = document.getElementById('shareBtn');
        const clearOutputBtn = document.getElementById('clearOutputBtn');
        const clearConsoleBtn = document.getElementById('clearConsoleBtn');
        const outputContainer = document.getElementById('outputContainer');
        const consoleOutput = document.getElementById('consoleOutput');
        const consoleInput = document.getElementById('consoleInput');
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');
        const lineCount = document.getElementById('lineCount');
        const examplesBtn = document.getElementById('examplesBtn');
        const examplesMenu = document.getElementById('examplesMenu');

        // Estado del simulador
        let executionCount = 0;

        // Actualizar contador de líneas
        editor.on('change', () => {
            lineCount.textContent = `Líneas: ${editor.lineCount()}`;
        });

        // Función para añadir log a la salida
        function addLog(message, type = 'info') {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = message;
            outputContainer.appendChild(logEntry);
            outputContainer.scrollTop = outputContainer.scrollHeight;
        }

        // Función para añadir log a la consola
        function addConsoleLog(message, type = 'info') {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = message;
            consoleOutput.appendChild(logEntry);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        // Sobrescribir console.log para capturar salida
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };

        function setupConsoleCapture() {
            console.log = (...args) => {
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' ');
                addLog(message, 'info');
                addConsoleLog(`<span style="color: #58a6ff;">></span> ${message}`, 'info');
                originalConsole.log(...args);
            };

            console.error = (...args) => {
                const message = args.join(' ');
                addLog(`Error: ${message}`, 'error');
                addConsoleLog(`<span style="color: #ff6b6b;">✗</span> Error: ${message}`, 'error');
                originalConsole.error(...args);
            };

            console.warn = (...args) => {
                const message = args.join(' ');
                addLog(`Warning: ${message}`, 'warn');
                addConsoleLog(`<span style="color: #f85149;">⚠</span> Warning: ${message}`, 'warn');
                originalConsole.warn(...args);
            };
        }

        // Función para ejecutar código
        function executeCode() {
            const code = editor.getValue();
            
            if (!code.trim()) {
                addLog('No hay código para ejecutar', 'warn');
                return;
            }

            executionCount++;
            statusDot.className = 'status-dot';
            statusText.textContent = 'Ejecutando...';

            addLog(`--- Ejecución #${executionCount} ---`, 'info');
            addConsoleLog(`<span style="color: #f7df1e;">🚀</span> Ejecutando código...`, 'info');

            try {
                // Crear un contexto seguro para la ejecución
                const func = new Function(code);
                func();
                
                statusDot.className = 'status-dot';
                statusText.textContent = 'Ejecutado correctamente';
                addConsoleLog(`<span style="color: #56d364;">✓</span> Código ejecutado correctamente`, 'success');
            } catch (error) {
                statusDot.className = 'status-dot error';
                statusText.textContent = 'Error en ejecución';
                addLog(`Error: ${error.message}`, 'error');
                addConsoleLog(`<span style="color: #ff6b6b;">✗</span> ${error.message}`, 'error');
            }
        }

        // Ejemplos de código
        const examples = {
            variables: `// Variables y Tipos de Datos
let nombre = 'JavaScript';
const año = 2024;
var esGenial = true;

console.log('Nombre:', nombre);
console.log('Año:', año);
console.log('Es genial:', esGenial);

// Tipos de datos
console.log('Tipo de nombre:', typeof nombre);
console.log('Tipo de año:', typeof año);
console.log('Tipo de esGenial:', typeof esGenial);`,

            functions: `// Funciones en JavaScript
function saludar(nombre) {
    return \`¡Hola, \${nombre}!\`;
}

// Arrow function
const despedir = (nombre) => \`¡Adiós, \${nombre}!\`;

// Función con múltiples parámetros
function calcular(a, b, operacion = 'suma') {
    switch(operacion) {
        case 'suma': return a + b;
        case 'resta': return a - b;
        case 'multiplicacion': return a * b;
        case 'division': return a / b;
        default: return 'Operación no válida';
    }
}

console.log(saludar('Desarrollador'));
console.log(despedir('Usuario'));
console.log('5 + 3 =', calcular(5, 3));
console.log('10 * 4 =', calcular(10, 4, 'multiplicacion'));`,

            arrays: `// Arrays y sus métodos
const frutas = ['manzana', 'banana', 'naranja'];
const numeros = [1, 2, 3, 4, 5];

console.log('Frutas:', frutas);
console.log('Números:', numeros);

// Métodos de array
console.log('Primera fruta:', frutas[0]);
console.log('Última fruta:', frutas[frutas.length - 1]);

// map, filter, reduce
const numerosDuplicados = numeros.map(n => n * 2);
const numerosPares = numeros.filter(n => n % 2 === 0);
const suma = numeros.reduce((acc, n) => acc + n, 0);

console.log('Duplicados:', numerosDuplicados);
console.log('Pares:', numerosPares);
console.log('Suma total:', suma);

// Añadir y quitar elementos
frutas.push('uva');
console.log('Después de push:', frutas);

const frutaQuitada = frutas.pop();
console.log('Fruta quitada:', frutaQuitada);
console.log('Array final:', frutas);`,

            objects: `// Objetos en JavaScript
const persona = {
    nombre: 'Ana',
    edad: 28,
    profesion: 'Desarrolladora',
    habilidades: ['JavaScript', 'React', 'Node.js'],
    
    // Método del objeto
    presentarse() {
        return \`Hola, soy \${this.nombre}, tengo \${this.edad} años y soy \${this.profesion}\`;
    },
    
    // Getter
    get descripcion() {
        return \`\${this.nombre} - \${this.profesion}\`;
    }
};

console.log('Persona:', persona);
console.log(persona.presentarse());
console.log('Descripción:', persona.descripcion);

// Destructuring
const { nombre, edad, habilidades } = persona;
console.log('Nombre:', nombre);
console.log('Edad:', edad);
console.log('Habilidades:', habilidades);

// Object.keys, Object.values, Object.entries
console.log('Claves:', Object.keys(persona));
console.log('Valores:', Object.values(persona));
console.log('Entradas:', Object.entries(persona));`,

            async: `// Programación Asíncrona
console.log('Inicio del programa');

// Simulación de operación asíncrona
function operacionAsincrona(tiempo, mensaje) {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve(mensaje);
        }, tiempo);
    });
}

// Usando Promises
operacionAsincrona(1000, 'Operación 1 completada')
    .then(resultado => {
        console.log(resultado);
        return operacionAsincrona(500, 'Operación 2 completada');
    })
    .then(resultado => {
        console.log(resultado);
    });

// Usando async/await
async function ejecutarOperaciones() {
    try {
        console.log('Iniciando operaciones async/await...');
        
        const resultado1 = await operacionAsincrona(800, 'Async/Await - Operación 1');
        console.log(resultado1);
        
        const resultado2 = await operacionAsincrona(300, 'Async/Await - Operación 2');
        console.log(resultado2);
        
        console.log('Todas las operaciones completadas');
    } catch (error) {
        console.error('Error:', error);
    }
}

ejecutarOperaciones();

console.log('Fin del programa (esto se ejecuta inmediatamente)');`,

            classes: `// Clases ES6
class Animal {
    constructor(nombre, tipo) {
        this.nombre = nombre;
        this.tipo = tipo;
    }
    
    hablar() {
        return \`\${this.nombre} hace un sonido\`;
    }
    
    // Método estático
    static crearPerro(nombre) {
        return new Animal(nombre, 'perro');
    }
}

// Herencia
class Perro extends Animal {
    constructor(nombre, raza) {
        super(nombre, 'perro');
        this.raza = raza;
    }
    
    hablar() {
        return \`\${this.nombre} ladra: ¡Guau guau!\`;
    }
    
    moverCola() {
        return \`\${this.nombre} mueve la cola felizmente\`;
    }
}

// Crear instancias
const animal = new Animal('Genérico', 'desconocido');
const perro = new Perro('Rex', 'Pastor Alemán');
const otroPerro = Animal.crearPerro('Max');

console.log(animal.hablar());
console.log(perro.hablar());
console.log(perro.moverCola());
console.log('Raza de Rex:', perro.raza);
console.log('Otro perro:', otroPerro.nombre, otroPerro.tipo);

// Verificar instancias
console.log('perro es instancia de Perro:', perro instanceof Perro);
console.log('perro es instancia de Animal:', perro instanceof Animal);`
        };

        // Event listeners
        runBtn.addEventListener('click', executeCode);

        clearBtn.addEventListener('click', () => {
            editor.setValue('// Escribe tu código JavaScript aquí\nconsole.log("¡Hola, mundo!");');
            addConsoleLog('<span style="color: #f7df1e;">🗑</span> Editor limpiado', 'info');
        });

        shareBtn.addEventListener('click', () => {
            const code = editor.getValue();
            const encodedCode = encodeURIComponent(code);
            const shareUrl = `${window.location.origin}${window.location.pathname}?code=${encodedCode}`;
            
            if (navigator.share) {
                navigator.share({
                    title: 'Código JavaScript',
                    text: 'Mira este código JavaScript',
                    url: shareUrl
                });
            } else {
                navigator.clipboard.writeText(shareUrl).then(() => {
                    addConsoleLog('<span style="color: #56d364;">📤</span> URL copiada al portapapeles', 'success');
                });
            }
        });

        clearOutputBtn.addEventListener('click', () => {
            outputContainer.innerHTML = '<div class="log-entry log-info">Salida limpiada. Ejecuta código para ver resultados.</div>';
        });

        clearConsoleBtn.addEventListener('click', () => {
            consoleOutput.innerHTML = '<div class="log-entry log-success">Consola limpiada</div>';
        });

        // Manejo de ejemplos
        examplesBtn.addEventListener('click', () => {
            examplesMenu.classList.toggle('show');
        });

        document.addEventListener('click', (e) => {
            if (!examplesBtn.contains(e.target) && !examplesMenu.contains(e.target)) {
                examplesMenu.classList.remove('show');
            }
        });

        document.querySelectorAll('.example-item').forEach(item => {
            item.addEventListener('click', () => {
                const exampleKey = item.dataset.example;
                if (examples[exampleKey]) {
                    editor.setValue(examples[exampleKey]);
                    addConsoleLog(`<span style="color: #f7df1e;">📝</span> Ejemplo cargado: ${item.querySelector('.example-title').textContent}`, 'info');
                }
                examplesMenu.classList.remove('show');
            });
        });

        // Consola interactiva
        consoleInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const command = consoleInput.value.trim();
                if (command) {
                    addConsoleLog(`<span style="color: #f7df1e;">></span> ${command}`, 'info');
                    
                    try {
                        const result = eval(command);
                        if (result !== undefined) {
                            const resultStr = typeof result === 'object' ? JSON.stringify(result, null, 2) : String(result);
                            addConsoleLog(`<span style="color: #58a6ff;">←</span> ${resultStr}`, 'info');
                        }
                    } catch (error) {
                        addConsoleLog(`<span style="color: #ff6b6b;">✗</span> ${error.message}`, 'error');
                    }
                    
                    consoleInput.value = '';
                }
            }
        });

        // Atajos de teclado
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                executeCode();
            }
        });

        // Cargar código desde URL si existe
        const urlParams = new URLSearchParams(window.location.search);
        const codeParam = urlParams.get('code');
        if (codeParam) {
            try {
                const decodedCode = decodeURIComponent(codeParam);
                editor.setValue(decodedCode);
                addConsoleLog('<span style="color: #56d364;">🔗</span> Código cargado desde URL', 'success');
            } catch (error) {
                addConsoleLog('<span style="color: #ff6b6b;">✗</span> Error al cargar código desde URL', 'error');
            }
        }

        // Configurar captura de consola
        setupConsoleCapture();

        // Mensaje de bienvenida
        addConsoleLog('<span style="color: #f7df1e;">🎉</span> ¡Simulador listo! Presiona Ctrl+Enter para ejecutar código', 'success');
    </script>
</body>
</html>
