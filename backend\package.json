{"name": "codecraft-academy-backend", "version": "1.0.0", "description": "Backend completo para CodeCraft Academy - Plataforma de aprendizaje de programación", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "seed": "node scripts/seedDatabase.js", "migrate": "node scripts/migrate.js", "build": "npm run build:client && npm run build:server", "build:client": "cd ../frontend && npm run build", "build:server": "echo 'Server build complete'", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "docker:build": "docker build -t codecraft-academy .", "docker:run": "docker run -p 3000:3000 codecraft-academy"}, "keywords": ["education", "programming", "javascript", "learning", "platform", "courses", "backend", "api"], "author": "CodeCraft Academy Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "cloudinary": "^1.40.0", "nodemailer": "^6.9.4", "socket.io": "^4.7.2", "redis": "^4.6.7", "stripe": "^13.5.0", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-github2": "^0.1.12", "passport-jwt": "^4.0.1", "dotenv": "^16.3.1", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.10.0", "joi": "^17.9.2", "moment": "^2.29.4", "uuid": "^9.0.0", "crypto": "^1.0.1", "sharp": "^0.32.5", "pdf-lib": "^1.17.1", "handlebars": "^4.7.8", "cron": "^2.4.4", "express-slow-down": "^1.6.0", "express-mongo-sanitize": "^2.2.0", "xss-clean": "^0.1.4", "hpp": "^0.2.3"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3", "eslint": "^8.47.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-node": "^11.1.0", "prettier": "^3.0.2", "@types/node": "^20.5.7", "mongodb-memory-server": "^8.15.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/codecraft-academy/backend.git"}, "bugs": {"url": "https://github.com/codecraft-academy/backend/issues"}, "homepage": "https://codecraft-academy.com"}