<svg width="1000" height="700" xmlns="http://www.w3.org/2000/svg">
  <!-- Definiciones -->
  <defs>
    <!-- Gradientes -->
    <linearGradient id="stepGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="memoryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="callGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="persistGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    
    <!-- Sombra -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
    
    <!-- Flecha -->
    <marker id="arrow" markerWidth="12" markerHeight="12" refX="6" refY="3" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,6 L9,3 z" fill="#2c3e50" stroke="#2c3e50" stroke-width="1"/>
    </marker>
  </defs>

  <!-- Título -->
  <text x="500" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2563eb">
    Flujo de Ejecución: Closure en Acción
  </text>
  
  <!-- Paso 1: Llamada inicial -->
  <rect x="50" y="60" width="200" height="80" fill="url(#stepGradient)" stroke="#1d4ed8" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="150" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#fff">1. Llamada Inicial</text>
  <text x="150" y="105" text-anchor="middle" font-size="12" fill="#fff">const gestor =</text>
  <text x="150" y="125" text-anchor="middle" font-size="12" fill="#fff">crearGestorEstado({})</text>
  
  <!-- Paso 2: Creación del contexto -->
  <rect x="300" y="60" width="200" height="80" fill="url(#memoryGradient)" stroke="#047857" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="400" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#fff">2. Contexto Creado</text>
  <text x="400" y="105" text-anchor="middle" font-size="12" fill="#fff">Variables privadas</text>
  <text x="400" y="125" text-anchor="middle" font-size="12" fill="#fff">inicializadas</text>
  
  <!-- Paso 3: Retorno del objeto -->
  <rect x="550" y="60" width="200" height="80" fill="url(#callGradient)" stroke="#d97706" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="650" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#fff">3. Objeto Retornado</text>
  <text x="650" y="105" text-anchor="middle" font-size="12" fill="#fff">API pública con</text>
  <text x="650" y="125" text-anchor="middle" font-size="12" fill="#fff">acceso al closure</text>
  
  <!-- Paso 4: Función externa termina -->
  <rect x="800" y="60" width="150" height="80" fill="url(#persistGradient)" stroke="#7c3aed" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="875" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#fff">4. Función</text>
  <text x="875" y="105" text-anchor="middle" font-size="12" fill="#fff">Externa</text>
  <text x="875" y="125" text-anchor="middle" font-size="12" fill="#fff">Termina</text>
  
  <!-- Memory Stack Visualization -->
  <rect x="50" y="180" width="900" height="200" fill="#f8fafc" stroke="#64748b" stroke-width="2" rx="10"/>
  <text x="70" y="205" font-size="16" font-weight="bold" fill="#334155">Estado de la Memoria</text>
  
  <!-- Call Stack -->
  <rect x="80" y="220" width="180" height="140" fill="#fee2e2" stroke="#dc2626" stroke-width="2" rx="5"/>
  <text x="90" y="240" font-size="14" font-weight="bold" fill="#dc2626">Call Stack</text>
  <rect x="90" y="250" width="160" height="30" fill="#fca5a5" stroke="#dc2626" stroke-width="1" rx="3"/>
  <text x="170" y="270" text-anchor="middle" font-size="12" fill="#7f1d1d">crearGestorEstado()</text>
  <text x="170" y="300" text-anchor="middle" font-size="11" fill="#dc2626">Se elimina al terminar</text>
  <text x="170" y="320" text-anchor="middle" font-size="11" fill="#dc2626">la función</text>
  
  <!-- Heap Memory -->
  <rect x="280" y="220" width="300" height="140" fill="#dcfce7" stroke="#16a34a" stroke-width="2" rx="5"/>
  <text x="290" y="240" font-size="14" font-weight="bold" fill="#16a34a">Heap Memory (Closure)</text>
  <rect x="290" y="250" width="130" height="25" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="355" y="267" text-anchor="middle" font-size="11" fill="#14532d">estado: {}</text>
  <rect x="290" y="280" width="130" height="25" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="355" y="297" text-anchor="middle" font-size="11" fill="#14532d">historial: []</text>
  <rect x="430" y="250" width="140" height="25" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="500" y="267" text-anchor="middle" font-size="11" fill="#14532d">contadorTransacciones</text>
  <rect x="430" y="280" width="140" height="25" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="500" y="297" text-anchor="middle" font-size="11" fill="#14532d">funciones privadas</text>
  <text x="430" y="325" text-anchor="middle" font-size="12" font-weight="bold" fill="#16a34a">¡PERSISTE!</text>
  <text x="430" y="345" text-anchor="middle" font-size="11" fill="#16a34a">Accesible via closure</text>
  
  <!-- Returned Object -->
  <rect x="600" y="220" width="180" height="140" fill="#dbeafe" stroke="#2563eb" stroke-width="2" rx="5"/>
  <text x="610" y="240" font-size="14" font-weight="bold" fill="#2563eb">Objeto Retornado</text>
  <rect x="610" y="250" width="160" height="20" fill="#bfdbfe" stroke="#2563eb" stroke-width="1" rx="3"/>
  <text x="690" y="263" text-anchor="middle" font-size="10" fill="#1e40af">obtenerEstado()</text>
  <rect x="610" y="275" width="160" height="20" fill="#bfdbfe" stroke="#2563eb" stroke-width="1" rx="3"/>
  <text x="690" y="288" text-anchor="middle" font-size="10" fill="#1e40af">actualizarEstado()</text>
  <rect x="610" y="300" width="160" height="20" fill="#bfdbfe" stroke="#2563eb" stroke-width="1" rx="3"/>
  <text x="690" y="313" text-anchor="middle" font-size="10" fill="#1e40af">suscribirse()</text>
  <text x="690" y="340" text-anchor="middle" font-size="11" fill="#2563eb">Mantiene referencia</text>
  <text x="690" y="355" text-anchor="middle" font-size="11" fill="#2563eb">al closure</text>
  
  <!-- Variable Reference -->
  <rect x="800" y="220" width="130" height="140" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" rx="5"/>
  <text x="810" y="240" font-size="14" font-weight="bold" fill="#f59e0b">Variable gestor</text>
  <rect x="810" y="250" width="110" height="30" fill="#fde68a" stroke="#f59e0b" stroke-width="1" rx="3"/>
  <text x="865" y="270" text-anchor="middle" font-size="11" fill="#92400e">Referencia al</text>
  <text x="865" y="285" text-anchor="middle" font-size="11" fill="#92400e">objeto retornado</text>
  <text x="865" y="310" text-anchor="middle" font-size="11" fill="#f59e0b">Permite acceso</text>
  <text x="865" y="325" text-anchor="middle" font-size="11" fill="#f59e0b">a métodos</text>
  <text x="865" y="340" text-anchor="middle" font-size="11" fill="#f59e0b">públicos</text>
  
  <!-- Uso posterior -->
  <rect x="50" y="420" width="900" height="120" fill="#f1f5f9" stroke="#64748b" stroke-width="2" rx="10"/>
  <text x="70" y="445" font-size="16" font-weight="bold" fill="#334155">Uso Posterior del Closure</text>
  
  <!-- Llamadas a métodos -->
  <rect x="80" y="460" width="200" height="60" fill="url(#callGradient)" stroke="#d97706" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="180" y="480" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">gestor.actualizarEstado()</text>
  <text x="180" y="500" text-anchor="middle" font-size="11" fill="#fff">Accede a variables</text>
  <text x="180" y="515" text-anchor="middle" font-size="11" fill="#fff">privadas del closure</text>
  
  <rect x="300" y="460" width="200" height="60" fill="url(#memoryGradient)" stroke="#047857" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="400" y="480" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">Estado Modificado</text>
  <text x="400" y="500" text-anchor="middle" font-size="11" fill="#fff">Variables privadas</text>
  <text x="400" y="515" text-anchor="middle" font-size="11" fill="#fff">actualizadas</text>
  
  <rect x="520" y="460" width="200" height="60" fill="url(#persistGradient)" stroke="#7c3aed" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="620" y="480" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">Estado Persistente</text>
  <text x="620" y="500" text-anchor="middle" font-size="11" fill="#fff">Cambios mantenidos</text>
  <text x="620" y="515" text-anchor="middle" font-size="11" fill="#fff">entre llamadas</text>
  
  <rect x="740" y="460" width="180" height="60" fill="url(#stepGradient)" stroke="#1d4ed8" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="830" y="480" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">Múltiples Instancias</text>
  <text x="830" y="500" text-anchor="middle" font-size="11" fill="#fff">Cada closure es</text>
  <text x="830" y="515" text-anchor="middle" font-size="11" fill="#fff">independiente</text>
  
  <!-- Flechas de flujo -->
  <line x1="250" y1="100" x2="300" y2="100" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="500" y1="100" x2="550" y2="100" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="750" y1="100" x2="800" y2="100" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>
  
  <!-- Flechas de memoria -->
  <line x1="430" y1="360" x2="430" y2="420" stroke="#16a34a" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="280" y1="490" x2="300" y2="490" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="500" y1="490" x2="520" y2="490" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="720" y1="490" x2="740" y2="490" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
  
  <!-- Etiquetas explicativas -->
  <text x="500" y="570" text-anchor="middle" font-size="14" font-weight="bold" fill="#64748b">
    El closure mantiene vivas las variables privadas incluso después de que la función externa termine
  </text>
  
  <text x="500" y="590" text-anchor="middle" font-size="12" fill="#64748b">
    Esto permite encapsulación verdadera y persistencia de estado en JavaScript
  </text>
  
  <!-- Conexión visual entre heap y objeto -->
  <path d="M 580 290 Q 590 290 590 290" stroke="#16a34a" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
  <path d="M 600 290 Q 600 290 600 290" stroke="#16a34a" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
</svg>
