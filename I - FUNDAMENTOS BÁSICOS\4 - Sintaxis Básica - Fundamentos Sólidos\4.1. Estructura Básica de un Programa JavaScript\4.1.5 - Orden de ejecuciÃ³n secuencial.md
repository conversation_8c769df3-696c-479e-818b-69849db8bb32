## 4.1.5. Orden de ejecución secuencial

### Introducción
El orden de ejecución secuencial en JavaScript significa que el código se ejecuta línea por línea.  
Esto asegura que las instrucciones se procesen en el orden escrito.  
Es esencial para predecir el comportamiento del programa.  
Además, ayuda a depurar al seguir el flujo lógico.  
Comprenderlo es básico para escribir código efectivo.  
¿ Cómo influye el orden secuencial en el resultado de un script?

### Código de Ejemplo
```javascript
console.log('Paso 1');
let x = 1;
console.log('Paso 2: x = ' + x);
x = x + 1;
console.log('Paso 3: x = ' + x);
if (x > 1) {
    console.log('Paso 4: x es mayor que 1');
}
console.log('Paso 5: Fin');
```
### Resultados en Consola
- `Paso 1`
- `Paso 2: x = 1`
- `Paso 3: x = 2`
- `Paso 4: x es mayor que 1`
- `Paso 5: Fin`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> Log1["Paso 1: console.log('Paso 1') <br> - Resultado: Paso 1"]
    Log1 --> Declaracion["Paso 2: let x = 1"]
    Declaracion --> Log2["Paso 3: console.log('Paso 2: x = ' + x) <br> - Resultado: Paso 2: x = 1"]
    Log2 --> Asignacion["Paso 4: x = x + 1"]
    Asignacion --> Log3["Paso 5: console.log('Paso 3: x = ' + x) <br> - Resultado: Paso 3: x = 2"]
    Log3 --> If["Paso 6: if (x > 1)"]
    If --> Log4["Paso 7: console.log('Paso 4: x es mayor que 1') <br> - Resultado: Paso 4: x es mayor que 1"]
    Log4 --> Log5["Paso 8: console.log('Paso 5: Fin') <br> - Resultado: Paso 5: Fin"]
    Log5 --> Fin["Paso 9: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Secuencia["Secuencia"]
    If --> NotaCondicion["Nota: Evaluar condición"]
    Asignacion --> NotaAsignacion["Nota: Modificar variable"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log1 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Declaracion fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log2 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Asignacion fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log3 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style If fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log4 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log5 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Secuencia fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaCondicion fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaAsignacion fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Paso1["Paso 1 <br> - Log inicial"]
        Paso1 --> Paso2["Paso 2 <br> - Declarar variable"]
        Paso2 --> Paso3["Paso 3 <br> - Log valor"]
        Paso3 --> Paso4["Paso 4 <br> - Modificar variable"]
        Paso4 --> Paso5["Paso 5 <br> - Log nuevo valor"]
        Paso5 --> Paso6["Paso 6 <br> - Condicional"]
        Paso6 --> Paso7["Paso 7 <br> - Log condicional"]
        Paso7 --> Paso8["Paso 8 <br> - Log final"]
        Inicio --> Desarrollador(("Desarrollador")) --> Flujo["Flujo"]
        Paso6 --> NotaFlujo["Nota: Orden lineal"]
        Paso4 --> NotaModificacion["Nota: Cambios secuenciales"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Paso1 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Paso2 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Paso3 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Paso4 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Paso5 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Paso6 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Paso7 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Paso8 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Flujo fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaFlujo fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaModificacion fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Inicializar variables antes de usarlas en cálculos posteriores.
2. Ejecutar logs en orden para rastrear el estado del programa.
3. Modificar valores secuencialmente en algoritmos simples.
4. Aplicar condicionales después de preparar datos necesarios.
5. Asegurar que el fin del script muestre resultados finales.

### Errores comunes
1. Usar variables antes de declararlas, causando undefined.
2. Colocar condicionales antes de asignar valores requeridos.
3. Ignorar el orden en funciones con dependencias.
4. No considerar el flujo secuencial en depuración.
5. Mezclar código asíncrono sin manejar promesas correctamente.

### Recomendaciones
1. Escribe código en orden lógico para facilitar la lectura.
2. Usa logs para verificar el orden de ejecución durante pruebas.
3. Declara todas las variables al inicio de su scope.
4. Evita dependencias circulares que rompan la secuencialidad.
5. Aprende sobre hoisting para entender excepciones al orden.