## 4.1.4. Bloques de código y scope

### Introducción
Los bloques de código en JavaScript delimitan secciones con llaves {}.  
El scope define la visibilidad de variables dentro de estos bloques.  
Esto es fundamental para gestionar variables sin conflictos.  
Además, previene errores al limitar el acceso a variables.  
Comprenderlo es clave para un código modular y seguro.  
¿ Cómo afecta el scope al comportamiento de las variables?

### Código de Ejemplo
```javascript
let globalVar = 'Global';
function ejemplo() {
    let localVar = 'Local';
    if (true) {
        let blockVar = 'Bloque';
        console.log(globalVar); // Accesible
        console.log(localVar); // Accesible
        console.log(blockVar); // Accesible
    }
    console.log(blockVar); // Error: no accesible
}
ejemplo();
```
### Resultados en Consola
- `Global`
- `Local`
- `Bloque`
- Error: blockVar is not defined

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> Global["Paso 1: Declarar globalVar <br> - Valor: 'Global'"]
    Global --> Funcion["Paso 2: Definir función ejemplo()"]
    Funcion --> Local["Paso 3: Declarar localVar <br> - Valor: 'Local'"]
    Local --> If["Paso 4: Bloque if <br> - Condición: true"]
    If --> Block["Paso 5: Declarar blockVar <br> - Valor: 'Bloque'"]
    Block --> Log1["Paso 6: console.log(globalVar) <br> - Resultado: Global"]
    Log1 --> Log2["Paso 7: console.log(localVar) <br> - Resultado: Local"]
    Log2 --> Log3["Paso 8: console.log(blockVar) <br> - Resultado: Bloque"]
    Log3 --> FinBloque["Paso 9: Fin de bloque"]
    FinBloque --> Log4["Paso 10: console.log(blockVar) <br> - Error: no definido"]
    Log4 --> Fin["Paso 11: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Scope["Scope"]
    Block --> NotaBlock["Nota: Scope de bloque"]
    Local --> NotaLocal["Nota: Scope de función"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Global fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Funcion fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Local fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:12px
    style If fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Block fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:12px
    style Log1 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log2 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log3 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style FinBloque fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log4 fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Scope fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaBlock fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaLocal fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> GlobalScope["Global Scope <br> - globalVar"]
        GlobalScope --> FunctionScope["Function Scope <br> - localVar"]
        FunctionScope --> BlockScope["Block Scope <br> - blockVar"]
        BlockScope --> Acceso["Acceso <br> - Logs dentro del bloque"]
        Acceso --> Error["Error <br> - Acceso fuera del bloque"]
        Inicio --> Desarrollador(("Desarrollador")) --> Visibilidad["Visibilidad"]
        BlockScope --> NotaScope["Nota: Limitar visibilidad"]
        FunctionScope --> NotaFuncion["Nota: Encapsular lógica"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style GlobalScope fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style FunctionScope fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style BlockScope fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:12px
    style Acceso fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Error fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Visibilidad fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaScope fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaFuncion fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Definir variables locales en funciones para evitar contaminación global.
2. Usar bloques en condicionales para limitar scope de variables temporales.
3. Gestionar visibilidad en loops con let para scope de bloque.
4. Prevenir errores accediendo variables fuera de su scope.
5. Organizar código modular con scopes bien definidos.

### Errores comunes
1. Acceder a variables de bloque fuera de su scope.
2. Usar var en lugar de let/const, causando scope inesperado.
3. No entender la diferencia entre scope global y local.
4. Sobrescribir variables en scopes anidados accidentalmente.
5. Ignorar scope en closures, llevando a bugs sutiles.

### Recomendaciones
1. Usa let y const para scope de bloque siempre que sea posible.
2. Limita el uso de variables globales al mínimo necesario.
3. Entiende y aplica el principio de menor privilegio en scopes.
4. Prueba accesos a variables en diferentes scopes.
5. Usa herramientas de linting para detectar problemas de scope.