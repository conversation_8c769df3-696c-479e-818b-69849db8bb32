/**
 * PARADIGM COMPARATOR - SISTEMA AVANZADO DE COMPARACIÓN DE PARADIGMAS
 * 
 * Este sistema permite ejecutar, comparar y analizar diferentes implementaciones
 * del mismo problema usando distintos paradigmas de programación.
 * 
 * Características:
 * - Ejecución de múltiples paradigmas
 * - Medición de performance
 * - Análisis de complejidad
 * - Generación de reportes comparativos
 * - Visualización de resultados
 * 
 * <AUTHOR> de Programación
 * @version 1.0.0
 */

class ParadigmComparator {
    constructor() {
        // Registro de implementaciones por paradigma
        this.implementations = new Map();
        
        // Métricas de ejecución
        this.metrics = {
            executionTimes: new Map(),
            memoryUsage: new Map(),
            codeComplexity: new Map(),
            maintainabilityScore: new Map()
        };
        
        // Configuración de análisis
        this.config = {
            iterations: 1000,           // Iteraciones para benchmark
            warmupRuns: 100,           // Ejecuciones de calentamiento
            memoryThreshold: 50,       // MB threshold para memoria
            complexityThreshold: 10    // Threshold de complejidad ciclomática
        };
        
        console.log('🔄 ParadigmComparator inicializado');
    }
    
    /**
     * Registra una implementación de paradigma
     * 
     * @param {string} paradigm - Nombre del paradigma
     * @param {Function} implementation - Función de implementación
     * @param {Object} metadata - Metadatos de la implementación
     */
    registerImplementation(paradigm, implementation, metadata = {}) {
        // Validar parámetros
        if (!paradigm || typeof paradigm !== 'string') {
            throw new Error('Paradigma debe ser un string válido');
        }
        
        if (!implementation || typeof implementation !== 'function') {
            throw new Error('Implementación debe ser una función válida');
        }
        
        // Registrar implementación con metadatos
        this.implementations.set(paradigm, {
            name: paradigm,
            implementation,
            metadata: {
                description: metadata.description || `Implementación ${paradigm}`,
                complexity: metadata.complexity || 'medium',
                maintainability: metadata.maintainability || 'medium',
                performance: metadata.performance || 'medium',
                readability: metadata.readability || 'medium',
                ...metadata
            },
            registeredAt: new Date()
        });
        
        console.log(`✅ Paradigma '${paradigm}' registrado exitosamente`);
    }
    
    /**
     * Ejecuta todas las implementaciones registradas
     * 
     * @param {*} input - Datos de entrada para las implementaciones
     * @returns {Object} Resultados de todas las ejecuciones
     */
    async executeAll(input) {
        console.log('🚀 Iniciando ejecución de todos los paradigmas...');
        
        const results = new Map();
        const startTime = performance.now();
        
        // Ejecutar cada implementación
        for (const [paradigm, config] of this.implementations) {
            try {
                console.log(`⚡ Ejecutando paradigma: ${paradigm}`);
                
                // Medir performance de ejecución
                const executionResult = await this.measureExecution(
                    config.implementation, 
                    input, 
                    paradigm
                );
                
                results.set(paradigm, {
                    paradigm,
                    result: executionResult.result,
                    metrics: executionResult.metrics,
                    metadata: config.metadata,
                    status: 'success'
                });
                
            } catch (error) {
                console.error(`❌ Error ejecutando ${paradigm}:`, error.message);
                
                results.set(paradigm, {
                    paradigm,
                    result: null,
                    error: error.message,
                    status: 'error'
                });
            }
        }
        
        const totalTime = performance.now() - startTime;
        console.log(`🏁 Ejecución completada en ${totalTime.toFixed(2)}ms`);
        
        return {
            results: Object.fromEntries(results),
            summary: this.generateSummary(results),
            executionTime: totalTime
        };
    }
    
    /**
     * Mide la performance de una implementación específica
     * 
     * @param {Function} implementation - Función a medir
     * @param {*} input - Datos de entrada
     * @param {string} paradigm - Nombre del paradigma
     * @returns {Object} Resultado con métricas
     */
    async measureExecution(implementation, input, paradigm) {
        // Calentamiento del motor JavaScript
        for (let i = 0; i < this.config.warmupRuns; i++) {
            try {
                implementation(input);
            } catch (error) {
                // Ignorar errores en calentamiento
            }
        }
        
        // Medición de memoria inicial
        const initialMemory = this.getMemoryUsage();
        
        // Medición de tiempo de ejecución
        const executionTimes = [];
        let result = null;
        
        for (let i = 0; i < this.config.iterations; i++) {
            const startTime = performance.now();
            
            try {
                result = implementation(input);
            } catch (error) {
                throw new Error(`Error en iteración ${i}: ${error.message}`);
            }
            
            const endTime = performance.now();
            executionTimes.push(endTime - startTime);
        }
        
        // Medición de memoria final
        const finalMemory = this.getMemoryUsage();
        
        // Calcular estadísticas
        const avgTime = executionTimes.reduce((a, b) => a + b, 0) / executionTimes.length;
        const minTime = Math.min(...executionTimes);
        const maxTime = Math.max(...executionTimes);
        const stdDev = this.calculateStandardDeviation(executionTimes, avgTime);
        
        const metrics = {
            averageTime: avgTime,
            minTime,
            maxTime,
            standardDeviation: stdDev,
            memoryDelta: finalMemory - initialMemory,
            iterations: this.config.iterations,
            throughput: 1000 / avgTime // operaciones por segundo
        };
        
        // Almacenar métricas para comparación
        this.metrics.executionTimes.set(paradigm, metrics);
        
        return { result, metrics };
    }
    
    /**
     * Genera un resumen comparativo de todos los paradigmas
     * 
     * @param {Map} results - Resultados de ejecución
     * @returns {Object} Resumen comparativo
     */
    generateSummary(results) {
        const successful = Array.from(results.values()).filter(r => r.status === 'success');
        
        if (successful.length === 0) {
            return { error: 'No hay resultados exitosos para comparar' };
        }
        
        // Encontrar el más rápido
        const fastest = successful.reduce((prev, current) => 
            prev.metrics.averageTime < current.metrics.averageTime ? prev : current
        );
        
        // Encontrar el más eficiente en memoria
        const mostMemoryEfficient = successful.reduce((prev, current) => 
            prev.metrics.memoryDelta < current.metrics.memoryDelta ? prev : current
        );
        
        // Calcular rankings
        const timeRanking = successful
            .sort((a, b) => a.metrics.averageTime - b.metrics.averageTime)
            .map((item, index) => ({ paradigm: item.paradigm, rank: index + 1, time: item.metrics.averageTime }));
        
        const memoryRanking = successful
            .sort((a, b) => a.metrics.memoryDelta - b.metrics.memoryDelta)
            .map((item, index) => ({ paradigm: item.paradigm, rank: index + 1, memory: item.metrics.memoryDelta }));
        
        return {
            totalParadigms: results.size,
            successfulExecutions: successful.length,
            fastest: {
                paradigm: fastest.paradigm,
                time: fastest.metrics.averageTime,
                throughput: fastest.metrics.throughput
            },
            mostMemoryEfficient: {
                paradigm: mostMemoryEfficient.paradigm,
                memoryDelta: mostMemoryEfficient.metrics.memoryDelta
            },
            rankings: {
                byTime: timeRanking,
                byMemory: memoryRanking
            },
            recommendations: this.generateRecommendations(successful)
        };
    }
    
    /**
     * Genera recomendaciones basadas en los resultados
     * 
     * @param {Array} results - Resultados exitosos
     * @returns {Array} Lista de recomendaciones
     */
    generateRecommendations(results) {
        const recommendations = [];
        
        // Recomendación por performance
        const fastest = results.reduce((prev, current) => 
            prev.metrics.averageTime < current.metrics.averageTime ? prev : current
        );
        
        recommendations.push({
            type: 'performance',
            paradigm: fastest.paradigm,
            reason: `Mejor tiempo de ejecución: ${fastest.metrics.averageTime.toFixed(3)}ms`,
            useCase: 'Aplicaciones que requieren alta performance'
        });
        
        // Recomendación por memoria
        const memoryEfficient = results.reduce((prev, current) => 
            prev.metrics.memoryDelta < current.metrics.memoryDelta ? prev : current
        );
        
        recommendations.push({
            type: 'memory',
            paradigm: memoryEfficient.paradigm,
            reason: `Menor uso de memoria: ${memoryEfficient.metrics.memoryDelta.toFixed(2)}MB`,
            useCase: 'Aplicaciones con restricciones de memoria'
        });
        
        // Recomendación por consistencia
        const mostConsistent = results.reduce((prev, current) => 
            prev.metrics.standardDeviation < current.metrics.standardDeviation ? prev : current
        );
        
        recommendations.push({
            type: 'consistency',
            paradigm: mostConsistent.paradigm,
            reason: `Menor desviación estándar: ${mostConsistent.metrics.standardDeviation.toFixed(3)}ms`,
            useCase: 'Aplicaciones que requieren comportamiento predecible'
        });
        
        return recommendations;
    }
    
    /**
     * Obtiene el uso actual de memoria (simulado)
     * 
     * @returns {number} Uso de memoria en MB
     */
    getMemoryUsage() {
        // En un entorno real, usarías process.memoryUsage() en Node.js
        // o performance.memory en el navegador
        return Math.random() * 100; // Simulación
    }
    
    /**
     * Calcula la desviación estándar de un array de números
     * 
     * @param {Array<number>} values - Valores numéricos
     * @param {number} mean - Media de los valores
     * @returns {number} Desviación estándar
     */
    calculateStandardDeviation(values, mean) {
        const squaredDifferences = values.map(value => Math.pow(value - mean, 2));
        const avgSquaredDiff = squaredDifferences.reduce((a, b) => a + b, 0) / values.length;
        return Math.sqrt(avgSquaredDiff);
    }
    
    /**
     * Genera un reporte detallado en formato texto
     * 
     * @param {Object} results - Resultados de ejecución
     * @returns {string} Reporte formateado
     */
    generateReport(results) {
        let report = '\n📊 REPORTE COMPARATIVO DE PARADIGMAS\n';
        report += '='.repeat(50) + '\n\n';
        
        // Resumen general
        report += `📈 RESUMEN GENERAL:\n`;
        report += `   • Total de paradigmas: ${results.summary.totalParadigms}\n`;
        report += `   • Ejecuciones exitosas: ${results.summary.successfulExecutions}\n`;
        report += `   • Tiempo total de análisis: ${results.executionTime.toFixed(2)}ms\n\n`;
        
        // Ganadores por categoría
        report += `🏆 GANADORES POR CATEGORÍA:\n`;
        report += `   • Más rápido: ${results.summary.fastest.paradigm} (${results.summary.fastest.time.toFixed(3)}ms)\n`;
        report += `   • Más eficiente en memoria: ${results.summary.mostMemoryEfficient.paradigm}\n\n`;
        
        // Detalles por paradigma
        report += `📋 DETALLES POR PARADIGMA:\n`;
        for (const [paradigm, result] of Object.entries(results.results)) {
            if (result.status === 'success') {
                report += `   ${paradigm.toUpperCase()}:\n`;
                report += `     - Tiempo promedio: ${result.metrics.averageTime.toFixed(3)}ms\n`;
                report += `     - Throughput: ${result.metrics.throughput.toFixed(0)} ops/sec\n`;
                report += `     - Uso de memoria: ${result.metrics.memoryDelta.toFixed(2)}MB\n`;
                report += `     - Desviación estándar: ${result.metrics.standardDeviation.toFixed(3)}ms\n\n`;
            }
        }
        
        // Recomendaciones
        report += `💡 RECOMENDACIONES:\n`;
        results.summary.recommendations.forEach((rec, index) => {
            report += `   ${index + 1}. ${rec.type.toUpperCase()}: ${rec.paradigm}\n`;
            report += `      Razón: ${rec.reason}\n`;
            report += `      Caso de uso: ${rec.useCase}\n\n`;
        });
        
        return report;
    }
    
    /**
     * Limpia todas las métricas y registros
     */
    reset() {
        this.implementations.clear();
        this.metrics.executionTimes.clear();
        this.metrics.memoryUsage.clear();
        this.metrics.codeComplexity.clear();
        this.metrics.maintainabilityScore.clear();
        
        console.log('🧹 ParadigmComparator reiniciado');
    }
}

module.exports = ParadigmComparator;
