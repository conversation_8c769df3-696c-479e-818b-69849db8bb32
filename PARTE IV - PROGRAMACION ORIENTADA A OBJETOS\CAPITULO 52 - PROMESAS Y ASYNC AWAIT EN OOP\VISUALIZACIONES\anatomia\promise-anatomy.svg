<svg width="1000" height="700" xmlns="http://www.w3.org/2000/svg">
  <!-- Definiciones -->
  <defs>
    <!-- Gradientes -->
    <linearGradient id="promiseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="pendingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="fulfilledGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="rejectedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
    
    <!-- Sombra -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="3" dy="3" stdDeviation="4" flood-color="#000" flood-opacity="0.3"/>
    </filter>
    
    <!-- Flechas -->
    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
  </defs>

  <!-- Título -->
  <text x="500" y="30" text-anchor="middle" font-size="22" font-weight="bold" fill="#1f2937">
    Anatomía de una Promesa en Programación Orientada a Objetos
  </text>
  
  <!-- Clase Container -->
  <rect x="50" y="60" width="900" height="600" fill="#f8fafc" stroke="#64748b" stroke-width="3" rx="15" filter="url(#shadow)"/>
  <text x="70" y="90" font-size="18" font-weight="bold" fill="#374151">class SimplePromiseExample</text>
  
  <!-- Constructor -->
  <rect x="80" y="110" width="200" height="100" fill="#e0e7ff" stroke="#3b82f6" stroke-width="2" rx="8"/>
  <text x="90" y="130" font-size="14" font-weight="bold" fill="#1e40af">constructor()</text>
  <text x="90" y="150" font-size="11" fill="#1e40af">• this.data = null</text>
  <text x="90" y="165" font-size="11" fill="#1e40af">• this.isLoading = false</text>
  <text x="90" y="180" font-size="11" fill="#1e40af">• this.lastError = null</text>
  <text x="90" y="195" font-size="11" fill="#1e40af">Estado inicial de la clase</text>
  
  <!-- Promise Producer -->
  <rect x="320" y="110" width="280" height="180" fill="#dcfce7" stroke="#16a34a" stroke-width="3" rx="10" filter="url(#shadow)"/>
  <text x="460" y="135" text-anchor="middle" font-size="16" font-weight="bold" fill="#16a34a">fetchData(url) - Producer</text>
  
  <!-- Executor Function -->
  <rect x="340" y="150" width="240" height="130" fill="#f0fdf4" stroke="#16a34a" stroke-width="1" rx="5"/>
  <text x="460" y="170" text-anchor="middle" font-size="12" font-weight="bold" fill="#14532d">new Promise((resolve, reject) => {...})</text>
  
  <!-- Estados de la Promesa -->
  <rect x="350" y="180" width="70" height="25" fill="url(#pendingGradient)" stroke="#f59e0b" stroke-width="1" rx="3"/>
  <text x="385" y="197" text-anchor="middle" font-size="10" fill="#fff">PENDING</text>
  
  <rect x="430" y="180" width="70" height="25" fill="url(#fulfilledGradient)" stroke="#10b981" stroke-width="1" rx="3"/>
  <text x="465" y="197" text-anchor="middle" font-size="10" fill="#fff">FULFILLED</text>
  
  <rect x="510" y="180" width="70" height="25" fill="url(#rejectedGradient)" stroke="#ef4444" stroke-width="1" rx="3"/>
  <text x="545" y="197" text-anchor="middle" font-size="10" fill="#fff">REJECTED</text>
  
  <!-- Lógica interna -->
  <text x="350" y="220" font-size="10" fill="#14532d">setTimeout(() => {</text>
  <text x="360" y="235" font-size="10" fill="#14532d">if (url) {</text>
  <text x="370" y="250" font-size="10" fill="#14532d">resolve({data, url, timestamp})</text>
  <text x="360" y="265" font-size="10" fill="#14532d">} else {</text>
  <text x="370" y="280" font-size="10" fill="#14532d">reject(new Error('URL required'))</text>
  <text x="350" y="295" font-size="10" fill="#14532d">}, 1000)</text>
  
  <!-- Promise Consumer -->
  <rect x="640" y="110" width="280" height="180" fill="#fef3c7" stroke="#f59e0b" stroke-width="3" rx="10" filter="url(#shadow)"/>
  <text x="780" y="135" text-anchor="middle" font-size="16" font-weight="bold" fill="#92400e">loadData(url) - Consumer</text>
  
  <!-- Async/Await Logic -->
  <rect x="660" y="150" width="240" height="130" fill="#fffbeb" stroke="#f59e0b" stroke-width="1" rx="5"/>
  <text x="780" y="170" text-anchor="middle" font-size="12" font-weight="bold" fill="#92400e">async/await + try/catch/finally</text>
  
  <text x="670" y="190" font-size="10" fill="#92400e">this.isLoading = true</text>
  <text x="670" y="205" font-size="10" fill="#92400e">try {</text>
  <text x="680" y="220" font-size="10" fill="#92400e">this.data = await this.fetchData(url)</text>
  <text x="680" y="235" font-size="10" fill="#92400e">return this.data</text>
  <text x="670" y="250" font-size="10" fill="#92400e">} catch (error) {</text>
  <text x="680" y="265" font-size="10" fill="#92400e">this.lastError = error</text>
  <text x="670" y="280" font-size="10" fill="#92400e">} finally {</text>
  <text x="680" y="295" font-size="10" fill="#92400e">this.isLoading = false</text>
  
  <!-- Flujo de Estados -->
  <rect x="80" y="320" width="840" height="200" fill="#f1f5f9" stroke="#64748b" stroke-width="2" rx="10"/>
  <text x="90" y="345" font-size="16" font-weight="bold" fill="#374151">Flujo de Estados de la Promesa</text>
  
  <!-- Estado Inicial -->
  <circle cx="150" cy="400" r="40" fill="url(#pendingGradient)" stroke="#f59e0b" stroke-width="3" filter="url(#shadow)"/>
  <text x="150" y="395" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">PENDING</text>
  <text x="150" y="410" text-anchor="middle" font-size="10" fill="#fff">Estado Inicial</text>
  
  <!-- Flecha a Fulfilled -->
  <line x1="190" y1="380" x2="280" y2="360" stroke="#10b981" stroke-width="4" marker-end="url(#arrow)"/>
  <text x="235" y="365" text-anchor="middle" font-size="11" fill="#10b981">resolve()</text>
  
  <!-- Estado Fulfilled -->
  <circle cx="320" cy="350" r="40" fill="url(#fulfilledGradient)" stroke="#10b981" stroke-width="3" filter="url(#shadow)"/>
  <text x="320" y="345" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">FULFILLED</text>
  <text x="320" y="360" text-anchor="middle" font-size="10" fill="#fff">Éxito</text>
  
  <!-- Flecha a Rejected -->
  <line x1="190" y1="420" x2="280" y2="440" stroke="#ef4444" stroke-width="4" marker-end="url(#arrow)"/>
  <text x="235" y="435" text-anchor="middle" font-size="11" fill="#ef4444">reject()</text>
  
  <!-- Estado Rejected -->
  <circle cx="320" cy="450" r="40" fill="url(#rejectedGradient)" stroke="#ef4444" stroke-width="3" filter="url(#shadow)"/>
  <text x="320" y="445" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">REJECTED</text>
  <text x="320" y="460" text-anchor="middle" font-size="10" fill="#fff">Error</text>
  
  <!-- Handlers -->
  <rect x="420" y="320" width="120" height="60" fill="#dcfce7" stroke="#16a34a" stroke-width="2" rx="5"/>
  <text x="480" y="340" text-anchor="middle" font-size="12" font-weight="bold" fill="#16a34a">.then()</text>
  <text x="480" y="355" text-anchor="middle" font-size="10" fill="#16a34a">Success Handler</text>
  <text x="480" y="370" text-anchor="middle" font-size="10" fill="#16a34a">Recibe el valor</text>
  
  <rect x="420" y="400" width="120" height="60" fill="#fee2e2" stroke="#dc2626" stroke-width="2" rx="5"/>
  <text x="480" y="420" text-anchor="middle" font-size="12" font-weight="bold" fill="#dc2626">.catch()</text>
  <text x="480" y="435" text-anchor="middle" font-size="10" fill="#dc2626">Error Handler</text>
  <text x="480" y="450" text-anchor="middle" font-size="10" fill="#dc2626">Recibe el error</text>
  
  <!-- Finally -->
  <rect x="560" y="360" width="120" height="60" fill="#e0e7ff" stroke="#3b82f6" stroke-width="2" rx="5"/>
  <text x="620" y="380" text-anchor="middle" font-size="12" font-weight="bold" fill="#1e40af">.finally()</text>
  <text x="620" y="395" text-anchor="middle" font-size="10" fill="#1e40af">Cleanup Handler</text>
  <text x="620" y="410" text-anchor="middle" font-size="10" fill="#1e40af">Siempre ejecuta</text>
  
  <!-- Flechas a handlers -->
  <line x1="360" y1="350" x2="420" y2="350" stroke="#10b981" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="360" y1="450" x2="420" y2="430" stroke="#ef4444" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="540" y1="390" x2="560" y2="390" stroke="#3b82f6" stroke-width="3" marker-end="url(#arrow)"/>
  
  <!-- Características Clave -->
  <rect x="700" y="320" width="200" height="180" fill="#f8fafc" stroke="#64748b" stroke-width="2" rx="8"/>
  <text x="800" y="340" text-anchor="middle" font-size="14" font-weight="bold" fill="#374151">Características Clave</text>
  
  <text x="710" y="360" font-size="11" fill="#374151">✓ Inmutable una vez resuelta</text>
  <text x="710" y="375" font-size="11" fill="#374171">✓ Chainable (.then().catch())</text>
  <text x="710" y="390" font-size="11" fill="#374151">✓ Composable (Promise.all)</text>
  <text x="710" y="405" font-size="11" fill="#374151">✓ Error propagation</text>
  <text x="710" y="420" font-size="11" fill="#374151">✓ Async/await compatible</text>
  <text x="710" y="435" font-size="11" fill="#374151">✓ Microtask scheduling</text>
  <text x="710" y="450" font-size="11" fill="#374151">✓ Memory efficient</text>
  <text x="710" y="465" font-size="11" fill="#374151">✓ Thenable interface</text>
  <text x="710" y="480" font-size="11" fill="#374151">✓ Race conditions safe</text>
  
  <!-- Conexiones entre componentes -->
  <line x1="280" y1="160" x2="320" y2="160" stroke="#374151" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="600" y1="200" x2="640" y2="200" stroke="#374151" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Etiquetas de conexión -->
  <text x="300" y="155" text-anchor="middle" font-size="9" fill="#64748b">produce</text>
  <text x="620" y="195" text-anchor="middle" font-size="9" fill="#64748b">consume</text>
  
  <!-- Timeline -->
  <rect x="80" y="540" width="840" height="80" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="2" rx="8"/>
  <text x="90" y="560" font-size="14" font-weight="bold" fill="#0c4a6e">Timeline de Ejecución</text>
  
  <text x="90" y="580" font-size="11" fill="#0c4a6e">1. new Promise() → Estado PENDING</text>
  <text x="90" y="595" font-size="11" fill="#0c4a6e">2. Executor function ejecuta asíncronamente</text>
  <text x="90" y="610" font-size="11" fill="#0c4a6e">3. resolve() o reject() cambia estado a FULFILLED/REJECTED</text>
  
  <text x="500" y="580" font-size="11" fill="#0c4a6e">4. .then()/.catch() handlers se ejecutan</text>
  <text x="500" y="595" font-size="11" fill="#0c4a6e">5. .finally() siempre ejecuta para cleanup</text>
  <text x="500" y="610" font-size="11" fill="#0c4a6e">6. Promise queda inmutable en estado final</text>
</svg>
