/**
 * Ejemplos Básicos de Promesas en OOP
 * 
 * Este archivo contiene ejemplos progresivos del uso de promesas
 * en programación orientada a objetos, desde conceptos básicos
 * hasta implementaciones más sofisticadas.
 */

const AdvancedPromiseManager = require('../../CODIGO/promesas/PromiseManager');

// ===== EJEMPLO 1: PROMESAS BÁSICAS EN CLASES =====

/**
 * Clase que demuestra el uso básico de promesas en OOP
 *
 * Esta clase implementa los patrones fundamentales:
 * - Producer Pattern: Métodos que crean y retornan promesas
 * - Consumer Pattern: Métodos que consumen promesas con async/await
 * - State Management: Tracking del estado de operaciones asíncronas
 */
class BasicPromiseExample {
    constructor() {
        // Estado interno para tracking de operaciones asíncronas
        this.data = null;           // Datos obtenidos de la última operación
        this.isLoading = false;     // Flag de operación en progreso
        this.loadHistory = [];      // Historial de operaciones para debugging
        this.errorCount = 0;        // Contador de errores para métricas
    }

    /**
     * Método que retorna una promesa simple - PRODUCER PATTERN
     *
     * Este método simula una operación asíncrona típica como:
     * - Llamada a API REST
     * - Query a base de datos
     * - Lectura de archivo
     * - Operación de red
     *
     * @param {number} userId - ID del usuario a obtener
     * @returns {Promise<Object>} Promesa que resuelve con datos del usuario
     */
    fetchUserData(userId) {
        console.log(`🔄 [${new Date().toISOString()}] Iniciando fetch para usuario ${userId}`);

        return new Promise((resolve, reject) => {
            // Simular delay de red variable (500-2500ms)
            const networkDelay = Math.random() * 2000 + 500;

            setTimeout(() => {
                if (userId > 0) {
                    // CASO EXITOSO: Resolver con objeto estructurado
                    const userData = {
                        id: userId,
                        name: `Usuario ${userId}`,
                        email: `user${userId}@example.com`,
                        createdAt: new Date().toISOString(),
                        lastLogin: new Date(Date.now() - Math.random() * 86400000).toISOString(),
                        status: 'active',
                        networkDelay: Math.round(networkDelay)
                    };

                    console.log(`✅ [${new Date().toISOString()}] Usuario ${userId} obtenido en ${Math.round(networkDelay)}ms`);
                    resolve(userData);
                } else {
                    // CASO ERROR: Rechazar con error descriptivo
                    const error = new Error(`ID de usuario inválido: ${userId}. Debe ser mayor a 0`);
                    error.code = 'INVALID_USER_ID';
                    error.userId = userId;

                    console.log(`❌ [${new Date().toISOString()}] Error: ${error.message}`);
                    reject(error);
                }
            }, networkDelay);
        });
    }
    
    /**
     * Método que consume promesas - CONSUMER PATTERN
     *
     * Demuestra el uso de async/await para manejar promesas de forma síncrona.
     * Incluye manejo completo de estados y errores.
     *
     * @param {number} userId - ID del usuario a cargar
     * @returns {Promise<Object>} Datos del usuario cargado
     */
    async loadUser(userId) {
        // PASO 1: Actualizar estado antes de operación asíncrona
        this.isLoading = true;
        const startTime = Date.now();

        // Registrar intento en historial para debugging
        const attempt = {
            userId,
            startTime,
            status: 'pending'
        };
        this.loadHistory.push(attempt);

        try {
            console.log(`🔄 [${new Date().toISOString()}] Iniciando carga de usuario ${userId}...`);

            // PASO 2: Await convierte la promesa en valor síncrono
            // La ejecución se pausa aquí hasta que fetchUserData se resuelve o rechaza
            this.data = await this.fetchUserData(userId);

            // PASO 3: Código que se ejecuta solo si la promesa se resuelve exitosamente
            const loadTime = Date.now() - startTime;
            attempt.status = 'success';
            attempt.loadTime = loadTime;
            attempt.endTime = Date.now();

            console.log(`✅ [${new Date().toISOString()}] Usuario cargado exitosamente: ${this.data.name} (${loadTime}ms)`);

            return this.data;

        } catch (error) {
            // PASO 4: Manejo de errores si la promesa se rechaza
            const loadTime = Date.now() - startTime;
            this.errorCount++;

            // Actualizar historial con información del error
            attempt.status = 'error';
            attempt.error = error.message;
            attempt.errorCode = error.code;
            attempt.loadTime = loadTime;
            attempt.endTime = Date.now();

            console.error(`❌ [${new Date().toISOString()}] Error cargando usuario ${userId}: ${error.message} (${loadTime}ms)`);

            // Re-lanzar error para que el caller pueda manejarlo
            throw error;

        } finally {
            // PASO 5: Cleanup que SIEMPRE se ejecuta (éxito o error)
            this.isLoading = false;
            console.log(`🏁 [${new Date().toISOString()}] Operación finalizada para usuario ${userId}`);
        }
    }
    
    // Método que maneja múltiples promesas en paralelo
    async loadMultipleUsers(userIds) {
        console.log(`🔄 Cargando ${userIds.length} usuarios en paralelo...`);
        
        try {
            const promises = userIds.map(id => this.fetchUserData(id));
            const users = await Promise.all(promises);
            
            console.log(`✅ ${users.length} usuarios cargados exitosamente`);
            return users;
        } catch (error) {
            console.error(`❌ Error en carga paralela:`, error.message);
            throw error;
        }
    }
    
    // Método que maneja promesas con Promise.allSettled
    async loadUsersWithErrorHandling(userIds) {
        console.log(`🔄 Cargando usuarios con manejo de errores...`);
        
        const promises = userIds.map(async (id) => {
            try {
                const user = await this.fetchUserData(id);
                return { status: 'fulfilled', value: user, id };
            } catch (error) {
                return { status: 'rejected', reason: error.message, id };
            }
        });
        
        const results = await Promise.allSettled(promises);
        
        const successful = results.filter(r => r.status === 'fulfilled');
        const failed = results.filter(r => r.status === 'rejected');
        
        console.log(`✅ Exitosos: ${successful.length}, ❌ Fallidos: ${failed.length}`);
        
        return {
            successful: successful.map(r => r.value),
            failed: failed.map(r => ({ id: r.reason.id, error: r.reason })),
            summary: {
                total: userIds.length,
                success: successful.length,
                failed: failed.length
            }
        };
    }
}

// ===== EJEMPLO 2: PROMESAS CON TIMEOUT Y RETRY =====

class RobustPromiseExample {
    constructor() {
        this.maxRetries = 3;
        this.timeoutMs = 5000;
    }
    
    // Simulación de API que puede fallar
    unreliableApiCall(endpoint, failureRate = 0.3) {
        return new Promise((resolve, reject) => {
            const delay = Math.random() * 3000 + 500;
            
            setTimeout(() => {
                if (Math.random() < failureRate) {
                    reject(new Error(`API Error: ${endpoint} failed`));
                } else {
                    resolve({
                        endpoint,
                        data: `Datos de ${endpoint}`,
                        timestamp: Date.now(),
                        delay: Math.round(delay)
                    });
                }
            }, delay);
        });
    }
    
    // Método con timeout manual
    async callWithTimeout(endpoint) {
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`Timeout: ${endpoint} tardó más de ${this.timeoutMs}ms`));
            }, this.timeoutMs);
        });
        
        const apiPromise = this.unreliableApiCall(endpoint);
        
        try {
            const result = await Promise.race([apiPromise, timeoutPromise]);
            console.log(`✅ ${endpoint} completado en ${result.delay}ms`);
            return result;
        } catch (error) {
            console.error(`❌ ${endpoint} falló:`, error.message);
            throw error;
        }
    }
    
    // Método con retry automático
    async callWithRetry(endpoint, attempt = 1) {
        try {
            console.log(`🔄 Intento ${attempt} para ${endpoint}`);
            const result = await this.callWithTimeout(endpoint);
            
            if (attempt > 1) {
                console.log(`✅ ${endpoint} exitoso después de ${attempt} intentos`);
            }
            
            return result;
        } catch (error) {
            if (attempt < this.maxRetries) {
                const delay = 1000 * Math.pow(2, attempt - 1); // Backoff exponencial
                console.log(`⏳ Reintentando ${endpoint} en ${delay}ms...`);
                
                await new Promise(resolve => setTimeout(resolve, delay));
                return this.callWithRetry(endpoint, attempt + 1);
            } else {
                console.error(`💥 ${endpoint} falló después de ${this.maxRetries} intentos`);
                throw error;
            }
        }
    }
}

// ===== EJEMPLO 3: USANDO EL PROMISE MANAGER AVANZADO =====

class AdvancedExample {
    constructor() {
        this.promiseManager = new AdvancedPromiseManager({
            maxConcurrency: 3,
            defaultTimeout: 5000,
            retryAttempts: 2
        });
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        this.promiseManager.on('promiseCreated', (data) => {
            console.log(`✨ Promesa creada: ${data.id}`);
        });
        
        this.promiseManager.on('promiseResolved', (data) => {
            console.log(`✅ Promesa resuelta: ${data.id} en ${data.executionTime}ms`);
        });
        
        this.promiseManager.on('promiseRejected', (data) => {
            console.log(`❌ Promesa falló: ${data.id} - ${data.error}`);
        });
        
        this.promiseManager.on('promiseRetry', (data) => {
            console.log(`🔄 Retry ${data.attempt} para promesa: ${data.id}`);
        });
    }
    
    // Ejemplo con el promise manager
    async demonstratePromiseManager() {
        console.log('\n=== DEMO: Promise Manager Avanzado ===');
        
        try {
            // Crear promesa con configuración personalizada
            const result1 = await this.promiseManager.createPromise((resolve, reject) => {
                setTimeout(() => {
                    if (Math.random() > 0.3) {
                        resolve('¡Operación exitosa!');
                    } else {
                        reject(new Error('Operación falló'));
                    }
                }, 1000);
            }, {
                retryAttempts: 3,
                timeout: 10000,
                tags: ['demo', 'test']
            });
            
            console.log('Resultado 1:', result1);
            
            // Ejecutar múltiples promesas con control de concurrencia
            const promiseFactories = Array.from({ length: 8 }, (_, i) => 
                () => new Promise(resolve => {
                    setTimeout(() => {
                        resolve(`Tarea ${i + 1} completada`);
                    }, Math.random() * 2000);
                })
            );
            
            const concurrentResult = await this.promiseManager.executeConcurrent(promiseFactories, {
                concurrency: 3,
                tags: ['concurrent', 'demo']
            });
            
            console.log('Resultado concurrente:', concurrentResult.summary);
            
            // Mostrar métricas
            console.log('Métricas finales:', this.promiseManager.getMetrics());
            
        } catch (error) {
            console.error('Error en demo:', error.message);
        }
    }
    
    // Cleanup
    destroy() {
        this.promiseManager.destroy();
    }
}

// ===== EJECUCIÓN DE EJEMPLOS =====

async function runExamples() {
    console.log('🚀 Iniciando ejemplos de promesas...\n');
    
    // Ejemplo 1: Promesas básicas
    console.log('=== EJEMPLO 1: Promesas Básicas ===');
    const basicExample = new BasicPromiseExample();
    
    try {
        // Cargar un usuario
        await basicExample.loadUser(1);
        
        // Cargar múltiples usuarios en paralelo
        await basicExample.loadMultipleUsers([1, 2, 3]);
        
        // Cargar con manejo de errores
        const result = await basicExample.loadUsersWithErrorHandling([1, -1, 2, -2, 3]);
        console.log('Resumen:', result.summary);
        
    } catch (error) {
        console.error('Error en ejemplo básico:', error.message);
    }
    
    // Ejemplo 2: Promesas robustas
    console.log('\n=== EJEMPLO 2: Promesas Robustas ===');
    const robustExample = new RobustPromiseExample();
    
    try {
        await robustExample.callWithRetry('users');
        await robustExample.callWithRetry('products');
    } catch (error) {
        console.error('Error en ejemplo robusto:', error.message);
    }
    
    // Ejemplo 3: Promise Manager avanzado
    console.log('\n=== EJEMPLO 3: Promise Manager Avanzado ===');
    const advancedExample = new AdvancedExample();
    
    try {
        await advancedExample.demonstratePromiseManager();
    } catch (error) {
        console.error('Error en ejemplo avanzado:', error.message);
    } finally {
        advancedExample.destroy();
    }
    
    console.log('\n✅ Todos los ejemplos completados!');
}

// Ejecutar si es el archivo principal
if (require.main === module) {
    runExamples().catch(console.error);
}

module.exports = {
    BasicPromiseExample,
    RobustPromiseExample,
    AdvancedExample
};
