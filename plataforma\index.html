<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Curso Completo de JavaScript - Maestría Profesional</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        /* CSS integrado para funcionamiento inmediato */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #0a0a0a;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(10px);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            border-bottom: 1px solid #333;
        }

        .header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #f7df1e;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav {
            display: flex;
            gap: 2rem;
        }

        .nav-link {
            color: #fff;
            text-decoration: none;
            transition: color 0.3s;
        }

        .nav-link:hover,
        .nav-link.active {
            color: #f7df1e;
        }

        .btn-primary {
            background: linear-gradient(135deg, #f7df1e, #ffeb3b);
            color: #000;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(247, 223, 30, 0.3);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding-top: 80px;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23333" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.1;
        }

        .hero .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            color: #fff;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .highlight {
            background: linear-gradient(135deg, #f7df1e, #ffeb3b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-description {
            font-size: 1.25rem;
            color: #ccc;
            margin-bottom: 2rem;
        }

        .hero-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat {
            text-align: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #f7df1e;
        }

        .stat-label {
            color: #ccc;
            font-size: 0.9rem;
        }

        .hero-actions {
            display: flex;
            gap: 1rem;
        }

        .btn-large {
            padding: 1rem 2rem;
            font-size: 1.1rem;
        }

        .btn-secondary {
            background: transparent;
            color: #fff;
            border: 2px solid #f7df1e;
        }

        .btn-secondary:hover {
            background: #f7df1e;
            color: #000;
        }

        .code-preview {
            background: #1e1e1e;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .code-header {
            background: #2d2d2d;
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .code-dots {
            display: flex;
            gap: 0.5rem;
        }

        .code-dots span {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #666;
        }

        .code-dots span:nth-child(1) { background: #ff5f56; }
        .code-dots span:nth-child(2) { background: #ffbd2e; }
        .code-dots span:nth-child(3) { background: #27ca3f; }

        .code-title {
            color: #ccc;
            font-size: 0.9rem;
        }

        pre {
            margin: 0;
            padding: 1.5rem;
            overflow-x: auto;
        }

        /* Course Overview */
        .course-overview {
            background: #111;
            padding: 5rem 0;
        }

        .section-title {
            font-size: 2.5rem;
            color: #fff;
            text-align: center;
            margin-bottom: 1rem;
        }

        .section-description {
            color: #ccc;
            text-align: center;
            font-size: 1.1rem;
            margin-bottom: 3rem;
        }

        .course-parts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .part-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s;
            cursor: pointer;
        }

        .part-card:hover {
            transform: translateY(-5px);
            border-color: #f7df1e;
            box-shadow: 0 10px 30px rgba(247, 223, 30, 0.1);
        }

        .part-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .part-number {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #f7df1e, #ffeb3b);
            color: #000;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .part-info h3 {
            color: #fff;
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .part-info p {
            color: #ccc;
            font-size: 0.9rem;
        }

        .part-difficulty {
            margin-left: auto;
            font-size: 1.2rem;
        }

        .part-description {
            color: #ccc;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .part-progress {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .progress-bar {
            flex: 1;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #f7df1e, #ffeb3b);
            transition: width 0.3s;
        }

        .progress-text {
            color: #ccc;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .view-all-parts {
            text-align: center;
        }

        .btn-outline {
            background: transparent;
            color: #f7df1e;
            border: 2px solid #f7df1e;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-outline:hover {
            background: #f7df1e;
            color: #000;
        }

        /* Learning Paths */
        .learning-paths {
            background: #0a0a0a;
            padding: 5rem 0;
        }

        .paths-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .path-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s;
        }

        .path-card.featured {
            border-color: #f7df1e;
            background: rgba(247, 223, 30, 0.05);
        }

        .path-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .path-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .path-card h3 {
            color: #fff;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .path-duration {
            color: #f7df1e;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .path-description {
            color: #ccc;
            margin-bottom: 1.5rem;
        }

        .path-features {
            list-style: none;
            margin-bottom: 2rem;
        }

        .path-features li {
            color: #ccc;
            padding: 0.25rem 0;
            position: relative;
            padding-left: 1.5rem;
        }

        .path-features li::before {
            content: '✓';
            color: #f7df1e;
            position: absolute;
            left: 0;
        }

        /* Features */
        .features {
            background: #111;
            padding: 5rem 0;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            border-color: #f7df1e;
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #f7df1e, #ffeb3b);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: #000;
        }

        .feature-card h3 {
            color: #fff;
            font-size: 1.3rem;
            margin-bottom: 1rem;
        }

        .feature-card p {
            color: #ccc;
            line-height: 1.6;
        }

        /* Footer */
        .footer {
            background: #000;
            padding: 3rem 0 1rem;
            border-top: 1px solid #333;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3,
        .footer-section h4 {
            color: #fff;
            margin-bottom: 1rem;
        }

        .footer-section p {
            color: #ccc;
            margin-bottom: 1rem;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 0.5rem;
        }

        .footer-section ul li a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-section ul li a:hover {
            color: #f7df1e;
        }

        .social-links {
            display: flex;
            gap: 1rem;
        }

        .social-links a {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ccc;
            transition: all 0.3s;
        }

        .social-links a:hover {
            background: #f7df1e;
            color: #000;
        }

        .footer-bottom {
            border-top: 1px solid #333;
            padding-top: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #ccc;
        }

        .footer-links {
            display: flex;
            gap: 2rem;
        }

        .footer-links a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: #f7df1e;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero .container {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .hero-actions {
                flex-direction: column;
            }

            .nav {
                display: none;
            }

            .footer-bottom {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <i class="fab fa-js-square"></i>
                <span>JavaScript Maestría</span>
            </div>
            <nav class="nav">
                <a href="#inicio" class="nav-link active">Inicio</a>
                <a href="#curso" class="nav-link">Curso</a>
                <a href="#progreso" class="nav-link">Progreso</a>
                <a href="#comunidad" class="nav-link">Comunidad</a>
            </nav>
            <div class="user-menu">
                <button class="btn-primary" id="loginBtn">Iniciar Sesión</button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="inicio" class="hero">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    Curso Completo de JavaScript
                    <span class="highlight">Maestría Profesional</span>
                </h1>
                <p class="hero-description">
                    Desde principiante absoluto hasta experto en JavaScript. 
                    95 capítulos, 475+ temas, 1000+ horas de contenido premium.
                </p>
                <div class="hero-stats">
                    <div class="stat">
                        <div class="stat-number">95</div>
                        <div class="stat-label">Capítulos</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">475+</div>
                        <div class="stat-label">Temas</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">1000+</div>
                        <div class="stat-label">Horas</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">285+</div>
                        <div class="stat-label">Proyectos</div>
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="btn-primary btn-large" id="startCourseBtn">
                        <i class="fas fa-play"></i>
                        Comenzar Curso
                    </button>
                    <button class="btn-secondary btn-large" id="previewBtn">
                        <i class="fas fa-eye"></i>
                        Vista Previa
                    </button>
                </div>
            </div>
            <div class="hero-visual">
                <div class="code-preview">
                    <div class="code-header">
                        <div class="code-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <span class="code-title">javascript-mastery.js</span>
                    </div>
                    <pre><code class="language-javascript">// ¡Bienvenido al Curso de JavaScript!
class JavaScriptMaster {
    constructor() {
        this.level = 'Principiante';
        this.knowledge = [];
        this.projects = [];
    }
    
    async learn(topic) {
        console.log(`Aprendiendo ${topic}...`);
        this.knowledge.push(topic);
        await this.practice(topic);
        this.levelUp();
    }
    
    practice(topic) {
        return new Promise(resolve => {
            setTimeout(() => {
                console.log(`¡${topic} dominado!`);
                resolve();
            }, 1000);
        });
    }
    
    levelUp() {
        if (this.knowledge.length > 50) {
            this.level = 'Experto';
        } else if (this.knowledge.length > 20) {
            this.level = 'Intermedio';
        }
    }
}

const student = new JavaScriptMaster();
student.learn('Variables y Tipos');
student.learn('Funciones Avanzadas');
student.learn('Programación Asíncrona');

// ¡Tu viaje hacia la maestría comienza aquí! 🚀</code></pre>
                </div>
            </div>
        </div>
    </section>

    <!-- Course Overview -->
    <section id="curso" class="course-overview">
        <div class="container">
            <h2 class="section-title">Estructura del Curso</h2>
            <p class="section-description">
                15 partes cuidadosamente diseñadas para llevarte desde principiante hasta experto
            </p>
            
            <div class="course-parts">
                <div class="part-card" data-part="1">
                    <div class="part-header">
                        <div class="part-number">I</div>
                        <div class="part-info">
                            <h3>Fundamentos Básicos</h3>
                            <p>14 capítulos • 80-120 horas</p>
                        </div>
                        <div class="part-difficulty">⭐⭐</div>
                    </div>
                    <div class="part-description">
                        Establece bases sólidas en JavaScript, desde configuración hasta funciones fundamentales.
                    </div>
                    <div class="part-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0% completado</span>
                    </div>
                </div>

                <div class="part-card" data-part="2">
                    <div class="part-header">
                        <div class="part-number">II</div>
                        <div class="part-info">
                            <h3>Estructuras de Datos</h3>
                            <p>15 capítulos • 90-130 horas</p>
                        </div>
                        <div class="part-difficulty">⭐⭐⭐</div>
                    </div>
                    <div class="part-description">
                        Domina arrays, objetos, Maps, Sets y estructuras de datos avanzadas.
                    </div>
                    <div class="part-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0% completado</span>
                    </div>
                </div>

                <div class="part-card" data-part="3">
                    <div class="part-header">
                        <div class="part-number">III</div>
                        <div class="part-info">
                            <h3>Funciones Avanzadas</h3>
                            <p>13 capítulos • 80-110 horas</p>
                        </div>
                        <div class="part-difficulty">⭐⭐⭐⭐</div>
                    </div>
                    <div class="part-description">
                        Explora programación funcional, closures, async/await y patrones avanzados.
                    </div>
                    <div class="part-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0% completado</span>
                    </div>
                </div>

                <div class="part-card" data-part="12">
                    <div class="part-header">
                        <div class="part-number">XII</div>
                        <div class="part-info">
                            <h3>Frameworks y Librerías</h3>
                            <p>12 capítulos • 120-160 horas</p>
                        </div>
                        <div class="part-difficulty">⭐⭐⭐⭐</div>
                    </div>
                    <div class="part-description">
                        Domina React, Vue, Angular, Node.js y el ecosistema moderno.
                    </div>
                    <div class="part-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0% completado</span>
                    </div>
                </div>

                <div class="part-card" data-part="14">
                    <div class="part-header">
                        <div class="part-number">XIV</div>
                        <div class="part-info">
                            <h3>Proyectos Prácticos</h3>
                            <p>10 capítulos • 150-200 horas</p>
                        </div>
                        <div class="part-difficulty">⭐⭐⭐⭐⭐</div>
                    </div>
                    <div class="part-description">
                        Construye aplicaciones reales del mundo profesional.
                    </div>
                    <div class="part-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0% completado</span>
                    </div>
                </div>

                <div class="part-card" data-part="15">
                    <div class="part-header">
                        <div class="part-number">XV</div>
                        <div class="part-info">
                            <h3>JavaScript Avanzado</h3>
                            <p>8 capítulos • 60-80 horas</p>
                        </div>
                        <div class="part-difficulty">⭐⭐⭐⭐⭐</div>
                    </div>
                    <div class="part-description">
                        Explora metaprogramming, WebAssembly, ML, blockchain, AR/VR.
                    </div>
                    <div class="part-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0% completado</span>
                    </div>
                </div>
            </div>

            <div class="view-all-parts">
                <button class="btn-outline" id="viewAllPartsBtn">
                    Ver todas las 15 partes
                    <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- Learning Paths -->
    <section class="learning-paths">
        <div class="container">
            <h2 class="section-title">Rutas de Aprendizaje</h2>
            <p class="section-description">
                Elige la ruta que mejor se adapte a tus objetivos
            </p>
            
            <div class="paths-grid">
                <div class="path-card">
                    <div class="path-icon">🚀</div>
                    <h3>Ruta Frontend</h3>
                    <p class="path-duration">300-400 horas</p>
                    <p class="path-description">
                        Enfoque en desarrollo frontend con React, Vue o Angular
                    </p>
                    <ul class="path-features">
                        <li>Fundamentos sólidos</li>
                        <li>DOM y APIs del navegador</li>
                        <li>Frameworks modernos</li>
                        <li>Proyectos frontend</li>
                    </ul>
                    <button class="btn-outline">Elegir Ruta</button>
                </div>

                <div class="path-card">
                    <div class="path-icon">🔧</div>
                    <h3>Ruta Backend</h3>
                    <p class="path-duration">250-350 horas</p>
                    <p class="path-description">
                        Especialización en desarrollo backend con Node.js
                    </p>
                    <ul class="path-features">
                        <li>Node.js y Express</li>
                        <li>Bases de datos</li>
                        <li>APIs y microservicios</li>
                        <li>DevOps básico</li>
                    </ul>
                    <button class="btn-outline">Elegir Ruta</button>
                </div>

                <div class="path-card featured">
                    <div class="path-icon">🌐</div>
                    <h3>Ruta Full-Stack</h3>
                    <p class="path-duration">600-800 horas</p>
                    <p class="path-description">
                        Desarrollo completo frontend y backend
                    </p>
                    <ul class="path-features">
                        <li>Frontend + Backend</li>
                        <li>Bases de datos</li>
                        <li>Deployment</li>
                        <li>Proyectos completos</li>
                    </ul>
                    <button class="btn-primary">Elegir Ruta</button>
                </div>

                <div class="path-card">
                    <div class="path-icon">🔬</div>
                    <h3>Ruta Experto</h3>
                    <p class="path-duration">1000+ horas</p>
                    <p class="path-description">
                        Maestría completa incluyendo tecnologías avanzadas
                    </p>
                    <ul class="path-features">
                        <li>Curso completo</li>
                        <li>Tecnologías emergentes</li>
                        <li>Arquitecturas avanzadas</li>
                        <li>Contribuciones open source</li>
                    </ul>
                    <button class="btn-outline">Elegir Ruta</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Features -->
    <section class="features">
        <div class="container">
            <h2 class="section-title">¿Por qué elegir nuestro curso?</h2>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3>Contenido Actualizado</h3>
                    <p>Siempre al día con las últimas características de JavaScript y mejores prácticas de la industria.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <h3>Proyectos Reales</h3>
                    <p>Construye aplicaciones del mundo real como e-commerce, redes sociales y sistemas de trading.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Comunidad Activa</h3>
                    <p>Únete a miles de estudiantes, comparte conocimientos y recibe ayuda cuando la necesites.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <h3>Certificación</h3>
                    <p>Obtén certificados verificables que demuestren tu expertise en JavaScript.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Acceso Móvil</h3>
                    <p>Aprende desde cualquier dispositivo, en cualquier momento y lugar.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-infinity"></i>
                    </div>
                    <h3>Acceso de por Vida</h3>
                    <p>Una vez inscrito, tendrás acceso permanente a todo el contenido y actualizaciones.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>JavaScript Maestría</h3>
                    <p>El curso más completo de JavaScript del mundo. Desde principiante hasta experto.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-github"></i></a>
                        <a href="#"><i class="fab fa-discord"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Curso</h4>
                    <ul>
                        <li><a href="#">Contenido</a></li>
                        <li><a href="#">Proyectos</a></li>
                        <li><a href="#">Certificación</a></li>
                        <li><a href="#">Precios</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Soporte</h4>
                    <ul>
                        <li><a href="#">Documentación</a></li>
                        <li><a href="#">Comunidad</a></li>
                        <li><a href="#">FAQ</a></li>
                        <li><a href="#">Contacto</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Recursos</h4>
                    <ul>
                        <li><a href="#">Blog</a></li>
                        <li><a href="#">Tutoriales</a></li>
                        <li><a href="#">Herramientas</a></li>
                        <li><a href="#">Cheat Sheets</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 JavaScript Maestría. Todos los derechos reservados.</p>
                <div class="footer-links">
                    <a href="#">Términos de Uso</a>
                    <a href="#">Política de Privacidad</a>
                    <a href="#">Cookies</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
