# **PATRONES DE DISEÑO EN JAVASCRIPT**

## **🏗️ PATRONES CREACIONALES**

### **1. Singleton**
Garantiza que una clase tenga una sola instancia y proporciona acceso global a ella.

```javascript
class Singleton {
    constructor() {
        if (Singleton.instance) {
            return Singleton.instance;
        }
        
        this.data = [];
        Singleton.instance = this;
        return this;
    }
    
    addData(item) {
        this.data.push(item);
    }
    
    getData() {
        return this.data;
    }
}

// Uso
const instance1 = new Singleton();
const instance2 = new Singleton();
console.log(instance1 === instance2); // true
```

### **2. Factory**
Crea objetos sin especificar la clase exacta a crear.

```javascript
class CarFactory {
    static createCar(type) {
        switch (type) {
            case 'sedan':
                return new Sedan();
            case 'suv':
                return new SUV();
            case 'hatchback':
                return new Hatchback();
            default:
                throw new Error('Tipo de carro no válido');
        }
    }
}

class Sedan {
    constructor() {
        this.type = 'sedan';
        this.doors = 4;
    }
}

class SUV {
    constructor() {
        this.type = 'suv';
        this.doors = 5;
    }
}

// Uso
const miCarro = CarFactory.createCar('sedan');
```

### **3. Builder**
Construye objetos complejos paso a paso.

```javascript
class PizzaBuilder {
    constructor() {
        this.pizza = {};
    }
    
    setSize(size) {
        this.pizza.size = size;
        return this;
    }
    
    addTopping(topping) {
        this.pizza.toppings = this.pizza.toppings || [];
        this.pizza.toppings.push(topping);
        return this;
    }
    
    setCrust(crust) {
        this.pizza.crust = crust;
        return this;
    }
    
    build() {
        return this.pizza;
    }
}

// Uso
const pizza = new PizzaBuilder()
    .setSize('large')
    .setCrust('thin')
    .addTopping('pepperoni')
    .addTopping('mushrooms')
    .build();
```

## **🔧 PATRONES ESTRUCTURALES**

### **4. Adapter**
Permite que interfaces incompatibles trabajen juntas.

```javascript
// API antigua
class OldAPI {
    getData() {
        return 'data from old API';
    }
}

// API nueva
class NewAPI {
    fetchData() {
        return 'data from new API';
    }
}

// Adapter
class APIAdapter {
    constructor(api) {
        this.api = api;
    }
    
    getData() {
        if (this.api instanceof OldAPI) {
            return this.api.getData();
        } else if (this.api instanceof NewAPI) {
            return this.api.fetchData();
        }
    }
}

// Uso
const oldAPI = new OldAPI();
const newAPI = new NewAPI();

const adapter1 = new APIAdapter(oldAPI);
const adapter2 = new APIAdapter(newAPI);

console.log(adapter1.getData()); // "data from old API"
console.log(adapter2.getData()); // "data from new API"
```

### **5. Decorator**
Añade funcionalidad a objetos dinámicamente.

```javascript
class Coffee {
    cost() {
        return 5;
    }
    
    description() {
        return 'Simple coffee';
    }
}

class MilkDecorator {
    constructor(coffee) {
        this.coffee = coffee;
    }
    
    cost() {
        return this.coffee.cost() + 2;
    }
    
    description() {
        return this.coffee.description() + ', milk';
    }
}

class SugarDecorator {
    constructor(coffee) {
        this.coffee = coffee;
    }
    
    cost() {
        return this.coffee.cost() + 1;
    }
    
    description() {
        return this.coffee.description() + ', sugar';
    }
}

// Uso
let coffee = new Coffee();
coffee = new MilkDecorator(coffee);
coffee = new SugarDecorator(coffee);

console.log(coffee.description()); // "Simple coffee, milk, sugar"
console.log(coffee.cost()); // 8
```

### **6. Facade**
Proporciona una interfaz simplificada a un subsistema complejo.

```javascript
class CPU {
    freeze() { console.log('CPU: freeze'); }
    jump(position) { console.log(`CPU: jump to ${position}`); }
    execute() { console.log('CPU: execute'); }
}

class Memory {
    load(position, data) {
        console.log(`Memory: load ${data} at ${position}`);
    }
}

class HardDrive {
    read(lba, size) {
        console.log(`HardDrive: read ${size} from ${lba}`);
        return 'boot data';
    }
}

// Facade
class ComputerFacade {
    constructor() {
        this.cpu = new CPU();
        this.memory = new Memory();
        this.hardDrive = new HardDrive();
    }
    
    start() {
        this.cpu.freeze();
        const bootData = this.hardDrive.read(0, 1024);
        this.memory.load(0, bootData);
        this.cpu.jump(0);
        this.cpu.execute();
    }
}

// Uso
const computer = new ComputerFacade();
computer.start(); // Inicia el proceso complejo con una sola llamada
```

## **🎯 PATRONES COMPORTAMENTALES**

### **7. Observer**
Define una dependencia uno-a-muchos entre objetos.

```javascript
class Subject {
    constructor() {
        this.observers = [];
    }
    
    subscribe(observer) {
        this.observers.push(observer);
    }
    
    unsubscribe(observer) {
        this.observers = this.observers.filter(obs => obs !== observer);
    }
    
    notify(data) {
        this.observers.forEach(observer => observer.update(data));
    }
}

class Observer {
    constructor(name) {
        this.name = name;
    }
    
    update(data) {
        console.log(`${this.name} received: ${data}`);
    }
}

// Uso
const subject = new Subject();
const observer1 = new Observer('Observer 1');
const observer2 = new Observer('Observer 2');

subject.subscribe(observer1);
subject.subscribe(observer2);

subject.notify('Hello observers!');
// Observer 1 received: Hello observers!
// Observer 2 received: Hello observers!
```

### **8. Strategy**
Define una familia de algoritmos y los hace intercambiables.

```javascript
class PaymentStrategy {
    pay(amount) {
        throw new Error('pay method must be implemented');
    }
}

class CreditCardStrategy extends PaymentStrategy {
    constructor(cardNumber) {
        super();
        this.cardNumber = cardNumber;
    }
    
    pay(amount) {
        console.log(`Paid $${amount} with credit card ${this.cardNumber}`);
    }
}

class PayPalStrategy extends PaymentStrategy {
    constructor(email) {
        super();
        this.email = email;
    }
    
    pay(amount) {
        console.log(`Paid $${amount} with PayPal account ${this.email}`);
    }
}

class ShoppingCart {
    constructor() {
        this.items = [];
        this.paymentStrategy = null;
    }
    
    addItem(item) {
        this.items.push(item);
    }
    
    setPaymentStrategy(strategy) {
        this.paymentStrategy = strategy;
    }
    
    checkout() {
        const total = this.items.reduce((sum, item) => sum + item.price, 0);
        this.paymentStrategy.pay(total);
    }
}

// Uso
const cart = new ShoppingCart();
cart.addItem({ name: 'Book', price: 20 });
cart.addItem({ name: 'Pen', price: 5 });

cart.setPaymentStrategy(new CreditCardStrategy('1234-5678-9012-3456'));
cart.checkout(); // Paid $25 with credit card 1234-5678-9012-3456
```

### **9. Command**
Encapsula una petición como un objeto.

```javascript
class Command {
    execute() {
        throw new Error('execute method must be implemented');
    }
    
    undo() {
        throw new Error('undo method must be implemented');
    }
}

class Light {
    constructor() {
        this.isOn = false;
    }
    
    turnOn() {
        this.isOn = true;
        console.log('Light is ON');
    }
    
    turnOff() {
        this.isOn = false;
        console.log('Light is OFF');
    }
}

class LightOnCommand extends Command {
    constructor(light) {
        super();
        this.light = light;
    }
    
    execute() {
        this.light.turnOn();
    }
    
    undo() {
        this.light.turnOff();
    }
}

class LightOffCommand extends Command {
    constructor(light) {
        super();
        this.light = light;
    }
    
    execute() {
        this.light.turnOff();
    }
    
    undo() {
        this.light.turnOn();
    }
}

class RemoteControl {
    constructor() {
        this.commands = [];
        this.currentCommand = 0;
    }
    
    executeCommand(command) {
        this.commands = this.commands.slice(0, this.currentCommand + 1);
        this.commands.push(command);
        this.currentCommand++;
        command.execute();
    }
    
    undo() {
        if (this.currentCommand >= 0) {
            const command = this.commands[this.currentCommand];
            command.undo();
            this.currentCommand--;
        }
    }
}

// Uso
const light = new Light();
const lightOn = new LightOnCommand(light);
const lightOff = new LightOffCommand(light);
const remote = new RemoteControl();

remote.executeCommand(lightOn);  // Light is ON
remote.executeCommand(lightOff); // Light is OFF
remote.undo();                   // Light is ON
```

## **🌐 PATRONES ESPECÍFICOS DE JAVASCRIPT**

### **10. Module Pattern**
Encapsula código y expone solo lo necesario.

```javascript
const MyModule = (function() {
    // Variables privadas
    let privateVariable = 0;
    
    // Función privada
    function privateFunction() {
        console.log('Private function called');
    }
    
    // API pública
    return {
        publicMethod: function() {
            privateVariable++;
            privateFunction();
            return privateVariable;
        },
        
        getCount: function() {
            return privateVariable;
        }
    };
})();

// Uso
console.log(MyModule.publicMethod()); // Private function called, 1
console.log(MyModule.getCount());     // 1
```

### **11. Revealing Module Pattern**
Variación del Module Pattern que define todas las funciones en el scope privado.

```javascript
const RevealingModule = (function() {
    let privateVariable = 0;
    
    function privateFunction() {
        console.log('Private function');
    }
    
    function publicFunction1() {
        privateVariable++;
        privateFunction();
    }
    
    function publicFunction2() {
        return privateVariable;
    }
    
    // Revelar métodos públicos
    return {
        method1: publicFunction1,
        method2: publicFunction2
    };
})();
```

### **12. Mixin Pattern**
Permite que un objeto adquiera propiedades de múltiples fuentes.

```javascript
const CanFly = {
    fly() {
        console.log(`${this.name} is flying`);
    }
};

const CanSwim = {
    swim() {
        console.log(`${this.name} is swimming`);
    }
};

const CanWalk = {
    walk() {
        console.log(`${this.name} is walking`);
    }
};

// Función para aplicar mixins
function mixin(target, ...sources) {
    Object.assign(target.prototype, ...sources);
}

class Duck {
    constructor(name) {
        this.name = name;
    }
}

class Fish {
    constructor(name) {
        this.name = name;
    }
}

// Aplicar mixins
mixin(Duck, CanFly, CanSwim, CanWalk);
mixin(Fish, CanSwim);

// Uso
const duck = new Duck('Donald');
duck.fly();   // Donald is flying
duck.swim();  // Donald is swimming
duck.walk();  // Donald is walking

const fish = new Fish('Nemo');
fish.swim();  // Nemo is swimming
```

### **13. Pub/Sub Pattern**
Sistema de mensajería donde los publishers envían mensajes sin conocer a los subscribers.

```javascript
class PubSub {
    constructor() {
        this.events = {};
    }
    
    subscribe(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
        
        // Retornar función para unsubscribe
        return () => {
            this.events[event] = this.events[event].filter(cb => cb !== callback);
        };
    }
    
    publish(event, data) {
        if (this.events[event]) {
            this.events[event].forEach(callback => callback(data));
        }
    }
}

// Uso
const pubsub = new PubSub();

const unsubscribe1 = pubsub.subscribe('user-login', (user) => {
    console.log(`User ${user.name} logged in`);
});

const unsubscribe2 = pubsub.subscribe('user-login', (user) => {
    console.log(`Welcome ${user.name}!`);
});

pubsub.publish('user-login', { name: 'John' });
// User John logged in
// Welcome John!

unsubscribe1(); // Cancelar primera suscripción
pubsub.publish('user-login', { name: 'Jane' });
// Welcome Jane!
```

Estos patrones de diseño te ayudarán a escribir código más estructurado, mantenible y reutilizable en JavaScript. Cada patrón resuelve problemas específicos y debe usarse cuando sea apropiado para la situación.
