## 4.2.5. Variable declarations

### Introducción
Las declaraciones de variables en JavaScript usan var, let y const.  
Var es hoisted y permite redeclaración.  
Let y const son block-scoped, no hoisted.  
Const previene reasignación.  
Esto afecta el scope y errores comunes.  
¿Usas let o const por defecto?

### Código de Ejemplo
```javascript
// Var declaration
var x = 1;
console.log(x);

// Let declaration
let y = 2;
console.log(y);

// Const declaration
const z = 3;
console.log(z);

// Reasignación
var x = 10; // Permitido
let y = 20; // Permitido
// z = 30; // Error

// Hoisting
console.log(a); // undefined
var a = 5;
// console.log(b); // Error
let b = 6;
```
### Resultados en Consola
- `1`
- `2`
- `3`
- `undefined`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> VarDecl["Paso 1: var x = 1 <br> - Hoisted"]
    VarDecl --> LogVar["Paso 2: console.log(x) <br> - Resultado: 1"]
    LogVar --> LetDecl["Paso 3: let y = 2 <br> - Block-scoped"]
    LetDecl --> LogLet["Paso 4: console.log(y) <br> - Resultado: 2"]
    LogLet --> ConstDecl["Paso 5: const z = 3 <br> - Inmutable"]
    ConstDecl --> LogConst["Paso 6: console.log(z) <br> - Resultado: 3"]
    LogConst --> Reassign["Paso 7: Reasignaciones <br> - Var y let permitidas, const no"]
    Reassign --> Hoist["Paso 8: Hoisting <br> - Var undefined, let error"]
    Hoist --> Fin["Paso 9: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> MejoresPracticas["Mejores prácticas"]
    VarDecl --> NotaVar["Nota: Function-scoped"]
    LetDecl --> NotaLet["Nota: Block-scoped"]
    ConstDecl --> NotaConst["Nota: Inmutable"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style VarDecl fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogVar fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LetDecl fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogLet fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ConstDecl fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogConst fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Reassign fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Hoist fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style MejoresPracticas fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaVar fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaLet fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaConst fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Var["Var <br> - Hoisted, redeclarable"]
        Var --> UsoVar["Uso: Function scope"]
        Inicio --> Let["Let <br> - Block-scoped, reasignable"]
        Let --> UsoLet["Uso: Temporal dead zone"]
        Inicio --> Const["Const <br> - Block-scoped, inmutable"]
        Const --> UsoConst["Uso: Valores constantes"]
        UsoVar --> Diferencias["Diferencias: Scope y hoisting"]
        UsoLet --> Diferencias
        UsoConst --> Diferencias
        Inicio --> Desarrollador(("Desarrollador")) --> Eleccion["Elección basada en uso"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Var fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style UsoVar fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Let fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style UsoLet fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Const fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style UsoConst fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Diferencias fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Eleccion fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
```

### Casos de uso
1. Var para compatibilidad legacy.
2. Let para variables mutables en bloques.
3. Const para constantes y prevención de errores.
4. Let en loops for.
5. Const para imports y configs.

### Errores comunes
1. Usar var en lugar de let/const.
2. Reasignar const.
3. Acceder variables antes de declaración (TDZ).
4. Redeclarar let/const en mismo scope.
5. Ignorar scope en bloques.

### Recomendaciones
1. Prefiere const por defecto.
2. Usa let solo cuando necesites reasignar.
3. Evita var en código moderno.
4. Entiende TDZ para let/const.
5. Usa linters para mejores prácticas.