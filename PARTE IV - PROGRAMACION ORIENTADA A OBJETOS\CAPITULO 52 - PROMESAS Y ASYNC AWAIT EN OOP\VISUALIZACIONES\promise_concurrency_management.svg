<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
  <!-- Definiciones -->
  <defs>
    <!-- Gradientes -->
    <linearGradient id="queueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="activeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="completedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22c55e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#16a34a;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="batchGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    
    <!-- Sombra -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
    
    <!-- Flechas -->
    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
    
    <marker id="queueArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3b82f6"/>
    </marker>
    
    <marker id="activeArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#10b981"/>
    </marker>
  </defs>

  <!-- Título -->
  <text x="500" y="30" text-anchor="middle" font-size="22" font-weight="bold" fill="#1f2937">
    Gestión de Concurrencia y Cola de Promesas
  </text>
  
  <!-- Configuración de Concurrencia -->
  <rect x="50" y="60" width="900" height="80" fill="#f8fafc" stroke="#64748b" stroke-width="2" rx="10"/>
  <text x="70" y="85" font-size="16" font-weight="bold" fill="#374151">Configuración: maxConcurrency = 5</text>
  <text x="70" y="105" font-size="12" fill="#64748b">
    El sistema permite máximo 5 promesas ejecutándose simultáneamente para optimizar recursos
  </text>
  <text x="70" y="125" font-size="12" fill="#64748b">
    Las promesas adicionales esperan en cola hasta que se libere un slot de ejecución
  </text>
  
  <!-- Cola de Promesas Pendientes -->
  <rect x="50" y="160" width="200" height="300" fill="#dbeafe" stroke="#3b82f6" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="150" y="185" text-anchor="middle" font-size="16" font-weight="bold" fill="#1e40af">Cola de Espera</text>
  <text x="150" y="205" text-anchor="middle" font-size="12" fill="#1e40af">(8 promesas)</text>
  
  <!-- Promesas en cola -->
  <rect x="70" y="220" width="160" height="25" fill="#bfdbfe" stroke="#3b82f6" stroke-width="1" rx="5"/>
  <text x="150" y="237" text-anchor="middle" font-size="11" fill="#1e40af">Promise 6 - fetchUserData()</text>
  
  <rect x="70" y="250" width="160" height="25" fill="#bfdbfe" stroke="#3b82f6" stroke-width="1" rx="5"/>
  <text x="150" y="267" text-anchor="middle" font-size="11" fill="#1e40af">Promise 7 - uploadFile()</text>
  
  <rect x="70" y="280" width="160" height="25" fill="#bfdbfe" stroke="#3b82f6" stroke-width="1" rx="5"/>
  <text x="150" y="297" text-anchor="middle" font-size="11" fill="#1e40af">Promise 8 - processImage()</text>
  
  <rect x="70" y="310" width="160" height="25" fill="#bfdbfe" stroke="#3b82f6" stroke-width="1" rx="5"/>
  <text x="150" y="327" text-anchor="middle" font-size="11" fill="#1e40af">Promise 9 - sendEmail()</text>
  
  <rect x="70" y="340" width="160" height="25" fill="#bfdbfe" stroke="#3b82f6" stroke-width="1" rx="5"/>
  <text x="150" y="357" text-anchor="middle" font-size="11" fill="#1e40af">Promise 10 - validateData()</text>
  
  <rect x="70" y="370" width="160" height="25" fill="#bfdbfe" stroke="#3b82f6" stroke-width="1" rx="5"/>
  <text x="150" y="387" text-anchor="middle" font-size="11" fill="#1e40af">Promise 11 - generateReport()</text>
  
  <rect x="70" y="400" width="160" height="25" fill="#bfdbfe" stroke="#3b82f6" stroke-width="1" rx="5"/>
  <text x="150" y="417" text-anchor="middle" font-size="11" fill="#1e40af">Promise 12 - backupData()</text>
  
  <rect x="70" y="430" width="160" height="25" fill="#bfdbfe" stroke="#3b82f6" stroke-width="1" rx="5"/>
  <text x="150" y="447" text-anchor="middle" font-size="11" fill="#1e40af">Promise 13 - syncDatabase()</text>
  
  <!-- Promesas Activas -->
  <rect x="280" y="160" width="200" height="300" fill="#dcfce7" stroke="#16a34a" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="380" y="185" text-anchor="middle" font-size="16" font-weight="bold" fill="#16a34a">Ejecutándose</text>
  <text x="380" y="205" text-anchor="middle" font-size="12" fill="#16a34a">(5/5 slots ocupados)</text>
  
  <!-- Promesas activas con progreso -->
  <rect x="300" y="220" width="160" height="40" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="5"/>
  <text x="380" y="235" text-anchor="middle" font-size="11" font-weight="bold" fill="#14532d">Promise 1 - apiCall()</text>
  <rect x="310" y="245" width="140" height="8" fill="#e5e7eb" rx="4"/>
  <rect x="310" y="245" width="98" height="8" fill="#10b981" rx="4"/>
  <text x="380" y="260" text-anchor="middle" font-size="9" fill="#14532d">70% - 2.1s</text>
  
  <rect x="300" y="270" width="160" height="40" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="5"/>
  <text x="380" y="285" text-anchor="middle" font-size="11" font-weight="bold" fill="#14532d">Promise 2 - dbQuery()</text>
  <rect x="310" y="295" width="140" height="8" fill="#e5e7eb" rx="4"/>
  <rect x="310" y="295" width="42" height="8" fill="#10b981" rx="4"/>
  <text x="380" y="310" text-anchor="middle" font-size="9" fill="#14532d">30% - 0.9s</text>
  
  <rect x="300" y="320" width="160" height="40" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="5"/>
  <text x="380" y="335" text-anchor="middle" font-size="11" font-weight="bold" fill="#14532d">Promise 3 - fileRead()</text>
  <rect x="310" y="345" width="140" height="8" fill="#e5e7eb" rx="4"/>
  <rect x="310" y="345" width="126" height="8" fill="#10b981" rx="4"/>
  <text x="380" y="360" text-anchor="middle" font-size="9" fill="#14532d">90% - 1.8s</text>
  
  <rect x="300" y="370" width="160" height="40" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="5"/>
  <text x="380" y="385" text-anchor="middle" font-size="11" font-weight="bold" fill="#14532d">Promise 4 - httpRequest()</text>
  <rect x="310" y="395" width="140" height="8" fill="#e5e7eb" rx="4"/>
  <rect x="310" y="395" width="84" height="8" fill="#10b981" rx="4"/>
  <text x="380" y="410" text-anchor="middle" font-size="9" fill="#14532d">60% - 1.5s</text>
  
  <rect x="300" y="420" width="160" height="40" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="5"/>
  <text x="380" y="435" text-anchor="middle" font-size="11" font-weight="bold" fill="#14532d">Promise 5 - calculation()</text>
  <rect x="310" y="445" width="140" height="8" fill="#e5e7eb" rx="4"/>
  <rect x="310" y="445" width="14" height="8" fill="#10b981" rx="4"/>
  <text x="380" y="460" text-anchor="middle" font-size="9" fill="#14532d">10% - 0.3s</text>
  
  <!-- Promesas Completadas -->
  <rect x="510" y="160" width="200" height="300" fill="#f0fdf4" stroke="#22c55e" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="610" y="185" text-anchor="middle" font-size="16" font-weight="bold" fill="#16a34a">Completadas</text>
  <text x="610" y="205" text-anchor="middle" font-size="12" fill="#16a34a">(Últimas 10)</text>
  
  <!-- Lista de completadas -->
  <rect x="530" y="220" width="160" height="20" fill="#dcfce7" stroke="#22c55e" stroke-width="1" rx="3"/>
  <text x="610" y="233" text-anchor="middle" font-size="10" fill="#14532d">✓ auth() - 0.8s</text>
  
  <rect x="530" y="245" width="160" height="20" fill="#dcfce7" stroke="#22c55e" stroke-width="1" rx="3"/>
  <text x="610" y="258" text-anchor="middle" font-size="10" fill="#14532d">✓ loadConfig() - 1.2s</text>
  
  <rect x="530" y="270" width="160" height="20" fill="#dcfce7" stroke="#22c55e" stroke-width="1" rx="3"/>
  <text x="610" y="283" text-anchor="middle" font-size="10" fill="#14532d">✓ initDB() - 2.1s</text>
  
  <rect x="530" y="295" width="160" height="20" fill="#dcfce7" stroke="#22c55e" stroke-width="1" rx="3"/>
  <text x="610" y="308" text-anchor="middle" font-size="10" fill="#14532d">✓ setupCache() - 0.5s</text>
  
  <rect x="530" y="320" width="160" height="20" fill="#dcfce7" stroke="#22c55e" stroke-width="1" rx="3"/>
  <text x="610" y="333" text-anchor="middle" font-size="10" fill="#14532d">✓ validateUser() - 1.8s</text>
  
  <!-- Métricas en tiempo real -->
  <rect x="740" y="160" width="200" height="300" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="840" y="185" text-anchor="middle" font-size="16" font-weight="bold" fill="#92400e">Métricas</text>
  
  <text x="750" y="210" font-size="12" font-weight="bold" fill="#92400e">Throughput:</text>
  <text x="750" y="225" font-size="11" fill="#92400e">• 3.2 promesas/segundo</text>
  <text x="750" y="240" font-size="11" fill="#92400e">• Pico: 4.1 promesas/segundo</text>
  
  <text x="750" y="265" font-size="12" font-weight="bold" fill="#92400e">Latencia:</text>
  <text x="750" y="280" font-size="11" fill="#92400e">• Promedio: 1.4s</text>
  <text x="750" y="295" font-size="11" fill="#92400e">• P95: 2.8s</text>
  <text x="750" y="310" font-size="11" fill="#92400e">• P99: 4.2s</text>
  
  <text x="750" y="335" font-size="12" font-weight="bold" fill="#92400e">Utilización:</text>
  <text x="750" y="350" font-size="11" fill="#92400e">• Slots activos: 100%</text>
  <text x="750" y="365" font-size="11" fill="#92400e">• Cola promedio: 6.2</text>
  <text x="750" y="380" font-size="11" fill="#92400e">• Tiempo espera: 2.1s</text>
  
  <text x="750" y="405" font-size="12" font-weight="bold" fill="#92400e">Tasa de éxito:</text>
  <text x="750" y="420" font-size="11" fill="#92400e">• Éxito: 94.2%</text>
  <text x="750" y="435" font-size="11" fill="#92400e">• Timeout: 3.1%</text>
  <text x="750" y="450" font-size="11" fill="#92400e">• Error: 2.7%</text>
  
  <!-- Flechas de flujo -->
  <line x1="250" y1="300" x2="280" y2="300" stroke="#3b82f6" stroke-width="3" marker-end="url(#queueArrow)"/>
  <line x1="480" y1="300" x2="510" y2="300" stroke="#10b981" stroke-width="3" marker-end="url(#activeArrow)"/>
  
  <text x="265" y="295" text-anchor="middle" font-size="10" fill="#3b82f6">siguiente</text>
  <text x="495" y="295" text-anchor="middle" font-size="10" fill="#10b981">completa</text>
  
  <!-- Estrategias de Ejecución -->
  <rect x="50" y="480" width="900" height="280" fill="#f8fafc" stroke="#64748b" stroke-width="2" rx="10"/>
  <text x="70" y="505" font-size="18" font-weight="bold" fill="#374151">Estrategias de Ejecución</text>
  
  <!-- Ejecución Concurrente -->
  <rect x="80" y="520" width="250" height="120" fill="#e0e7ff" stroke="#3b82f6" stroke-width="2" rx="8"/>
  <text x="205" y="540" text-anchor="middle" font-size="14" font-weight="bold" fill="#1e40af">Ejecución Concurrente</text>
  <text x="90" y="560" font-size="11" fill="#1e40af">• Máximo paralelismo</text>
  <text x="90" y="575" font-size="11" fill="#1e40af">• Control de concurrencia</text>
  <text x="90" y="590" font-size="11" fill="#1e40af">• Fail-fast opcional</text>
  <text x="90" y="605" font-size="11" fill="#1e40af">• Ideal para I/O bound</text>
  <text x="90" y="620" font-size="11" fill="#1e40af">• Uso: APIs, archivos, DB</text>
  
  <!-- Ejecución Secuencial -->
  <rect x="350" y="520" width="250" height="120" fill="#dcfce7" stroke="#16a34a" stroke-width="2" rx="8"/>
  <text x="475" y="540" text-anchor="middle" font-size="14" font-weight="bold" fill="#16a34a">Ejecución Secuencial</text>
  <text x="360" y="560" font-size="11" fill="#16a34a">• Una promesa a la vez</text>
  <text x="360" y="575" font-size="11" fill="#16a34a">• Orden garantizado</text>
  <text x="360" y="590" font-size="11" fill="#16a34a">• Control de dependencias</text>
  <text x="360" y="605" font-size="11" fill="#16a34a">• Callback de progreso</text>
  <text x="360" y="620" font-size="11" fill="#16a34a">• Uso: Pipelines, workflows</text>
  
  <!-- Ejecución por Lotes -->
  <rect x="620" y="520" width="250" height="120" fill="#ede9fe" stroke="#8b5cf6" stroke-width="2" rx="8"/>
  <text x="745" y="540" text-anchor="middle" font-size="14" font-weight="bold" fill="#7c3aed">Ejecución por Lotes</text>
  <text x="630" y="560" font-size="11" fill="#7c3aed">• Grupos de concurrencia</text>
  <text x="630" y="575" font-size="11" fill="#7c3aed">• Procesamiento eficiente</text>
  <text x="630" y="590" font-size="11" fill="#7c3aed">• Memoria optimizada</text>
  <text x="630" y="605" font-size="11" fill="#7c3aed">• Rate limiting natural</text>
  <text x="630" y="620" font-size="11" fill="#7c3aed">• Uso: Bulk operations</text>
  
  <!-- Ejemplos de código -->
  <rect x="80" y="660" width="790" height="80" fill="#f1f5f9" stroke="#64748b" stroke-width="1" rx="5"/>
  <text x="90" y="680" font-size="12" font-weight="bold" fill="#374151">Ejemplos de Uso:</text>
  
  <text x="90" y="700" font-size="10" font-family="monospace" fill="#374151">
    // Concurrente: await manager.executeConcurrent(tasks, {concurrency: 5})
  </text>
  <text x="90" y="715" font-size="10" font-family="monospace" fill="#374151">
    // Secuencial: await manager.executeSequential(tasks, {onProgress: callback})
  </text>
  <text x="90" y="730" font-size="10" font-family="monospace" fill="#374151">
    // Por lotes: await manager.executeConcurrent(tasks, {concurrency: 3, failFast: false})
  </text>
  
  <!-- Indicadores de estado -->
  <circle cx="70" cy="240" r="5" fill="#3b82f6"/>
  <text x="85" y="245" font-size="9" fill="#3b82f6">En cola</text>
  
  <circle cx="70" cy="260" r="5" fill="#10b981"/>
  <text x="85" y="265" font-size="9" fill="#10b981">Ejecutando</text>
  
  <circle cx="70" cy="280" r="5" fill="#22c55e"/>
  <text x="85" y="285" font-size="9" fill="#22c55e">Completada</text>
  
  <circle cx="170" cy="240" r="5" fill="#ef4444"/>
  <text x="185" y="245" font-size="9" fill="#ef4444">Error</text>
  
  <circle cx="170" cy="260" r="5" fill="#f59e0b"/>
  <text x="185" y="265" font-size="9" fill="#f59e0b">Timeout</text>
  
  <circle cx="170" cy="280" r="5" fill="#8b5cf6"/>
  <text x="185" y="285" font-size="9" fill="#8b5cf6">Retry</text>
</svg>
