<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Curso Completo de JavaScript - Maestría Profesional</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <i class="fab fa-js-square"></i>
                <span>JavaScript Maestría</span>
            </div>
            <nav class="nav">
                <a href="#inicio" class="nav-link active">Inicio</a>
                <a href="#curso" class="nav-link">Curso</a>
                <a href="#progreso" class="nav-link">Progreso</a>
                <a href="#comunidad" class="nav-link">Comunidad</a>
            </nav>
            <div class="user-menu">
                <button class="btn-primary" id="loginBtn">Iniciar Sesión</button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="inicio" class="hero">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    Curso Completo de JavaScript
                    <span class="highlight">Maestría Profesional</span>
                </h1>
                <p class="hero-description">
                    Desde principiante absoluto hasta experto en JavaScript. 
                    95 capítulos, 475+ temas, 1000+ horas de contenido premium.
                </p>
                <div class="hero-stats">
                    <div class="stat">
                        <div class="stat-number">95</div>
                        <div class="stat-label">Capítulos</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">475+</div>
                        <div class="stat-label">Temas</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">1000+</div>
                        <div class="stat-label">Horas</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">285+</div>
                        <div class="stat-label">Proyectos</div>
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="btn-primary btn-large" id="startCourseBtn">
                        <i class="fas fa-play"></i>
                        Comenzar Curso
                    </button>
                    <button class="btn-secondary btn-large" id="previewBtn">
                        <i class="fas fa-eye"></i>
                        Vista Previa
                    </button>
                </div>
            </div>
            <div class="hero-visual">
                <div class="code-preview">
                    <div class="code-header">
                        <div class="code-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <span class="code-title">javascript-mastery.js</span>
                    </div>
                    <pre><code class="language-javascript">// ¡Bienvenido al Curso de JavaScript!
class JavaScriptMaster {
    constructor() {
        this.level = 'Principiante';
        this.knowledge = [];
        this.projects = [];
    }
    
    async learn(topic) {
        console.log(`Aprendiendo ${topic}...`);
        this.knowledge.push(topic);
        await this.practice(topic);
        this.levelUp();
    }
    
    practice(topic) {
        return new Promise(resolve => {
            setTimeout(() => {
                console.log(`¡${topic} dominado!`);
                resolve();
            }, 1000);
        });
    }
    
    levelUp() {
        if (this.knowledge.length > 50) {
            this.level = 'Experto';
        } else if (this.knowledge.length > 20) {
            this.level = 'Intermedio';
        }
    }
}

const student = new JavaScriptMaster();
student.learn('Variables y Tipos');
student.learn('Funciones Avanzadas');
student.learn('Programación Asíncrona');

// ¡Tu viaje hacia la maestría comienza aquí! 🚀</code></pre>
                </div>
            </div>
        </div>
    </section>

    <!-- Course Overview -->
    <section id="curso" class="course-overview">
        <div class="container">
            <h2 class="section-title">Estructura del Curso</h2>
            <p class="section-description">
                15 partes cuidadosamente diseñadas para llevarte desde principiante hasta experto
            </p>
            
            <div class="course-parts">
                <div class="part-card" data-part="1">
                    <div class="part-header">
                        <div class="part-number">I</div>
                        <div class="part-info">
                            <h3>Fundamentos Básicos</h3>
                            <p>14 capítulos • 80-120 horas</p>
                        </div>
                        <div class="part-difficulty">⭐⭐</div>
                    </div>
                    <div class="part-description">
                        Establece bases sólidas en JavaScript, desde configuración hasta funciones fundamentales.
                    </div>
                    <div class="part-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0% completado</span>
                    </div>
                </div>

                <div class="part-card" data-part="2">
                    <div class="part-header">
                        <div class="part-number">II</div>
                        <div class="part-info">
                            <h3>Estructuras de Datos</h3>
                            <p>15 capítulos • 90-130 horas</p>
                        </div>
                        <div class="part-difficulty">⭐⭐⭐</div>
                    </div>
                    <div class="part-description">
                        Domina arrays, objetos, Maps, Sets y estructuras de datos avanzadas.
                    </div>
                    <div class="part-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0% completado</span>
                    </div>
                </div>

                <div class="part-card" data-part="3">
                    <div class="part-header">
                        <div class="part-number">III</div>
                        <div class="part-info">
                            <h3>Funciones Avanzadas</h3>
                            <p>13 capítulos • 80-110 horas</p>
                        </div>
                        <div class="part-difficulty">⭐⭐⭐⭐</div>
                    </div>
                    <div class="part-description">
                        Explora programación funcional, closures, async/await y patrones avanzados.
                    </div>
                    <div class="part-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0% completado</span>
                    </div>
                </div>

                <div class="part-card" data-part="12">
                    <div class="part-header">
                        <div class="part-number">XII</div>
                        <div class="part-info">
                            <h3>Frameworks y Librerías</h3>
                            <p>12 capítulos • 120-160 horas</p>
                        </div>
                        <div class="part-difficulty">⭐⭐⭐⭐</div>
                    </div>
                    <div class="part-description">
                        Domina React, Vue, Angular, Node.js y el ecosistema moderno.
                    </div>
                    <div class="part-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0% completado</span>
                    </div>
                </div>

                <div class="part-card" data-part="14">
                    <div class="part-header">
                        <div class="part-number">XIV</div>
                        <div class="part-info">
                            <h3>Proyectos Prácticos</h3>
                            <p>10 capítulos • 150-200 horas</p>
                        </div>
                        <div class="part-difficulty">⭐⭐⭐⭐⭐</div>
                    </div>
                    <div class="part-description">
                        Construye aplicaciones reales del mundo profesional.
                    </div>
                    <div class="part-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0% completado</span>
                    </div>
                </div>

                <div class="part-card" data-part="15">
                    <div class="part-header">
                        <div class="part-number">XV</div>
                        <div class="part-info">
                            <h3>JavaScript Avanzado</h3>
                            <p>8 capítulos • 60-80 horas</p>
                        </div>
                        <div class="part-difficulty">⭐⭐⭐⭐⭐</div>
                    </div>
                    <div class="part-description">
                        Explora metaprogramming, WebAssembly, ML, blockchain, AR/VR.
                    </div>
                    <div class="part-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0% completado</span>
                    </div>
                </div>
            </div>

            <div class="view-all-parts">
                <button class="btn-outline" id="viewAllPartsBtn">
                    Ver todas las 15 partes
                    <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- Learning Paths -->
    <section class="learning-paths">
        <div class="container">
            <h2 class="section-title">Rutas de Aprendizaje</h2>
            <p class="section-description">
                Elige la ruta que mejor se adapte a tus objetivos
            </p>
            
            <div class="paths-grid">
                <div class="path-card">
                    <div class="path-icon">🚀</div>
                    <h3>Ruta Frontend</h3>
                    <p class="path-duration">300-400 horas</p>
                    <p class="path-description">
                        Enfoque en desarrollo frontend con React, Vue o Angular
                    </p>
                    <ul class="path-features">
                        <li>Fundamentos sólidos</li>
                        <li>DOM y APIs del navegador</li>
                        <li>Frameworks modernos</li>
                        <li>Proyectos frontend</li>
                    </ul>
                    <button class="btn-outline">Elegir Ruta</button>
                </div>

                <div class="path-card">
                    <div class="path-icon">🔧</div>
                    <h3>Ruta Backend</h3>
                    <p class="path-duration">250-350 horas</p>
                    <p class="path-description">
                        Especialización en desarrollo backend con Node.js
                    </p>
                    <ul class="path-features">
                        <li>Node.js y Express</li>
                        <li>Bases de datos</li>
                        <li>APIs y microservicios</li>
                        <li>DevOps básico</li>
                    </ul>
                    <button class="btn-outline">Elegir Ruta</button>
                </div>

                <div class="path-card featured">
                    <div class="path-icon">🌐</div>
                    <h3>Ruta Full-Stack</h3>
                    <p class="path-duration">600-800 horas</p>
                    <p class="path-description">
                        Desarrollo completo frontend y backend
                    </p>
                    <ul class="path-features">
                        <li>Frontend + Backend</li>
                        <li>Bases de datos</li>
                        <li>Deployment</li>
                        <li>Proyectos completos</li>
                    </ul>
                    <button class="btn-primary">Elegir Ruta</button>
                </div>

                <div class="path-card">
                    <div class="path-icon">🔬</div>
                    <h3>Ruta Experto</h3>
                    <p class="path-duration">1000+ horas</p>
                    <p class="path-description">
                        Maestría completa incluyendo tecnologías avanzadas
                    </p>
                    <ul class="path-features">
                        <li>Curso completo</li>
                        <li>Tecnologías emergentes</li>
                        <li>Arquitecturas avanzadas</li>
                        <li>Contribuciones open source</li>
                    </ul>
                    <button class="btn-outline">Elegir Ruta</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Features -->
    <section class="features">
        <div class="container">
            <h2 class="section-title">¿Por qué elegir nuestro curso?</h2>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3>Contenido Actualizado</h3>
                    <p>Siempre al día con las últimas características de JavaScript y mejores prácticas de la industria.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <h3>Proyectos Reales</h3>
                    <p>Construye aplicaciones del mundo real como e-commerce, redes sociales y sistemas de trading.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Comunidad Activa</h3>
                    <p>Únete a miles de estudiantes, comparte conocimientos y recibe ayuda cuando la necesites.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <h3>Certificación</h3>
                    <p>Obtén certificados verificables que demuestren tu expertise en JavaScript.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Acceso Móvil</h3>
                    <p>Aprende desde cualquier dispositivo, en cualquier momento y lugar.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-infinity"></i>
                    </div>
                    <h3>Acceso de por Vida</h3>
                    <p>Una vez inscrito, tendrás acceso permanente a todo el contenido y actualizaciones.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>JavaScript Maestría</h3>
                    <p>El curso más completo de JavaScript del mundo. Desde principiante hasta experto.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-github"></i></a>
                        <a href="#"><i class="fab fa-discord"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Curso</h4>
                    <ul>
                        <li><a href="#">Contenido</a></li>
                        <li><a href="#">Proyectos</a></li>
                        <li><a href="#">Certificación</a></li>
                        <li><a href="#">Precios</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Soporte</h4>
                    <ul>
                        <li><a href="#">Documentación</a></li>
                        <li><a href="#">Comunidad</a></li>
                        <li><a href="#">FAQ</a></li>
                        <li><a href="#">Contacto</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Recursos</h4>
                    <ul>
                        <li><a href="#">Blog</a></li>
                        <li><a href="#">Tutoriales</a></li>
                        <li><a href="#">Herramientas</a></li>
                        <li><a href="#">Cheat Sheets</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 JavaScript Maestría. Todos los derechos reservados.</p>
                <div class="footer-links">
                    <a href="#">Términos de Uso</a>
                    <a href="#">Política de Privacidad</a>
                    <a href="#">Cookies</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
