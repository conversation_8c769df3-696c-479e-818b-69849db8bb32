## 4.2.3. Declaration statements

### Introducción
Los declaration statements en JavaScript crean variables, funciones o clases.  
Incluyen keywords como let, const, function, class.  
Son hoisted en algunos casos, afectando el scope.  
Esto es fundamental para estructurar código.  
Las declarations no son expresiones y no producen valores.  
¿ Cómo organizas tus declarations en scripts?

### Código de Ejemplo
```javascript
// Variable declarations
let a = 5;
const b = 10;

// Function declaration
function sumar(x, y) {
    return x + y;
}

// Class declaration
class Persona {
    constructor(nombre) {
        this.nombre = nombre;
    }
}

let p = new Persona('Juan');
console.log(a + b);
console.log(sumar(3, 4));
console.log(p.nombre);
```
### Resultados en Consola
- `15`
- `7`
- `Juan`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> VarDecl["Paso 1: let a = 5; const b = 10 <br> - Variable declarations"]
    VarDecl --> FuncDecl["Paso 2: function sumar(x, y) <br> - Function declaration"]
    FuncDecl --> ClassDecl["Paso 3: class Persona <br> - Class declaration"]
    ClassDecl --> Instancia["Paso 4: let p = new Persona('Juan') <br> - Instanciación"]
    Instancia --> Log1["Paso 5: console.log(a + b) <br> - Resultado: 15"]
    Log1 --> Log2["Paso 6: console.log(sumar(3, 4)) <br> - Resultado: 7"]
    Log2 --> Log3["Paso 7: console.log(p.nombre) <br> - Resultado: Juan"]
    Log3 --> Fin["Paso 8: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Declarations["Declarations"]
    VarDecl --> NotaVar["Nota: Scope local"]
    FuncDecl --> NotaFunc["Nota: Hoisted"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style VarDecl fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style FuncDecl fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ClassDecl fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Instancia fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log1 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log2 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log3 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Declarations fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaVar fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaFunc fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Variables["Variables <br> - let, const, var"]
        Variables --> Funciones["Funciones <br> - function"]
        Funciones --> Clases["Clases <br> - class"]
        Clases --> Hoisting["Hoisting <br> - En functions"]
        Hoisting --> Scope["Scope <br> - Block o function"]
        Inicio --> Desarrollador(("Desarrollador")) --> Estructura["Estructura"]
        Variables --> NotaVar["Nota: Inmutables con const"]
        Funciones --> NotaFunc["Nota: Declaradas antes de uso"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Variables fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Funciones fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Clases fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Hoisting fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Scope fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Estructura fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaVar fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaFunc fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Declarar variables al inicio de funciones.
2. Usar const para constantes inmutables.
3. Definir funciones reutilizables.
4. Crear clases para OOP.
5. Organizar código con declarations hoisted.

### Errores comunes
1. Usar var en lugar de let/const.
2. Reasignar const.
3. Llamar functions antes de declaration en strict mode.
4. Confundir class declarations con expressions.
5. No inicializar variables declaradas.

### Recomendaciones
1. Prefiere let/const sobre var.
2. Declara functions en top-level.
3. Usa classes para encapsulación.
4. Entiende hoisting para evitar bugs.
5. Sigue estándares modernos de ES6+.