<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <style>
    text { font-family: Arial; font-size: 14px; }
    rect { fill: #f0f0f0; stroke: #000; stroke-width: 1; }
    .undefined { fill: #FFD700; }
    .uninitialized { fill: #FF6347; }
    .tdz { fill: #FFB6C1; }
  </style>
  <rect x="10" y="10" width="780" height="580" fill="#ffffff" stroke="#000"/>
  <text x="400" y="40" text-anchor="middle" font-size="18" font-weight="bold">Undefined vs Uninitialized en JavaScript</text>
  
  <g transform="translate(50,80)">
    <rect x="0" y="0" width="200" height="40" class="uninitialized"/>
    <text x="100" y="25" text-anchor="middle">Variable No Declarada</text>
    <text x="100" y="60" text-anchor="middle">ReferenceError: not defined</text>
  </g>
  
  <g transform="translate(300,80)">
    <rect x="0" y="0" width="200" height="40" class="undefined"/>
    <text x="100" y="25" text-anchor="middle">Declarada No Inicializada (var)</text>
    <text x="100" y="60" text-anchor="middle">Valor: undefined (hoisting)</text>
  </g>
  
  <g transform="translate(550,80)">
    <rect x="0" y="0" width="200" height="40" class="tdz"/>
    <text x="100" y="25" text-anchor="middle">let/const en TDZ</text>
    <text x="100" y="60" text-anchor="middle">ReferenceError: before initialization</text>
  </g>
  
  <g transform="translate(50,180)">
    <rect x="0" y="0" width="200" height="40" class="undefined"/>
    <text x="100" y="25" text-anchor="middle">Inicializada con undefined</text>
    <text x="100" y="60" text-anchor="middle">Valor: undefined (explícito)</text>
  </g>
  
  <g transform="translate(300,180)">
    <text x="0" y="0">Comparaciones:</text>
    <text x="0" y="30">undefined === undefined: true</text>
    <text x="0" y="60">typeof undefined: 'undefined'</text>
    <text x="0" y="90">!undefined: true (falsy)</text>
  </g>
  
  <g transform="translate(50,300)">
    <text x="0" y="0" font-weight="bold">Patrones Seguros:</text>
    <text x="0" y="30">value || 'default' (falsy)</text>
    <text x="0" y="60">value ?? 'default' (nullish)</text>
    <text x="0" y="90">(value !== undefined &amp;&amp; value !== null) ? value : 'default'</text>
  </g>
  
  <g transform="translate(400,300)">
    <text x="0" y="0" font-weight="bold">Debugging:</text>
    <text x="0" y="30">typeof value</text>
    <text x="0" y="60">value === undefined</text>
    <text x="0" y="90">value == null (nullish)</text>
    <text x="0" y="120">!value (falsy)</text>
  </g>
  
  <line x1="150" y1="120" x2="150" y2="160" stroke="#000" stroke-width="2"/>
  <line x1="400" y1="120" x2="400" y2="160" stroke="#000" stroke-width="2"/>
  <line x1="650" y1="120" x2="650" y2="160" stroke="#000" stroke-width="2"/>
  <line x1="150" y1="220" x2="400" y2="220" stroke="#000" stroke-width="2" marker-end="url(#arrow)"/>
</svg>