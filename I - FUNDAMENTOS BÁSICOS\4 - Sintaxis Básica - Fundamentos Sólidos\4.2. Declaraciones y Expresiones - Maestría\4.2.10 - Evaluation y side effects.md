## 4.2.10. Evaluation y side effects

### Introducción
La evaluation en JavaScript ejecuta expressions y statements.  
Side effects modifican estado.  
Expressions evalúan a valores.  
Statements ejecutan acciones.  
Orden de evaluation importa.  
¿Consideras side effects en tu código?

### Código de Ejemplo
```javascript
// Simple evaluation
let a = 5 + 3;
console.log(a);

// Side effect in expression
let b = 0;
console.log(++b);
console.log(b);

// Function with side effect
function sideEffect() {
    console.log('Side effect');
    return 42;
}
let c = sideEffect();
console.log(c);

// Order of evaluation
let d = 1;
let e = (d++, d + 1);
console.log(e);
console.log(d);

// Short-circuit evaluation
let f = false && sideEffect();
console.log(f);
```
### Resultados en Consola
- `8`
- `1`
- `1`
- `Side effect`
- `42`
- `3`
- `2`
- `false`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> EvalSimple["Paso 1: let a = 5 + 3 <br> - Evaluation"]
    EvalSimple --> LogA["Paso 2: console.log(a) <br> - Resultado: 8"]
    LogA --> SideExpr["Paso 3: ++b <br> - Side effect"]
    SideExpr --> LogB1["Paso 4: console.log(++b) <br> - Resultado: 1"]
    LogB1 --> LogB2["Paso 5: console.log(b) <br> - Resultado: 1"]
    LogB2 --> FuncSide["Paso 6: sideEffect() <br> - Function with side"]
    FuncSide --> LogC["Paso 7: console.log(c) <br> - Resultado: 42"]
    LogC --> OrderEval["Paso 8: (d++, d + 1) <br> - Order"]
    OrderEval --> LogE["Paso 9: console.log(e) <br> - Resultado: 3"]
    LogE --> LogD["Paso 10: console.log(d) <br> - Resultado: 2"]
    LogD --> ShortCircuit["Paso 11: false && sideEffect() <br> - No ejecuta side"]
    ShortCircuit --> LogF["Paso 12: console.log(f) <br> - Resultado: false"]
    LogF --> Fin["Paso 13: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> SideEffects["Gestionar side effects"]
    SideExpr --> NotaSide["Nota: Modifica estado"]
    ShortCircuit --> NotaShort["Nota: Short-circuit evita eval"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style EvalSimple fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogA fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style SideExpr fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogB1 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogB2 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style FuncSide fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogC fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style OrderEval fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogE fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogD fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ShortCircuit fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogF fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style SideEffects fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaSide fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaShort fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Evaluation["Evaluation <br> - Ejecuta code"]
        Evaluation --> Value["Produce <br> - Valor"]
        Value --> SideEffect["Side Effect <br> - Modifica estado"]
        SideEffect --> Order["Order <br> - Secuencia de eval"]
        Order --> ShortCircuit["Short-Circuit <br> - Evita eval innecesaria"]
        ShortCircuit --> Pure["Pure Functions <br> - Sin side effects"]
        Pure --> Uso["Uso: Predecible y testable"]
        Inicio --> Desarrollador(("Desarrollador")) --> Control["Controlar side effects"]
        SideEffect --> NotaSide["Nota: IO, mutación"]
        ShortCircuit --> NotaShort["Nota: &&, ||, ?:"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Evaluation fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Value fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style SideEffect fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Order fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ShortCircuit fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Pure fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Uso fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Control fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaSide fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaShort fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Incrementar contadores.
2. Funciones con logging.
3. Lazy evaluation.
4. Conditional execution.
5. Pure computations.

### Errores comunes
1. Ignorar order de evaluation.
2. Side effects inesperados.
3. No manejar short-circuit.
4. Mutar estado global.
5. Confundir value y effect.

### Recomendaciones
1. Minimiza side effects.
2. Usa pure functions.
3. Entiende operator precedence.
4. Testea evaluation order.
5. Documenta side effects.