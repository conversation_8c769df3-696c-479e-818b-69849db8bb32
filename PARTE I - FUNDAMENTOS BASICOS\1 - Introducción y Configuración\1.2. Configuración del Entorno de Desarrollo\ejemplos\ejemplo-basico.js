/**
 * EJEMPLO BÁSICO - CONFIGURACIÓN DEL ENTORNO
 * ==========================================
 * 
 * Este archivo demuestra la configuración básica de un proyecto JavaScript
 * y verifica que todas las herramientas estén funcionando correctamente.
 * 
 * Prerrequisitos:
 * - Node.js instalado
 * - VS Code configurado
 * - Terminal funcionando
 */

// ===================================
// 1. VERIFICACIÓN DE NODE.JS
// ===================================

console.log('🚀 VERIFICACIÓN DEL ENTORNO DE DESARROLLO');
console.log('==========================================\n');

// Información básica de Node.js
console.log('📦 INFORMACIÓN DE NODE.JS:');
console.log(`   Versión: ${process.version}`);
console.log(`   Plataforma: ${process.platform}`);
console.log(`   Arquitectura: ${process.arch}`);
console.log(`   PID del proceso: ${process.pid}`);
console.log(`   Directorio de trabajo: ${process.cwd()}`);
console.log('');

// ===================================
// 2. VERIFICACIÓN DE MÓDULOS CORE
// ===================================

console.log('🔧 MÓDULOS CORE DISPONIBLES:');

// File System
const fs = require('fs');
console.log('   ✅ fs (File System) - Disponible');

// Path
const path = require('path');
console.log('   ✅ path - Disponible');

// OS
const os = require('os');
console.log('   ✅ os (Operating System) - Disponible');

// HTTP
const http = require('http');
console.log('   ✅ http - Disponible');

// URL
const url = require('url');
console.log('   ✅ url - Disponible');

console.log('');

// ===================================
// 3. INFORMACIÓN DEL SISTEMA
// ===================================

console.log('💻 INFORMACIÓN DEL SISTEMA:');
console.log(`   Usuario: ${os.userInfo().username}`);
console.log(`   Sistema: ${os.type()} ${os.release()}`);
console.log(`   Memoria total: ${Math.round(os.totalmem() / 1024 / 1024)} MB`);
console.log(`   Memoria libre: ${Math.round(os.freemem() / 1024 / 1024)} MB`);
console.log(`   CPUs: ${os.cpus().length} cores`);
console.log(`   Directorio home: ${os.homedir()}`);
console.log('');

// ===================================
// 4. VERIFICACIÓN DE ARCHIVOS
// ===================================

console.log('📁 INFORMACIÓN DE ARCHIVOS:');
console.log(`   Archivo actual: ${path.basename(__filename)}`);
console.log(`   Directorio actual: ${path.dirname(__filename)}`);
console.log(`   Extensión: ${path.extname(__filename)}`);
console.log(`   Ruta completa: ${__filename}`);
console.log('');

// ===================================
// 5. VERIFICACIÓN DE VARIABLES DE ENTORNO
// ===================================

console.log('🌍 VARIABLES DE ENTORNO IMPORTANTES:');
console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'no definida'}`);
console.log(`   PATH incluye npm: ${process.env.PATH.includes('npm') ? '✅ Sí' : '❌ No'}`);
console.log(`   HOME/USERPROFILE: ${process.env.HOME || process.env.USERPROFILE}`);
console.log('');

// ===================================
// 6. PRUEBA DE FUNCIONALIDAD BÁSICA
// ===================================

console.log('🧪 PRUEBAS DE FUNCIONALIDAD:');

// Prueba 1: Operaciones matemáticas
const suma = 2 + 3;
const multiplicacion = 4 * 5;
console.log(`   ✅ Matemáticas: 2 + 3 = ${suma}, 4 * 5 = ${multiplicacion}`);

// Prueba 2: Manipulación de strings
const saludo = 'Hola';
const nombre = 'Mundo';
const mensaje = `${saludo}, ${nombre}!`;
console.log(`   ✅ Strings: "${mensaje}"`);

// Prueba 3: Arrays y objetos
const numeros = [1, 2, 3, 4, 5];
const persona = { nombre: 'Juan', edad: 30 };
console.log(`   ✅ Arrays: [${numeros.join(', ')}]`);
console.log(`   ✅ Objetos: ${JSON.stringify(persona)}`);

// Prueba 4: Funciones
function saludar(nombre) {
    return `¡Hola, ${nombre}!`;
}

const saludarArrow = (nombre) => `¡Hola, ${nombre}!`;
console.log(`   ✅ Función tradicional: ${saludar('JavaScript')}`);
console.log(`   ✅ Arrow function: ${saludarArrow('Node.js')}`);

console.log('');

// ===================================
// 7. VERIFICACIÓN DE TIMING
// ===================================

console.log('⏱️  VERIFICACIÓN DE TIMING:');

// Tiempo de inicio
const inicioTiempo = Date.now();
console.log(`   Timestamp de inicio: ${inicioTiempo}`);

// Simular operación asíncrona
setTimeout(() => {
    const finTiempo = Date.now();
    const duracion = finTiempo - inicioTiempo;
    console.log(`   ✅ setTimeout funcionando: ${duracion}ms transcurridos`);
}, 100);

// ===================================
// 8. VERIFICACIÓN DE ARGUMENTOS
// ===================================

console.log('📝 ARGUMENTOS DE LÍNEA DE COMANDOS:');
const argumentos = process.argv.slice(2);
if (argumentos.length > 0) {
    console.log(`   Argumentos recibidos: ${argumentos.join(', ')}`);
} else {
    console.log('   No se recibieron argumentos');
    console.log('   Prueba ejecutando: node ejemplo-basico.js arg1 arg2');
}
console.log('');

// ===================================
// 9. RESUMEN FINAL
// ===================================

console.log('📊 RESUMEN DE VERIFICACIÓN:');
console.log('   ✅ Node.js instalado y funcionando');
console.log('   ✅ Módulos core accesibles');
console.log('   ✅ Sistema operativo detectado');
console.log('   ✅ Funcionalidades básicas de JavaScript operativas');
console.log('   ✅ Timing y asincronía funcionando');
console.log('');

console.log('🎉 ¡CONFIGURACIÓN BÁSICA COMPLETADA!');
console.log('Tu entorno está listo para el desarrollo con JavaScript.');
console.log('');

// ===================================
// 10. INSTRUCCIONES SIGUIENTES
// ===================================

console.log('📋 PRÓXIMOS PASOS:');
console.log('   1. Instalar Visual Studio Code');
console.log('   2. Configurar extensiones esenciales');
console.log('   3. Configurar Git');
console.log('   4. Instalar gestores de paquetes');
console.log('   5. Configurar Prettier y ESLint');
console.log('');

console.log('💡 CONSEJOS:');
console.log('   - Guarda este archivo como referencia');
console.log('   - Ejecuta este script periódicamente para verificar tu entorno');
console.log('   - Si algo falla, revisa la instalación de Node.js');
console.log('');

// ===================================
// EXPORTAR FUNCIONES PARA TESTING
// ===================================

// Si este archivo se usa como módulo, exportar funciones útiles
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        verificarNodejs: () => {
            return {
                version: process.version,
                platform: process.platform,
                arch: process.arch
            };
        },
        
        obtenerInfoSistema: () => {
            return {
                usuario: os.userInfo().username,
                sistema: `${os.type()} ${os.release()}`,
                memoriaTotal: Math.round(os.totalmem() / 1024 / 1024),
                memoriaLibre: Math.round(os.freemem() / 1024 / 1024),
                cpus: os.cpus().length
            };
        },
        
        probarFuncionalidad: () => {
            try {
                // Pruebas básicas
                const suma = 2 + 3;
                const mensaje = `Hola, ${'Mundo'}!`;
                const array = [1, 2, 3];
                const objeto = { test: true };
                
                return {
                    matematicas: suma === 5,
                    strings: mensaje === 'Hola, Mundo!',
                    arrays: Array.isArray(array),
                    objetos: typeof objeto === 'object',
                    funciones: typeof (() => {}) === 'function'
                };
            } catch (error) {
                return { error: error.message };
            }
        }
    };
}

/*
INSTRUCCIONES DE USO:
====================

1. Para ejecutar este archivo:
   node ejemplo-basico.js

2. Para ejecutar con argumentos:
   node ejemplo-basico.js arg1 arg2 arg3

3. Para usar como módulo en otro archivo:
   const verificador = require('./ejemplo-basico.js');
   console.log(verificador.verificarNodejs());

4. Para verificar que todo funciona:
   - Deberías ver todos los ✅ en verde
   - No debería haber errores en la consola
   - El script debería completarse sin problemas

SOLUCIÓN DE PROBLEMAS:
=====================

Si ves errores:
- Verifica que Node.js esté instalado: node --version
- Verifica que estés en el directorio correcto
- Asegúrate de que el archivo tenga permisos de lectura
- Revisa que no haya errores de sintaxis

Si el script no se ejecuta:
- Verifica la instalación de Node.js
- Comprueba que el PATH incluya Node.js
- Reinicia tu terminal
- Intenta ejecutar: node --version

¡Tu entorno está listo para continuar con el curso!
*/
