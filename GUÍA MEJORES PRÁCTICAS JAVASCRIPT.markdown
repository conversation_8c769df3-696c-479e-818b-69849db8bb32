# **GUÍA DE MEJORES PRÁCTICAS JAVASCRIPT**

## **📋 PRINCIPIOS FUNDAMENTALES**

### **1. <PERSON>ó<PERSON> y Legible**
- Usa nombres descriptivos para variables y funciones
- Mantén funciones pequeñas y con una sola responsabilidad
- Comenta el código cuando sea necesario, pero prefiere código auto-documentado
- Usa indentación consistente (2 o 4 espacios)

### **2. Principio DRY (Don't Repeat Yourself)**
- Evita duplicar código
- Crea funciones reutilizables
- Usa constantes para valores que se repiten

### **3. Principio SOLID**
- **S**ingle Responsibility: Una función, una responsabilidad
- **O**pen/Closed: Abierto para extensión, cerrado para modificación
- **L**iskov Substitution: Los objetos derivados deben ser sustituibles
- **I**nterface Segregation: Interfaces específicas mejor que generales
- **D**ependency Inversion: Depender de abstracciones, no de concreciones

## **🔤 VARIABLES Y DECLARACIONES**

### **✅ Buenas Prácticas**
```javascript
// Usar const por defecto, let cuando necesites reasignar
const API_URL = 'https://api.ejemplo.com';
let contador = 0;

// Nombres descriptivos
const usuarioActivo = obtenerUsuarioActivo();
const estaAutenticado = verificarAutenticacion();

// Destructuring para extraer propiedades
const { nombre, email } = usuario;
const [primero, segundo] = array;

// Template literals para strings complejos
const mensaje = `Bienvenido ${nombre}, tienes ${notificaciones} notificaciones`;
```

### **❌ Evitar**
```javascript
// No usar var
var x = 1; // ❌

// Nombres no descriptivos
let d = new Date(); // ❌
let u = getUser(); // ❌

// Concatenación de strings compleja
let msg = 'Hola ' + nombre + ', tienes ' + count + ' mensajes'; // ❌
```

## **⚡ FUNCIONES**

### **✅ Buenas Prácticas**
```javascript
// Funciones puras (sin efectos secundarios)
const calcularTotal = (precio, impuesto) => precio * (1 + impuesto);

// Parámetros por defecto
const saludar = (nombre = 'Usuario') => `Hola ${nombre}`;

// Funciones pequeñas y específicas
const esEmailValido = email => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

// Usar arrow functions para callbacks
const numeros = [1, 2, 3, 4, 5];
const pares = numeros.filter(num => num % 2 === 0);

// Early return para reducir anidamiento
function procesarUsuario(usuario) {
    if (!usuario) return null;
    if (!usuario.activo) return null;
    
    return {
        id: usuario.id,
        nombre: usuario.nombre.toUpperCase()
    };
}
```

### **❌ Evitar**
```javascript
// Funciones muy largas
function procesarTodo() {
    // 100+ líneas de código ❌
}

// Muchos parámetros
function crearUsuario(nombre, email, edad, telefono, direccion, ciudad, pais) {} // ❌

// Efectos secundarios ocultos
function calcular(x) {
    globalVariable = x * 2; // ❌ Modifica estado global
    return x + 1;
}
```

## **🏗️ OBJETOS Y CLASES**

### **✅ Buenas Prácticas**
```javascript
// Usar Object.freeze para objetos inmutables
const CONFIG = Object.freeze({
    API_URL: 'https://api.ejemplo.com',
    TIMEOUT: 5000
});

// Métodos en prototype para mejor performance
class Usuario {
    constructor(nombre, email) {
        this.nombre = nombre;
        this.email = email;
    }
    
    obtenerInfo() {
        return `${this.nombre} (${this.email})`;
    }
}

// Composición sobre herencia
const conAutenticacion = (obj) => ({
    ...obj,
    autenticar() { /* lógica */ }
});

// Usar getters/setters cuando sea apropiado
class Temperatura {
    constructor(celsius) {
        this._celsius = celsius;
    }
    
    get fahrenheit() {
        return this._celsius * 9/5 + 32;
    }
    
    set fahrenheit(valor) {
        this._celsius = (valor - 32) * 5/9;
    }
}
```

## **🔄 PROGRAMACIÓN ASÍNCRONA**

### **✅ Buenas Prácticas**
```javascript
// Usar async/await en lugar de .then()
async function obtenerDatos() {
    try {
        const response = await fetch('/api/datos');
        const datos = await response.json();
        return datos;
    } catch (error) {
        console.error('Error al obtener datos:', error);
        throw error;
    }
}

// Manejar múltiples promesas en paralelo
async function obtenerTodosLosDatos() {
    try {
        const [usuarios, productos, pedidos] = await Promise.all([
            obtenerUsuarios(),
            obtenerProductos(),
            obtenerPedidos()
        ]);
        
        return { usuarios, productos, pedidos };
    } catch (error) {
        console.error('Error:', error);
    }
}

// Timeout para peticiones
const fetchConTimeout = (url, timeout = 5000) => {
    return Promise.race([
        fetch(url),
        new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Timeout')), timeout)
        )
    ]);
};
```

### **❌ Evitar**
```javascript
// Callback hell
getData(function(a) {
    getMoreData(a, function(b) {
        getEvenMoreData(b, function(c) {
            // ❌ Difícil de leer y mantener
        });
    });
});

// No manejar errores
async function malEjemplo() {
    const data = await fetch('/api'); // ❌ Sin try/catch
    return data.json();
}
```

## **🚨 MANEJO DE ERRORES**

### **✅ Buenas Prácticas**
```javascript
// Errores específicos y descriptivos
class ValidationError extends Error {
    constructor(message, field) {
        super(message);
        this.name = 'ValidationError';
        this.field = field;
    }
}

// Manejo centralizado de errores
function manejarError(error) {
    if (error instanceof ValidationError) {
        mostrarErrorValidacion(error.message, error.field);
    } else if (error instanceof NetworkError) {
        mostrarErrorRed();
    } else {
        mostrarErrorGenerico();
    }
    
    // Log para debugging
    console.error('Error:', error);
}

// Validación de entrada
function dividir(a, b) {
    if (typeof a !== 'number' || typeof b !== 'number') {
        throw new TypeError('Los argumentos deben ser números');
    }
    
    if (b === 0) {
        throw new Error('División por cero no permitida');
    }
    
    return a / b;
}
```

## **🎯 PERFORMANCE**

### **✅ Buenas Prácticas**
```javascript
// Debouncing para eventos frecuentes
function debounce(func, delay) {
    let timeoutId;
    return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

const buscarDebounced = debounce(buscar, 300);

// Lazy loading
const cargarModulo = () => import('./modulo-pesado.js');

// Memoización para cálculos costosos
const memoize = (fn) => {
    const cache = new Map();
    return (...args) => {
        const key = JSON.stringify(args);
        if (cache.has(key)) {
            return cache.get(key);
        }
        const result = fn(...args);
        cache.set(key, result);
        return result;
    };
};

// Usar WeakMap para referencias débiles
const metadatos = new WeakMap();
metadatos.set(objeto, { info: 'datos adicionales' });
```

## **🔒 SEGURIDAD**

### **✅ Buenas Prácticas**
```javascript
// Validar y sanitizar entrada del usuario
function sanitizarHTML(str) {
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
}

// Usar Content Security Policy
// <meta http-equiv="Content-Security-Policy" content="default-src 'self'">

// Evitar eval() y similares
// const codigo = "alert('hola')";
// eval(codigo); // ❌ Nunca usar

// Usar HTTPS para todas las comunicaciones
const API_BASE = 'https://api.segura.com'; // ✅

// Validar tokens JWT
function validarToken(token) {
    try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        return payload.exp > Date.now() / 1000;
    } catch {
        return false;
    }
}
```

## **🧪 TESTING**

### **✅ Buenas Prácticas**
```javascript
// Tests descriptivos y específicos
describe('Calculadora', () => {
    test('debe sumar dos números correctamente', () => {
        expect(sumar(2, 3)).toBe(5);
    });
    
    test('debe lanzar error con argumentos no numéricos', () => {
        expect(() => sumar('a', 'b')).toThrow(TypeError);
    });
});

// Arrange, Act, Assert pattern
test('debe procesar usuario válido', () => {
    // Arrange
    const usuario = { nombre: 'Juan', email: '<EMAIL>' };
    
    // Act
    const resultado = procesarUsuario(usuario);
    
    // Assert
    expect(resultado.nombre).toBe('JUAN');
    expect(resultado.email).toBe('<EMAIL>');
});

// Mocks para dependencias externas
const mockFetch = jest.fn();
global.fetch = mockFetch;

test('debe obtener datos del API', async () => {
    mockFetch.mockResolvedValue({
        json: () => Promise.resolve({ id: 1, nombre: 'Test' })
    });
    
    const datos = await obtenerDatos();
    expect(datos.nombre).toBe('Test');
});
```

## **📦 ORGANIZACIÓN DE CÓDIGO**

### **✅ Estructura de Proyecto**
```
src/
├── components/          # Componentes reutilizables
├── services/           # Lógica de negocio
├── utils/              # Utilidades generales
├── constants/          # Constantes de la aplicación
├── types/              # Definiciones de tipos (TypeScript)
├── tests/              # Tests
└── assets/             # Recursos estáticos
```

### **✅ Imports y Exports**
```javascript
// Exports nombrados para múltiples funciones
export const utilidad1 = () => {};
export const utilidad2 = () => {};

// Export default para la función principal
export default class ComponentePrincipal {}

// Imports organizados
import React from 'react';
import { useState, useEffect } from 'react';

import { utilidad1, utilidad2 } from '../utils';
import ComponentePrincipal from './ComponentePrincipal';
```

## **🔧 HERRAMIENTAS Y CONFIGURACIÓN**

### **✅ Configuración Recomendada**
```javascript
// .eslintrc.js
module.exports = {
    extends: ['eslint:recommended'],
    rules: {
        'no-console': 'warn',
        'no-unused-vars': 'error',
        'prefer-const': 'error',
        'no-var': 'error'
    }
};

// prettier.config.js
module.exports = {
    semi: true,
    singleQuote: true,
    tabWidth: 2,
    trailingComma: 'es5'
};
```

## **📚 DOCUMENTACIÓN**

### **✅ Comentarios Útiles**
```javascript
/**
 * Calcula el precio total incluyendo impuestos
 * @param {number} precio - Precio base del producto
 * @param {number} impuesto - Porcentaje de impuesto (0.1 = 10%)
 * @returns {number} Precio total con impuestos
 */
function calcularPrecioTotal(precio, impuesto) {
    return precio * (1 + impuesto);
}

// TODO: Implementar cache para mejorar performance
// FIXME: Bug con fechas en Safari
// NOTE: Esta función será deprecada en v2.0
```

Estas prácticas te ayudarán a escribir código JavaScript más mantenible, legible y robusto. Recuerda que la consistencia en el equipo es clave para el éxito del proyecto.
