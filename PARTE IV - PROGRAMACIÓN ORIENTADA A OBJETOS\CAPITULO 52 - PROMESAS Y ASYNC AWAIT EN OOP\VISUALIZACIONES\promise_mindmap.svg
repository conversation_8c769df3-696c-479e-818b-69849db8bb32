<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
  <!-- Definiciones -->
  <defs>
    <!-- Gradientes -->
    <radialGradient id="centerGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </radialGradient>
    
    <linearGradient id="branch1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="branch2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="branch3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ec4899;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#be185d;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="branch4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="branch5" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="branch6" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
    
    <!-- Sombra -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Título -->
  <text x="600" y="30" text-anchor="middle" font-size="24" font-weight="bold" fill="#1f2937">
    Mapa Mental: Promesas en Programación Orientada a Objetos
  </text>
  
  <!-- Nodo Central -->
  <circle cx="600" cy="450" r="90" fill="url(#centerGradient)" stroke="#1d4ed8" stroke-width="4" filter="url(#shadow)"/>
  <text x="600" y="440" text-anchor="middle" font-size="18" font-weight="bold" fill="#fff">PROMESAS</text>
  <text x="600" y="460" text-anchor="middle" font-size="14" fill="#fff">EN OOP</text>
  <text x="600" y="475" text-anchor="middle" font-size="12" fill="#fff">JavaScript</text>
  
  <!-- Rama 1: Fundamentos -->
  <line x1="690" y1="450" x2="850" y2="300" stroke="#10b981" stroke-width="5"/>
  <ellipse cx="900" cy="270" rx="80" ry="45" fill="url(#branch1)" stroke="#047857" stroke-width="3" filter="url(#shadow)"/>
  <text x="900" y="265" text-anchor="middle" font-size="16" font-weight="bold" fill="#fff">Fundamentos</text>
  <text x="900" y="280" text-anchor="middle" font-size="12" fill="#fff">Básicos</text>
  
  <!-- Sub-ramas Fundamentos -->
  <line x1="980" y1="270" x2="1080" y2="200" stroke="#10b981" stroke-width="3"/>
  <rect x="1050" y="180" width="140" height="40" fill="#dcfce7" stroke="#16a34a" stroke-width="2" rx="8"/>
  <text x="1120" y="195" text-anchor="middle" font-size="11" fill="#16a34a">Estados de Promesa</text>
  <text x="1120" y="210" text-anchor="middle" font-size="10" fill="#16a34a">pending/fulfilled/rejected</text>
  
  <line x1="980" y1="270" x2="1080" y2="270" stroke="#10b981" stroke-width="3"/>
  <rect x="1050" y="250" width="140" height="40" fill="#dcfce7" stroke="#16a34a" stroke-width="2" rx="8"/>
  <text x="1120" y="265" text-anchor="middle" font-size="11" fill="#16a34a">Constructor Promise</text>
  <text x="1120" y="280" text-anchor="middle" font-size="10" fill="#16a34a">new Promise(executor)</text>
  
  <line x1="980" y1="270" x2="1080" y2="340" stroke="#10b981" stroke-width="3"/>
  <rect x="1050" y="320" width="140" height="40" fill="#dcfce7" stroke="#16a34a" stroke-width="2" rx="8"/>
  <text x="1120" y="335" text-anchor="middle" font-size="11" fill="#16a34a">Then/Catch/Finally</text>
  <text x="1120" y="350" text-anchor="middle" font-size="10" fill="#16a34a">Métodos de manejo</text>
  
  <!-- Rama 2: Creación Avanzada -->
  <line x1="600" y1="360" x2="600" y2="200" stroke="#f59e0b" stroke-width="5"/>
  <ellipse cx="600" cy="150" rx="80" ry="45" fill="url(#branch2)" stroke="#d97706" stroke-width="3" filter="url(#shadow)"/>
  <text x="600" y="145" text-anchor="middle" font-size="16" font-weight="bold" fill="#fff">Creación</text>
  <text x="600" y="160" text-anchor="middle" font-size="12" fill="#fff">Avanzada</text>
  
  <!-- Sub-ramas Creación -->
  <line x1="520" y1="150" x2="400" y2="80" stroke="#f59e0b" stroke-width="3"/>
  <rect x="320" y="60" width="140" height="40" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" rx="8"/>
  <text x="390" y="75" text-anchor="middle" font-size="11" fill="#92400e">Factory Functions</text>
  <text x="390" y="90" text-anchor="middle" font-size="10" fill="#92400e">createPromise()</text>
  
  <line x1="600" y1="105" x2="600" y2="50" stroke="#f59e0b" stroke-width="3"/>
  <rect x="530" y="30" width="140" height="40" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" rx="8"/>
  <text x="600" y="45" text-anchor="middle" font-size="11" fill="#92400e">Timeout & Retry</text>
  <text x="600" y="60" text-anchor="middle" font-size="10" fill="#92400e">Resilencia automática</text>
  
  <line x1="680" y1="150" x2="800" y2="80" stroke="#f59e0b" stroke-width="3"/>
  <rect x="740" y="60" width="140" height="40" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" rx="8"/>
  <text x="810" y="75" text-anchor="middle" font-size="11" fill="#92400e">Promisificación</text>
  <text x="810" y="90" text-anchor="middle" font-size="10" fill="#92400e">Callback → Promise</text>
  
  <!-- Rama 3: Concurrencia -->
  <line x1="510" y1="450" x2="350" y2="300" stroke="#ec4899" stroke-width="5"/>
  <ellipse cx="300" cy="270" rx="80" ry="45" fill="url(#branch3)" stroke="#be185d" stroke-width="3" filter="url(#shadow)"/>
  <text x="300" y="265" text-anchor="middle" font-size="16" font-weight="bold" fill="#fff">Concurrencia</text>
  <text x="300" y="280" text-anchor="middle" font-size="12" fill="#fff">Control</text>
  
  <!-- Sub-ramas Concurrencia -->
  <line x1="220" y1="270" x2="100" y2="200" stroke="#ec4899" stroke-width="3"/>
  <rect x="20" y="180" width="140" height="40" fill="#fce7f3" stroke="#ec4899" stroke-width="2" rx="8"/>
  <text x="90" y="195" text-anchor="middle" font-size="11" fill="#be185d">Ejecución Paralela</text>
  <text x="90" y="210" text-anchor="middle" font-size="10" fill="#be185d">executeConcurrent()</text>
  
  <line x1="220" y1="270" x2="100" y2="270" stroke="#ec4899" stroke-width="3"/>
  <rect x="20" y="250" width="140" height="40" fill="#fce7f3" stroke="#ec4899" stroke-width="2" rx="8"/>
  <text x="90" y="265" text-anchor="middle" font-size="11" fill="#be185d">Ejecución Secuencial</text>
  <text x="90" y="280" text-anchor="middle" font-size="10" fill="#be185d">executeSequential()</text>
  
  <line x1="220" y1="270" x2="100" y2="340" stroke="#ec4899" stroke-width="3"/>
  <rect x="20" y="320" width="140" height="40" fill="#fce7f3" stroke="#ec4899" stroke-width="2" rx="8"/>
  <text x="90" y="335" text-anchor="middle" font-size="11" fill="#be185d">Cola de Promesas</text>
  <text x="90" y="350" text-anchor="middle" font-size="10" fill="#be185d">Queue management</text>
  
  <!-- Rama 4: Monitoreo -->
  <line x1="600" y1="540" x2="600" y2="700" stroke="#8b5cf6" stroke-width="5"/>
  <ellipse cx="600" cy="750" rx="80" ry="45" fill="url(#branch4)" stroke="#7c3aed" stroke-width="3" filter="url(#shadow)"/>
  <text x="600" y="745" text-anchor="middle" font-size="16" font-weight="bold" fill="#fff">Monitoreo</text>
  <text x="600" y="760" text-anchor="middle" font-size="12" fill="#fff">Métricas</text>
  
  <!-- Sub-ramas Monitoreo -->
  <line x1="520" y1="750" x2="400" y2="820" stroke="#8b5cf6" stroke-width="3"/>
  <rect x="320" y="800" width="140" height="40" fill="#ede9fe" stroke="#8b5cf6" stroke-width="2" rx="8"/>
  <text x="390" y="815" text-anchor="middle" font-size="11" fill="#7c3aed">Event System</text>
  <text x="390" y="830" text-anchor="middle" font-size="10" fill="#7c3aed">on() / emit()</text>
  
  <line x1="600" y1="795" x2="600" y2="850" stroke="#8b5cf6" stroke-width="3"/>
  <rect x="530" y="830" width="140" height="40" fill="#ede9fe" stroke="#8b5cf6" stroke-width="2" rx="8"/>
  <text x="600" y="845" text-anchor="middle" font-size="11" fill="#7c3aed">Estadísticas</text>
  <text x="600" y="860" text-anchor="middle" font-size="10" fill="#7c3aed">Performance metrics</text>
  
  <line x1="680" y1="750" x2="800" y2="820" stroke="#8b5cf6" stroke-width="3"/>
  <rect x="740" y="800" width="140" height="40" fill="#ede9fe" stroke="#8b5cf6" stroke-width="2" rx="8"/>
  <text x="810" y="815" text-anchor="middle" font-size="11" fill="#7c3aed">Debugging</text>
  <text x="810" y="830" text-anchor="middle" font-size="10" fill="#7c3aed">Trace & logs</text>
  
  <!-- Rama 5: Patrones -->
  <line x1="690" y1="450" x2="850" y2="600" stroke="#06b6d4" stroke-width="5"/>
  <ellipse cx="900" cy="630" rx="80" ry="45" fill="url(#branch5)" stroke="#0891b2" stroke-width="3" filter="url(#shadow)"/>
  <text x="900" y="625" text-anchor="middle" font-size="16" font-weight="bold" fill="#fff">Patrones</text>
  <text x="900" y="640" text-anchor="middle" font-size="12" fill="#fff">Avanzados</text>
  
  <!-- Sub-ramas Patrones -->
  <line x1="980" y1="630" x2="1080" y2="560" stroke="#06b6d4" stroke-width="3"/>
  <rect x="1050" y="540" width="140" height="40" fill="#cffafe" stroke="#06b6d4" stroke-width="2" rx="8"/>
  <text x="1120" y="555" text-anchor="middle" font-size="11" fill="#0c4a6e">Circuit Breaker</text>
  <text x="1120" y="570" text-anchor="middle" font-size="10" fill="#0c4a6e">Fault tolerance</text>
  
  <line x1="980" y1="630" x2="1080" y2="630" stroke="#06b6d4" stroke-width="3"/>
  <rect x="1050" y="610" width="140" height="40" fill="#cffafe" stroke="#06b6d4" stroke-width="2" rx="8"/>
  <text x="1120" y="625" text-anchor="middle" font-size="11" fill="#0c4a6e">Bulkhead</text>
  <text x="1120" y="640" text-anchor="middle" font-size="10" fill="#0c4a6e">Resource isolation</text>
  
  <line x1="980" y1="630" x2="1080" y2="700" stroke="#06b6d4" stroke-width="3"/>
  <rect x="1050" y="680" width="140" height="40" fill="#cffafe" stroke="#06b6d4" stroke-width="2" rx="8"/>
  <text x="1120" y="695" text-anchor="middle" font-size="11" fill="#0c4a6e">Cache Pattern</text>
  <text x="1120" y="710" text-anchor="middle" font-size="10" fill="#0c4a6e">Memoization</text>
  
  <!-- Rama 6: Manejo de Errores -->
  <line x1="510" y1="450" x2="350" y2="600" stroke="#ef4444" stroke-width="5"/>
  <ellipse cx="300" cy="630" rx="80" ry="45" fill="url(#branch6)" stroke="#dc2626" stroke-width="3" filter="url(#shadow)"/>
  <text x="300" y="625" text-anchor="middle" font-size="16" font-weight="bold" fill="#fff">Manejo</text>
  <text x="300" y="640" text-anchor="middle" font-size="12" fill="#fff">de Errores</text>
  
  <!-- Sub-ramas Errores -->
  <line x1="220" y1="630" x2="100" y2="560" stroke="#ef4444" stroke-width="3"/>
  <rect x="20" y="540" width="140" height="40" fill="#fee2e2" stroke="#ef4444" stroke-width="2" rx="8"/>
  <text x="90" y="555" text-anchor="middle" font-size="11" fill="#dc2626">Try/Catch Global</text>
  <text x="90" y="570" text-anchor="middle" font-size="10" fill="#dc2626">Error boundaries</text>
  
  <line x1="220" y1="630" x2="100" y2="630" stroke="#ef4444" stroke-width="3"/>
  <rect x="20" y="610" width="140" height="40" fill="#fee2e2" stroke="#ef4444" stroke-width="2" rx="8"/>
  <text x="90" y="625" text-anchor="middle" font-size="11" fill="#dc2626">Retry Logic</text>
  <text x="90" y="640" text-anchor="middle" font-size="10" fill="#dc2626">Exponential backoff</text>
  
  <line x1="220" y1="630" x2="100" y2="700" stroke="#ef4444" stroke-width="3"/>
  <rect x="20" y="680" width="140" height="40" fill="#fee2e2" stroke="#ef4444" stroke-width="2" rx="8"/>
  <text x="90" y="695" text-anchor="middle" font-size="11" fill="#dc2626">Fallback Strategy</text>
  <text x="90" y="710" text-anchor="middle" font-size="10" fill="#dc2626">Graceful degradation</text>
  
  <!-- Conexiones entre ramas -->
  <path d="M 820 300 Q 750 350 680 400" stroke="#64748b" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
  <path d="M 380 300 Q 450 350 520 400" stroke="#64748b" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
  <path d="M 600 200 Q 650 300 700 400" stroke="#64748b" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
  <path d="M 380 600 Q 450 550 520 500" stroke="#64748b" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
  <path d="M 820 600 Q 750 550 680 500" stroke="#64748b" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
  
  <!-- Casos de Uso Centrales -->
  <rect x="450" y="400" width="300" height="100" fill="#f8fafc" stroke="#64748b" stroke-width="2" rx="10" opacity="0.9"/>
  <text x="600" y="420" text-anchor="middle" font-size="14" font-weight="bold" fill="#374151">Casos de Uso Principales</text>
  <text x="600" y="440" text-anchor="middle" font-size="11" fill="#64748b">• APIs y servicios externos</text>
  <text x="600" y="455" text-anchor="middle" font-size="11" fill="#64748b">• Operaciones de base de datos</text>
  <text x="600" y="470" text-anchor="middle" font-size="11" fill="#64748b">• Procesamiento de archivos</text>
  <text x="600" y="485" text-anchor="middle" font-size="11" fill="#64748b">• Microservicios y orquestación</text>
  
  <!-- Leyenda -->
  <rect x="50" y="50" width="200" height="140" fill="#f8fafc" stroke="#64748b" stroke-width="1" rx="5" opacity="0.95"/>
  <text x="60" y="70" font-size="14" font-weight="bold" fill="#374151">LEYENDA:</text>
  
  <circle cx="70" cy="90" r="8" fill="url(#centerGradient)"/>
  <text x="90" y="95" font-size="11" fill="#374151">Concepto Central</text>
  
  <line x1="60" y1="110" x2="80" y2="110" stroke="#10b981" stroke-width="4"/>
  <text x="90" y="115" font-size="11" fill="#374151">Ramas Principales</text>
  
  <rect x="60" y="125" width="15" height="10" fill="#dcfce7" stroke="#16a34a" stroke-width="1" rx="2"/>
  <text x="90" y="135" font-size="11" fill="#374151">Sub-conceptos</text>
  
  <line x1="60" y1="150" x2="80" y2="150" stroke="#64748b" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="90" y="155" font-size="11" fill="#374151">Interconexiones</text>
  
  <circle cx="70" cy="170" r="3" fill="#f59e0b"/>
  <text x="90" y="175" font-size="11" fill="#374151">Puntos clave</text>
  
  <!-- Beneficios clave -->
  <rect x="950" y="50" width="200" height="140" fill="#f0fdf4" stroke="#16a34a" stroke-width="2" rx="8" opacity="0.95"/>
  <text x="1050" y="75" text-anchor="middle" font-size="14" font-weight="bold" fill="#16a34a">Beneficios Clave</text>
  <text x="960" y="95" font-size="11" fill="#16a34a">✓ Código asíncrono limpio</text>
  <text x="960" y="110" font-size="11" fill="#16a34a">✓ Manejo robusto de errores</text>
  <text x="960" y="125" font-size="11" fill="#16a34a">✓ Control de concurrencia</text>
  <text x="960" y="140" font-size="11" fill="#16a34a">✓ Escalabilidad mejorada</text>
  <text x="960" y="155" font-size="11" fill="#16a34a">✓ Debugging simplificado</text>
  <text x="960" y="170" font-size="11" fill="#16a34a">✓ Arquitectura resiliente</text>
</svg>
