/**
 * MODELO DE FORO
 * ==============
 * 
 * Sistema completo de foros para CodeCraft Academy
 * Incluye categorías, hilos, respuestas, moderación y gamificación
 */

const mongoose = require('mongoose');

// Schema para respuestas/comentarios
const replySchema = new mongoose.Schema({
    content: {
        type: String,
        required: [true, 'El contenido de la respuesta es requerido'],
        maxlength: [10000, 'La respuesta no puede exceder 10000 caracteres']
    },
    
    author: {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        username: String,
        avatar: String,
        role: String,
        badges: [{
            badgeId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Badge'
            },
            name: String,
            icon: String
        }]
    },
    
    // Contenido enriquecido
    formatting: {
        hasCode: {
            type: Boolean,
            default: false
        },
        codeBlocks: [{
            language: String,
            code: String,
            title: String
        }],
        hasImages: {
            type: Boolean,
            default: false
        },
        images: [String],
        hasLinks: {
            type: Boolean,
            default: false
        },
        mentions: [{
            userId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            },
            username: String
        }]
    },
    
    // Interacciones
    votes: {
        upvotes: {
            type: Number,
            default: 0
        },
        downvotes: {
            type: Number,
            default: 0
        },
        score: {
            type: Number,
            default: 0
        },
        voters: [{
            userId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            },
            vote: {
                type: String,
                enum: ['up', 'down']
            },
            votedAt: {
                type: Date,
                default: Date.now
            }
        }]
    },
    
    // Estado
    isEdited: {
        type: Boolean,
        default: false
    },
    
    editHistory: [{
        content: String,
        editedAt: {
            type: Date,
            default: Date.now
        },
        reason: String
    }],
    
    isDeleted: {
        type: Boolean,
        default: false
    },
    
    deletedAt: Date,
    deletedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    
    // Moderación
    moderation: {
        isReported: {
            type: Boolean,
            default: false
        },
        reports: [{
            reportedBy: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            },
            reason: {
                type: String,
                enum: ['spam', 'inappropriate', 'harassment', 'off-topic', 'misinformation', 'other']
            },
            description: String,
            reportedAt: {
                type: Date,
                default: Date.now
            },
            status: {
                type: String,
                enum: ['pending', 'reviewed', 'resolved', 'dismissed'],
                default: 'pending'
            }
        }],
        isFlagged: {
            type: Boolean,
            default: false
        },
        flagReason: String,
        moderatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        moderatedAt: Date
    },
    
    // Respuestas anidadas
    replies: [this] // Recursivo para respuestas a respuestas

}, {
    timestamps: true
});

// Schema principal del foro
const forumSchema = new mongoose.Schema({
    // Información básica
    title: {
        type: String,
        required: [true, 'El título es requerido'],
        trim: true,
        maxlength: [200, 'El título no puede exceder 200 caracteres']
    },
    
    slug: {
        type: String,
        required: true,
        unique: true,
        lowercase: true
    },
    
    content: {
        type: String,
        required: [true, 'El contenido es requerido'],
        maxlength: [50000, 'El contenido no puede exceder 50000 caracteres']
    },
    
    excerpt: {
        type: String,
        maxlength: [300, 'El extracto no puede exceder 300 caracteres']
    },
    
    // Categorización
    category: {
        type: String,
        required: true,
        enum: [
            'general',
            'help',
            'showcase',
            'career',
            'resources',
            'announcements',
            'feedback',
            'off-topic',
            'course-specific',
            'study-groups',
            'job-board',
            'mentorship'
        ]
    },
    
    subcategory: {
        type: String,
        enum: [
            // Help
            'debugging', 'concept-explanation', 'project-help', 'tool-setup',
            // Showcase
            'projects', 'achievements', 'code-review', 'portfolio',
            // Career
            'job-search', 'interview-prep', 'salary-negotiation', 'career-change',
            // Resources
            'tutorials', 'tools', 'books', 'articles', 'videos',
            // Course-specific
            'javascript', 'python', 'react', 'nodejs', 'algorithms', 'databases'
        ]
    },
    
    tags: [{
        type: String,
        lowercase: true,
        trim: true
    }],
    
    // Autor
    author: {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        username: String,
        avatar: String,
        role: String,
        reputation: {
            type: Number,
            default: 0
        },
        badges: [{
            badgeId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Badge'
            },
            name: String,
            icon: String
        }]
    },
    
    // Contenido relacionado
    relatedCourse: {
        courseId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Course'
        },
        title: String,
        language: String
    },
    
    relatedLesson: {
        lessonId: String,
        title: String,
        partNumber: Number,
        chapterNumber: Number,
        lessonNumber: Number
    },
    
    // Contenido enriquecido
    formatting: {
        hasCode: {
            type: Boolean,
            default: false
        },
        codeBlocks: [{
            language: String,
            code: String,
            title: String,
            isRunnable: {
                type: Boolean,
                default: false
            }
        }],
        hasImages: {
            type: Boolean,
            default: false
        },
        images: [{
            url: String,
            caption: String,
            alt: String
        }],
        hasLinks: {
            type: Boolean,
            default: false
        },
        externalLinks: [{
            url: String,
            title: String,
            description: String
        }],
        mentions: [{
            userId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            },
            username: String
        }]
    },
    
    // Interacciones
    votes: {
        upvotes: {
            type: Number,
            default: 0
        },
        downvotes: {
            type: Number,
            default: 0
        },
        score: {
            type: Number,
            default: 0
        },
        voters: [{
            userId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            },
            vote: {
                type: String,
                enum: ['up', 'down']
            },
            votedAt: {
                type: Date,
                default: Date.now
            }
        }]
    },
    
    // Respuestas
    replies: [replySchema],
    
    replyCount: {
        type: Number,
        default: 0
    },
    
    // Solución (para hilos de ayuda)
    solution: {
        replyId: {
            type: mongoose.Schema.Types.ObjectId
        },
        markedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        markedAt: Date,
        isSolved: {
            type: Boolean,
            default: false
        }
    },
    
    // Estadísticas
    stats: {
        views: {
            type: Number,
            default: 0
        },
        uniqueViews: {
            type: Number,
            default: 0
        },
        viewers: [{
            userId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            },
            viewCount: {
                type: Number,
                default: 1
            },
            lastViewed: {
                type: Date,
                default: Date.now
            }
        }],
        shares: {
            type: Number,
            default: 0
        },
        bookmarks: {
            type: Number,
            default: 0
        }
    },
    
    // Estado del hilo
    status: {
        type: String,
        enum: ['open', 'closed', 'locked', 'archived', 'deleted'],
        default: 'open'
    },
    
    isPinned: {
        type: Boolean,
        default: false
    },
    
    isSticky: {
        type: Boolean,
        default: false
    },
    
    isFeatured: {
        type: Boolean,
        default: false
    },
    
    // Moderación
    moderation: {
        isReported: {
            type: Boolean,
            default: false
        },
        reports: [{
            reportedBy: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            },
            reason: {
                type: String,
                enum: ['spam', 'inappropriate', 'harassment', 'off-topic', 'misinformation', 'duplicate', 'other']
            },
            description: String,
            reportedAt: {
                type: Date,
                default: Date.now
            },
            status: {
                type: String,
                enum: ['pending', 'reviewed', 'resolved', 'dismissed'],
                default: 'pending'
            }
        }],
        isFlagged: {
            type: Boolean,
            default: false
        },
        flagReason: String,
        moderatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        moderatedAt: Date,
        moderationNotes: String
    },
    
    // Configuración
    settings: {
        allowReplies: {
            type: Boolean,
            default: true
        },
        allowVoting: {
            type: Boolean,
            default: true
        },
        requireModeration: {
            type: Boolean,
            default: false
        },
        isPrivate: {
            type: Boolean,
            default: false
        },
        allowedRoles: [{
            type: String,
            enum: ['student', 'instructor', 'admin', 'moderator']
        }]
    },
    
    // Fechas importantes
    lastActivity: {
        type: Date,
        default: Date.now
    },
    
    lastReplyAt: Date,
    
    // Notificaciones
    subscribers: [{
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        subscribedAt: {
            type: Date,
            default: Date.now
        },
        notificationSettings: {
            newReplies: {
                type: Boolean,
                default: true
            },
            mentions: {
                type: Boolean,
                default: true
            },
            solutions: {
                type: Boolean,
                default: true
            }
        }
    }]

}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Índices
forumSchema.index({ slug: 1 }, { unique: true });
forumSchema.index({ category: 1, subcategory: 1 });
forumSchema.index({ 'author.userId': 1 });
forumSchema.index({ tags: 1 });
forumSchema.index({ status: 1 });
forumSchema.index({ isPinned: -1, lastActivity: -1 });
forumSchema.index({ 'votes.score': -1 });
forumSchema.index({ 'stats.views': -1 });
forumSchema.index({ createdAt: -1 });
forumSchema.index({ 'relatedCourse.courseId': 1 });

// Índice de texto para búsqueda
forumSchema.index({
    title: 'text',
    content: 'text',
    tags: 'text'
});

// Virtuals
forumSchema.virtual('isPopular').get(function() {
    return this.stats.views > 100 && this.votes.score > 10;
});

forumSchema.virtual('isTrending').get(function() {
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);
    return this.lastActivity > oneDayAgo && this.stats.views > 20;
});

forumSchema.virtual('url').get(function() {
    return `/forum/${this.category}/${this.slug}`;
});

forumSchema.virtual('totalReplies').get(function() {
    let count = this.replies.length;
    
    // Contar respuestas anidadas
    const countNestedReplies = (replies) => {
        let nestedCount = 0;
        replies.forEach(reply => {
            if (reply.replies && reply.replies.length > 0) {
                nestedCount += reply.replies.length;
                nestedCount += countNestedReplies(reply.replies);
            }
        });
        return nestedCount;
    };
    
    count += countNestedReplies(this.replies);
    return count;
});

// Middleware pre-save
forumSchema.pre('save', function(next) {
    // Generar slug si no existe
    if (!this.slug && this.title) {
        this.slug = this.title
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '');
    }
    
    // Generar excerpt si no existe
    if (!this.excerpt && this.content) {
        this.excerpt = this.content
            .replace(/<[^>]*>/g, '') // Remover HTML
            .substring(0, 300)
            .trim();
        
        if (this.content.length > 300) {
            this.excerpt += '...';
        }
    }
    
    // Actualizar replyCount
    this.replyCount = this.totalReplies;
    
    // Calcular score de votos
    this.votes.score = this.votes.upvotes - this.votes.downvotes;
    
    next();
});

// Métodos de instancia
forumSchema.methods.addView = function(userId = null) {
    this.stats.views += 1;
    
    if (userId) {
        const existingViewer = this.stats.viewers.find(
            viewer => viewer.userId.toString() === userId.toString()
        );
        
        if (existingViewer) {
            existingViewer.viewCount += 1;
            existingViewer.lastViewed = new Date();
        } else {
            this.stats.viewers.push({
                userId,
                viewCount: 1,
                lastViewed: new Date()
            });
            this.stats.uniqueViews += 1;
        }
    }
    
    return this.save();
};

forumSchema.methods.addVote = function(userId, voteType) {
    // Remover voto anterior si existe
    this.votes.voters = this.votes.voters.filter(
        voter => voter.userId.toString() !== userId.toString()
    );
    
    // Añadir nuevo voto
    this.votes.voters.push({
        userId,
        vote: voteType,
        votedAt: new Date()
    });
    
    // Recalcular votos
    this.votes.upvotes = this.votes.voters.filter(v => v.vote === 'up').length;
    this.votes.downvotes = this.votes.voters.filter(v => v.vote === 'down').length;
    this.votes.score = this.votes.upvotes - this.votes.downvotes;
    
    return this.save();
};

forumSchema.methods.removeVote = function(userId) {
    this.votes.voters = this.votes.voters.filter(
        voter => voter.userId.toString() !== userId.toString()
    );
    
    // Recalcular votos
    this.votes.upvotes = this.votes.voters.filter(v => v.vote === 'up').length;
    this.votes.downvotes = this.votes.voters.filter(v => v.vote === 'down').length;
    this.votes.score = this.votes.upvotes - this.votes.downvotes;
    
    return this.save();
};

forumSchema.methods.addReply = function(replyData) {
    this.replies.push(replyData);
    this.replyCount = this.totalReplies;
    this.lastActivity = new Date();
    this.lastReplyAt = new Date();
    
    return this.save();
};

forumSchema.methods.markSolution = function(replyId, markedBy) {
    this.solution = {
        replyId,
        markedBy,
        markedAt: new Date(),
        isSolved: true
    };
    
    return this.save();
};

forumSchema.methods.subscribe = function(userId, settings = {}) {
    const existingSubscription = this.subscribers.find(
        sub => sub.userId.toString() === userId.toString()
    );
    
    if (!existingSubscription) {
        this.subscribers.push({
            userId,
            subscribedAt: new Date(),
            notificationSettings: {
                newReplies: settings.newReplies !== false,
                mentions: settings.mentions !== false,
                solutions: settings.solutions !== false
            }
        });
    }
    
    return this.save();
};

forumSchema.methods.unsubscribe = function(userId) {
    this.subscribers = this.subscribers.filter(
        sub => sub.userId.toString() !== userId.toString()
    );
    
    return this.save();
};

forumSchema.methods.report = function(reportData) {
    this.moderation.reports.push(reportData);
    this.moderation.isReported = true;
    
    return this.save();
};

forumSchema.methods.close = function(moderatorId, reason) {
    this.status = 'closed';
    this.moderation.moderatedBy = moderatorId;
    this.moderation.moderatedAt = new Date();
    this.moderation.moderationNotes = reason;
    
    return this.save();
};

forumSchema.methods.pin = function() {
    this.isPinned = true;
    return this.save();
};

forumSchema.methods.unpin = function() {
    this.isPinned = false;
    return this.save();
};

// Métodos estáticos
forumSchema.statics.findByCategory = function(category, options = {}) {
    const query = { category, status: 'open' };
    
    if (options.subcategory) {
        query.subcategory = options.subcategory;
    }
    
    return this.find(query)
        .sort({ isPinned: -1, lastActivity: -1 })
        .populate('author.userId', 'username profile.avatar role')
        .populate('relatedCourse.courseId', 'title language');
};

forumSchema.statics.findPopular = function(timeframe = 7, limit = 10) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - timeframe);
    
    return this.find({
        status: 'open',
        createdAt: { $gte: cutoffDate }
    })
    .sort({ 'votes.score': -1, 'stats.views': -1 })
    .limit(limit)
    .populate('author.userId', 'username profile.avatar role');
};

forumSchema.statics.findTrending = function(limit = 10) {
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);
    
    return this.find({
        status: 'open',
        lastActivity: { $gte: oneDayAgo }
    })
    .sort({ 'stats.views': -1, lastActivity: -1 })
    .limit(limit)
    .populate('author.userId', 'username profile.avatar role');
};

forumSchema.statics.findUnanswered = function(limit = 20) {
    return this.find({
        status: 'open',
        category: 'help',
        replyCount: 0
    })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('author.userId', 'username profile.avatar role');
};

forumSchema.statics.findUnsolved = function(limit = 20) {
    return this.find({
        status: 'open',
        category: 'help',
        'solution.isSolved': { $ne: true }
    })
    .sort({ lastActivity: -1 })
    .limit(limit)
    .populate('author.userId', 'username profile.avatar role');
};

forumSchema.statics.searchPosts = function(query, filters = {}) {
    const searchQuery = {
        $text: { $search: query },
        status: 'open'
    };
    
    if (filters.category) searchQuery.category = filters.category;
    if (filters.subcategory) searchQuery.subcategory = filters.subcategory;
    if (filters.tags) searchQuery.tags = { $in: filters.tags };
    if (filters.courseId) searchQuery['relatedCourse.courseId'] = filters.courseId;
    
    return this.find(searchQuery)
        .sort({ score: { $meta: 'textScore' }, lastActivity: -1 })
        .populate('author.userId', 'username profile.avatar role');
};

forumSchema.statics.getStatsByCategory = function() {
    return this.aggregate([
        { $match: { status: 'open' } },
        {
            $group: {
                _id: '$category',
                totalPosts: { $sum: 1 },
                totalReplies: { $sum: '$replyCount' },
                totalViews: { $sum: '$stats.views' },
                averageScore: { $avg: '$votes.score' }
            }
        },
        { $sort: { totalPosts: -1 } }
    ]);
};

module.exports = mongoose.model('Forum', forumSchema);
