<svg width="900" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Definiciones -->
  <defs>
    <!-- Gradientes -->
    <linearGradient id="activeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="gcGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="referenceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="leakGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <!-- Sombra -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
    
    <!-- Flecha -->
    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
    
    <!-- Flecha de referencia -->
    <marker id="refArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3b82f6"/>
    </marker>
  </defs>

  <!-- Título -->
  <text x="450" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#1f2937">
    Gestión de Memoria en Closures
  </text>
  
  <!-- Escenario 1: Closure Activo -->
  <rect x="50" y="60" width="380" height="240" fill="#f0fdf4" stroke="#16a34a" stroke-width="2" rx="10"/>
  <text x="60" y="85" font-size="16" font-weight="bold" fill="#16a34a">Closure Activo (Memoria Preservada)</text>
  
  <!-- Variable global -->
  <rect x="70" y="100" width="120" height="40" fill="url(#referenceGradient)" stroke="#1d4ed8" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="130" y="125" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">const gestor</text>
  
  <!-- Objeto retornado -->
  <rect x="220" y="100" width="180" height="80" fill="url(#activeGradient)" stroke="#047857" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="310" y="120" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">Objeto API</text>
  <text x="310" y="140" text-anchor="middle" font-size="10" fill="#fff">• obtenerEstado()</text>
  <text x="310" y="155" text-anchor="middle" font-size="10" fill="#fff">• actualizarEstado()</text>
  <text x="310" y="170" text-anchor="middle" font-size="10" fill="#fff">• suscribirse()</text>
  
  <!-- Variables del closure -->
  <rect x="70" y="200" width="330" height="80" fill="#dcfce7" stroke="#16a34a" stroke-width="2" rx="5"/>
  <text x="80" y="220" font-size="12" font-weight="bold" fill="#16a34a">Variables del Closure (Heap)</text>
  <rect x="80" y="230" width="80" height="20" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="120" y="243" text-anchor="middle" font-size="10" fill="#14532d">estado: {}</text>
  <rect x="170" y="230" width="80" height="20" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="210" y="243" text-anchor="middle" font-size="10" fill="#14532d">historial: []</text>
  <rect x="260" y="230" width="80" height="20" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="300" y="243" text-anchor="middle" font-size="10" fill="#14532d">contador: 0</text>
  <rect x="80" y="255" width="260" height="20" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="210" y="268" text-anchor="middle" font-size="10" fill="#14532d">funciones privadas: validarCambio(), registrarCambio()</text>
  
  <!-- Escenario 2: Closure Liberado -->
  <rect x="470" y="60" width="380" height="240" fill="#fef2f2" stroke="#dc2626" stroke-width="2" rx="10"/>
  <text x="480" y="85" font-size="16" font-weight="bold" fill="#dc2626">Closure Liberado (Garbage Collection)</text>
  
  <!-- Variable global nula -->
  <rect x="490" y="100" width="120" height="40" fill="#fca5a5" stroke="#dc2626" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="550" y="125" text-anchor="middle" font-size="12" font-weight="bold" fill="#7f1d1d">gestor = null</text>
  
  <!-- Objeto marcado para GC -->
  <rect x="640" y="100" width="180" height="80" fill="url(#gcGradient)" stroke="#dc2626" stroke-width="2" rx="5" filter="url(#shadow)" opacity="0.6"/>
  <text x="730" y="120" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">Objeto API</text>
  <text x="730" y="140" text-anchor="middle" font-size="10" fill="#fff">Marcado para GC</text>
  <text x="730" y="155" text-anchor="middle" font-size="10" fill="#fff">Sin referencias</text>
  <text x="730" y="170" text-anchor="middle" font-size="10" fill="#fff">activas</text>
  
  <!-- Variables liberadas -->
  <rect x="490" y="200" width="330" height="80" fill="#fee2e2" stroke="#dc2626" stroke-width="2" rx="5" opacity="0.6"/>
  <text x="500" y="220" font-size="12" font-weight="bold" fill="#dc2626">Variables Liberadas</text>
  <rect x="500" y="230" width="80" height="20" fill="#fca5a5" stroke="#dc2626" stroke-width="1" rx="3" opacity="0.6"/>
  <text x="540" y="243" text-anchor="middle" font-size="10" fill="#7f1d1d">estado: ❌</text>
  <rect x="590" y="230" width="80" height="20" fill="#fca5a5" stroke="#dc2626" stroke-width="1" rx="3" opacity="0.6"/>
  <text x="630" y="243" text-anchor="middle" font-size="10" fill="#7f1d1d">historial: ❌</text>
  <rect x="680" y="230" width="80" height="20" fill="#fca5a5" stroke="#dc2626" stroke-width="1" rx="3" opacity="0.6"/>
  <text x="720" y="243" text-anchor="middle" font-size="10" fill="#7f1d1d">contador: ❌</text>
  <rect x="500" y="255" width="260" height="20" fill="#fca5a5" stroke="#dc2626" stroke-width="1" rx="3" opacity="0.6"/>
  <text x="630" y="268" text-anchor="middle" font-size="10" fill="#7f1d1d">funciones privadas: ❌</text>
  
  <!-- Memory Leak Warning -->
  <rect x="50" y="330" width="800" height="120" fill="#fffbeb" stroke="#f59e0b" stroke-width="2" rx="10"/>
  <text x="60" y="355" font-size="16" font-weight="bold" fill="#f59e0b">⚠️ Prevención de Memory Leaks</text>
  
  <!-- Leak Example -->
  <rect x="70" y="370" width="360" height="60" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" rx="5"/>
  <text x="80" y="390" font-size="12" font-weight="bold" fill="#92400e">❌ Patrón Problemático:</text>
  <text x="80" y="405" font-size="11" fill="#92400e">const obj = { data: largeArray };</text>
  <text x="80" y="420" font-size="11" fill="#92400e">obj.self = obj; // Referencia circular</text>
  
  <!-- Good Practice -->
  <rect x="450" y="370" width="380" height="60" fill="#dcfce7" stroke="#16a34a" stroke-width="2" rx="5"/>
  <text x="460" y="390" font-size="12" font-weight="bold" fill="#16a34a">✅ Buena Práctica:</text>
  <text x="460" y="405" font-size="11" fill="#16a34a">// Limpiar referencias cuando no se necesiten</text>
  <text x="460" y="420" font-size="11" fill="#16a34a">gestor = null; // Permite garbage collection</text>
  
  <!-- Performance Tips -->
  <rect x="50" y="470" width="800" height="110" fill="#f8fafc" stroke="#64748b" stroke-width="2" rx="10"/>
  <text x="60" y="495" font-size="16" font-weight="bold" fill="#374151">💡 Consejos de Performance</text>
  
  <rect x="70" y="510" width="240" height="60" fill="#e0e7ff" stroke="#3b82f6" stroke-width="1" rx="5"/>
  <text x="80" y="530" font-size="12" font-weight="bold" fill="#1e40af">1. Minimizar Scope</text>
  <text x="80" y="545" font-size="10" fill="#1e40af">Solo mantener variables</text>
  <text x="80" y="560" font-size="10" fill="#1e40af">realmente necesarias</text>
  
  <rect x="330" y="510" width="240" height="60" fill="#f0fdf4" stroke="#16a34a" stroke-width="1" rx="5"/>
  <text x="340" y="530" font-size="12" font-weight="bold" fill="#16a34a">2. Cleanup Explícito</text>
  <text x="340" y="545" font-size="10" fill="#16a34a">Implementar métodos</text>
  <text x="340" y="560" font-size="10" fill="#16a34a">destroy() o cleanup()</text>
  
  <rect x="590" y="510" width="240" height="60" fill="#fef3c7" stroke="#f59e0b" stroke-width="1" rx="5"/>
  <text x="600" y="530" font-size="12" font-weight="bold" fill="#92400e">3. WeakMap/WeakSet</text>
  <text x="600" y="545" font-size="10" fill="#92400e">Para referencias débiles</text>
  <text x="600" y="560" font-size="10" fill="#92400e">cuando sea apropiado</text>
  
  <!-- Flechas de referencia -->
  <line x1="190" y1="120" x2="220" y2="120" stroke="#3b82f6" stroke-width="2" marker-end="url(#refArrow)"/>
  <line x1="310" y1="180" x2="235" y2="200" stroke="#16a34a" stroke-width="2" marker-end="url(#arrow)"/>
  
  <!-- Flecha de liberación -->
  <line x1="610" y1="120" x2="640" y2="120" stroke="#dc2626" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="730" y1="180" x2="655" y2="200" stroke="#dc2626" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Etiquetas -->
  <text x="205" y="115" font-size="10" fill="#3b82f6">referencia</text>
  <text x="270" y="195" font-size="10" fill="#16a34a">acceso closure</text>
  <text x="625" y="115" font-size="10" fill="#dc2626">sin referencia</text>
  <text x="690" y="195" font-size="10" fill="#dc2626">GC eligible</text>
  
  <!-- Indicador de tiempo -->
  <rect x="400" y="320" width="100" height="30" fill="#6366f1" stroke="#4f46e5" stroke-width="2" rx="15"/>
  <text x="450" y="340" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">TIEMPO</text>
  <path d="M 350 335 L 400 335" stroke="#374151" stroke-width="2" marker-end="url(#arrow)"/>
  <path d="M 500 335 L 550 335" stroke="#374151" stroke-width="2" marker-end="url(#arrow)"/>
</svg>
