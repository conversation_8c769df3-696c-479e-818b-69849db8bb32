# **Capítulo 2 - Variables y Tipos de Datos**

## **📖 Descripción del Capítulo**

Este capítulo es fundamental para comprender cómo JavaScript maneja los datos y la memoria. Aprenderás las diferencias cruciales entre var, let y const, dominarás todos los tipos de datos primitivos y complejos, comprenderás las conversiones de tipos que pueden causar errores sutiles, y explorarás conceptos avanzados como scope y hoisting que son esenciales para escribir código JavaScript profesional.

## **🎯 Objetivos de Aprendizaje**

Al completar este capítulo, serás capaz de:

- [ ] Declarar variables correctamente usando var, let y const según el contexto
- [ ] Identificar y trabajar con todos los tipos de datos primitivos de JavaScript
- [ ] Manipular tipos de datos complejos como objetos y arrays
- [ ] Controlar las conversiones de tipos implícitas y explícitas
- [ ] Comprender y aplicar conceptos de scope y hoisting
- [ ] Evitar errores comunes relacionados con variables y tipos
- [ ] Escribir código más predecible y mantenible

## **📊 Información del Capítulo**

- **Dificultad:** ⭐⭐⭐ (Intermedio)
- **Tiempo estimado:** 10-15 horas
- **Temas:** 5
- **Subtemas:** 50
- **Ejercicios prácticos:** 25
- **Proyectos:** 5

## **📋 Índice de Temas**

### **[2.1. Declaración de Variables (var, let, const)](2.1.%20Declaración%20de%20Variables/README.md)** ⭐⭐⭐
**Tiempo:** 2-3 horas | **Subtemas:** 10

Aprende las diferencias fundamentales entre var, let y const, cuándo usar cada una y cómo evitar errores comunes.

**Contenido:**
- [2.1.1 - Introducción a las Variables](2.1.%20Declaración%20de%20Variables/contenido/2.1.1%20-%20Introducción%20a%20las%20Variables.md)
- [2.1.2 - Declaración con var](2.1.%20Declaración%20de%20Variables/contenido/2.1.2%20-%20Declaración%20con%20var.md)
- [2.1.3 - Declaración con let](2.1.%20Declaración%20de%20Variables/contenido/2.1.3%20-%20Declaración%20con%20let.md)
- [2.1.4 - Declaración con const](2.1.%20Declaración%20de%20Variables/contenido/2.1.4%20-%20Declaración%20con%20const.md)
- [2.1.5 - Diferencias entre var, let y const](2.1.%20Declaración%20de%20Variables/contenido/2.1.5%20-%20Diferencias%20entre%20var,%20let%20y%20const.md)
- [2.1.6 - Block Scope vs Function Scope](2.1.%20Declaración%20de%20Variables/contenido/2.1.6%20-%20Block%20Scope%20vs%20Function%20Scope.md)
- [2.1.7 - Temporal Dead Zone](2.1.%20Declaración%20de%20Variables/contenido/2.1.7%20-%20Temporal%20Dead%20Zone.md)
- [2.1.8 - Redeclaración y Reasignación](2.1.%20Declaración%20de%20Variables/contenido/2.1.8%20-%20Redeclaración%20y%20Reasignación.md)
- [2.1.9 - Mejores Prácticas](2.1.%20Declaración%20de%20Variables/contenido/2.1.9%20-%20Mejores%20Prácticas.md)
- [2.1.10 - Casos de Uso Avanzados](2.1.%20Declaración%20de%20Variables/contenido/2.1.10%20-%20Casos%20de%20Uso%20Avanzados.md)

---

### **[2.2. Tipos de Datos Primitivos](2.2.%20Tipos%20de%20Datos%20Primitivos/README.md)** ⭐⭐⭐
**Tiempo:** 2-3 horas | **Subtemas:** 10

Domina todos los tipos primitivos: string, number, boolean, undefined, null, symbol y bigint.

**Contenido:**
- [2.2.1 - Introducción a Tipos Primitivos](2.2.%20Tipos%20de%20Datos%20Primitivos/contenido/2.2.1%20-%20Introducción%20a%20Tipos%20Primitivos.md)
- [2.2.2 - Tipo String](2.2.%20Tipos%20de%20Datos%20Primitivos/contenido/2.2.2%20-%20Tipo%20String.md)
- [2.2.3 - Tipo Number](2.2.%20Tipos%20de%20Datos%20Primitivos/contenido/2.2.3%20-%20Tipo%20Number.md)
- [2.2.4 - Tipo Boolean](2.2.%20Tipos%20de%20Datos%20Primitivos/contenido/2.2.4%20-%20Tipo%20Boolean.md)
- [2.2.5 - Undefined vs Null](2.2.%20Tipos%20de%20Datos%20Primitivos/contenido/2.2.5%20-%20Undefined%20vs%20Null.md)
- [2.2.6 - Tipo Symbol](2.2.%20Tipos%20de%20Datos%20Primitivos/contenido/2.2.6%20-%20Tipo%20Symbol.md)
- [2.2.7 - Tipo BigInt](2.2.%20Tipos%20de%20Datos%20Primitivos/contenido/2.2.7%20-%20Tipo%20BigInt.md)
- [2.2.8 - Operador typeof](2.2.%20Tipos%20de%20Datos%20Primitivos/contenido/2.2.8%20-%20Operador%20typeof.md)
- [2.2.9 - Inmutabilidad de Primitivos](2.2.%20Tipos%20de%20Datos%20Primitivos/contenido/2.2.9%20-%20Inmutabilidad%20de%20Primitivos.md)
- [2.2.10 - Comparación de Primitivos](2.2.%20Tipos%20de%20Datos%20Primitivos/contenido/2.2.10%20-%20Comparación%20de%20Primitivos.md)

---

### **[2.3. Tipos de Datos Complejos](2.3.%20Tipos%20de%20Datos%20Complejos/README.md)** ⭐⭐⭐⭐
**Tiempo:** 3-4 horas | **Subtemas:** 10

Explora objetos, arrays, funciones y otros tipos de referencia, incluyendo conceptos de mutabilidad.

**Contenido:**
- [2.3.1 - Introducción a Tipos Complejos](2.3.%20Tipos%20de%20Datos%20Complejos/contenido/2.3.1%20-%20Introducción%20a%20Tipos%20Complejos.md)
- [2.3.2 - Objetos Básicos](2.3.%20Tipos%20de%20Datos%20Complejos/contenido/2.3.2%20-%20Objetos%20Básicos.md)
- [2.3.3 - Arrays Fundamentales](2.3.%20Tipos%20de%20Datos%20Complejos/contenido/2.3.3%20-%20Arrays%20Fundamentales.md)
- [2.3.4 - Funciones como Objetos](2.3.%20Tipos%20de%20Datos%20Complejos/contenido/2.3.4%20-%20Funciones%20como%20Objetos.md)
- [2.3.5 - Tipos por Valor vs Referencia](2.3.%20Tipos%20de%20Datos%20Complejos/contenido/2.3.5%20-%20Tipos%20por%20Valor%20vs%20Referencia.md)
- [2.3.6 - Mutabilidad e Inmutabilidad](2.3.%20Tipos%20de%20Datos%20Complejos/contenido/2.3.6%20-%20Mutabilidad%20e%20Inmutabilidad.md)
- [2.3.7 - Clonación Superficial y Profunda](2.3.%20Tipos%20de%20Datos%20Complejos/contenido/2.3.7%20-%20Clonación%20Superficial%20y%20Profunda.md)
- [2.3.8 - Comparación de Objetos](2.3.%20Tipos%20de%20Datos%20Complejos/contenido/2.3.8%20-%20Comparación%20de%20Objetos.md)
- [2.3.9 - Garbage Collection](2.3.%20Tipos%20de%20Datos%20Complejos/contenido/2.3.9%20-%20Garbage%20Collection.md)
- [2.3.10 - Estructuras de Datos Avanzadas](2.3.%20Tipos%20de%20Datos%20Complejos/contenido/2.3.10%20-%20Estructuras%20de%20Datos%20Avanzadas.md)

---

### **[2.4. Conversión de Tipos](2.4.%20Conversión%20de%20Tipos/README.md)** ⭐⭐⭐⭐
**Tiempo:** 2-3 horas | **Subtemas:** 10

Comprende la coerción implícita y explícita, evita errores comunes y controla las conversiones.

**Contenido:**
- [2.4.1 - Introducción a Conversión de Tipos](2.4.%20Conversión%20de%20Tipos/contenido/2.4.1%20-%20Introducción%20a%20Conversión%20de%20Tipos.md)
- [2.4.2 - Coerción Implícita](2.4.%20Conversión%20de%20Tipos/contenido/2.4.2%20-%20Coerción%20Implícita.md)
- [2.4.3 - Conversión Explícita](2.4.%20Conversión%20de%20Tipos/contenido/2.4.3%20-%20Conversión%20Explícita.md)
- [2.4.4 - Conversión a String](2.4.%20Conversión%20de%20Tipos/contenido/2.4.4%20-%20Conversión%20a%20String.md)
- [2.4.5 - Conversión a Number](2.4.%20Conversión%20de%20Tipos/contenido/2.4.5%20-%20Conversión%20a%20Number.md)
- [2.4.6 - Conversión a Boolean](2.4.%20Conversión%20de%20Tipos/contenido/2.4.6%20-%20Conversión%20a%20Boolean.md)
- [2.4.7 - Truthy y Falsy Values](2.4.%20Conversión%20de%20Tipos/contenido/2.4.7%20-%20Truthy%20y%20Falsy%20Values.md)
- [2.4.8 - Operadores de Comparación](2.4.%20Conversión%20de%20Tipos/contenido/2.4.8%20-%20Operadores%20de%20Comparación.md)
- [2.4.9 - Casos Problemáticos](2.4.%20Conversión%20de%20Tipos/contenido/2.4.9%20-%20Casos%20Problemáticos.md)
- [2.4.10 - Mejores Prácticas](2.4.%20Conversión%20de%20Tipos/contenido/2.4.10%20-%20Mejores%20Prácticas.md)

---

### **[2.5. Scope y Hoisting](2.5.%20Scope%20y%20Hoisting/README.md)** ⭐⭐⭐⭐⭐
**Tiempo:** 3-4 horas | **Subtemas:** 10

Domina conceptos avanzados de ámbito de variables, elevación y contexto de ejecución.

**Contenido:**
- [2.5.1 - Introducción al Scope](2.5.%20Scope%20y%20Hoisting/contenido/2.5.1%20-%20Introducción%20al%20Scope.md)
- [2.5.2 - Global Scope](2.5.%20Scope%20y%20Hoisting/contenido/2.5.2%20-%20Global%20Scope.md)
- [2.5.3 - Function Scope](2.5.%20Scope%20y%20Hoisting/contenido/2.5.3%20-%20Function%20Scope.md)
- [2.5.4 - Block Scope](2.5.%20Scope%20y%20Hoisting/contenido/2.5.4%20-%20Block%20Scope.md)
- [2.5.5 - Lexical Scope](2.5.%20Scope%20y%20Hoisting/contenido/2.5.5%20-%20Lexical%20Scope.md)
- [2.5.6 - Hoisting de Variables](2.5.%20Scope%20y%20Hoisting/contenido/2.5.6%20-%20Hoisting%20de%20Variables.md)
- [2.5.7 - Hoisting de Funciones](2.5.%20Scope%20y%20Hoisting/contenido/2.5.7%20-%20Hoisting%20de%20Funciones.md)
- [2.5.8 - Execution Context](2.5.%20Scope%20y%20Hoisting/contenido/2.5.8%20-%20Execution%20Context.md)
- [2.5.9 - Scope Chain](2.5.%20Scope%20y%20Hoisting/contenido/2.5.9%20-%20Scope%20Chain.md)
- [2.5.10 - Closures Introducción](2.5.%20Scope%20y%20Hoisting/contenido/2.5.10%20-%20Closures%20Introducción.md)

---

## **🎯 Rutas de Aprendizaje del Capítulo**

### **🚀 Ruta Rápida (6-8 horas)**
Enfoque en conceptos esenciales para programar efectivamente.

**Temas recomendados:**
- 2.1 - Declaración de Variables (subtemas 2.1.1, 2.1.3, 2.1.4, 2.1.5)
- 2.2 - Tipos Primitivos (subtemas 2.2.1, 2.2.2, 2.2.3, 2.2.4, 2.2.5)
- 2.4 - Conversión de Tipos (subtemas 2.4.1, 2.4.7, 2.4.10)

### **📚 Ruta Completa (10-15 horas)**
Cobertura completa de todos los fundamentos.

**Incluye:**
- Todos los temas y subtemas
- Todos los ejercicios prácticos
- Proyectos del capítulo
- Evaluaciones completas

### **🔬 Ruta Experto (15-20 horas)**
Para dominio completo y comprensión profunda.

**Incluye:**
- Ruta completa
- Ejercicios avanzados adicionales
- Investigación de edge cases
- Optimizaciones de memoria
- Patrones avanzados

---

## **📊 Progreso del Capítulo**

```
Tema 2.1: [░░░░░░░░░░] 0% completado
Tema 2.2: [░░░░░░░░░░] 0% completado
Tema 2.3: [░░░░░░░░░░] 0% completado
Tema 2.4: [░░░░░░░░░░] 0% completado
Tema 2.5: [░░░░░░░░░░] 0% completado

Progreso Total: [░░░░░░░░░░] 0% completado
```

## **🏆 Logros del Capítulo**

- 📝 **Declarador**: Dominar var, let y const
- 🔢 **Tipificador**: Conocer todos los tipos de datos
- 🔄 **Convertidor**: Controlar conversiones de tipos
- 🎯 **Scopeador**: Entender scope y hoisting
- 🧠 **Experto en Datos**: Completar todos los temas
- 🚀 **Maestro**: Completar ruta experto

---

## **📝 Evaluación del Capítulo**

### **Quiz Final**
- 30 preguntas sobre conceptos clave
- Tiempo límite: 45 minutos
- Puntuación mínima: 80%

### **Proyecto Práctico**
- Sistema de gestión de datos
- Validación de tipos
- Conversiones seguras
- Manejo de scope

---

## **🔗 Recursos Adicionales**

- [MDN - JavaScript Data Types](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures)
- [You Don't Know JS - Types & Grammar](https://github.com/getify/You-Dont-Know-JS)
- [JavaScript.info - Data Types](https://javascript.info/types)
- [ECMAScript Specification](https://tc39.es/ecma262/)

---

## **➡️ Navegación**

⬅️ **Anterior:** [Capítulo 1 - Introducción y Configuración](../1%20-%20Introducción%20y%20Configuración/README.md)  
➡️ **Siguiente:** [Capítulo 3 - Operadores y Expresiones](../3%20-%20Operadores%20y%20Expresiones/README.md)  
🏠 **Parte:** [Parte I - Fundamentos Básicos](../README.md)
