# 4.5.5 - Contextual keywords

## Introducción
Las contextual keywords en JavaScript son palabras como 'async', 'await', 'of' o 'from' que actúan como keywords solo en contextos específicos, permitiendo su uso como identificadores en otros lugares, lo que ofrece flexibilidad en la sintaxis sin restringir completamente su empleo, pero requiriendo cuidado para evitar errores en parsers o comportamientos inesperados en código asíncrono o iteraciones. Estas keywords dependen del contexto gramatical, como en funciones async o imports, y entender su comportamiento es clave para escribir código moderno y eficiente, especialmente en ES6+ donde facilitan patrones avanzados, promoviendo prácticas que equilibren innovación con compatibilidad en proyectos que evolucionan con el estándar ECMAScript.

## Ejemplo de Código
```javascript
let async = 'value'; // Válido fuera de contexto
async function myFunc() { await Promise.resolve(); } // 'async' y 'await' como keywords
console.log(async);
```

## Resultados en Consola
value

## Diagrama de Flujo de Código
<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg"><rect x="10" y="10" width="280" height="180" fill="none" stroke="black"/><text x="150" y="50" text-anchor="middle">Contexto específico</text><line x1="150" y1="60" x2="150" y2="100" stroke="black"/><text x="150" y="130" text-anchor="middle">Keyword activada</text><line x1="150" y1="140" x2="150" y2="180" stroke="black"/></svg>

## Visualización Conceptual
<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg"><ellipse cx="100" cy="100" rx="80" ry="60" fill="lightpink"/><text x="100" y="100" text-anchor="middle">Contextual</text><text x="100" y="120" text-anchor="middle">Keywords</text></svg>

## Casos de Uso
- Uso de 'async' en funciones para manejo asíncrono.
- Aplicación de 'await' en promesas para código síncrono-like.
- Empleo de 'of' en bucles for-of para iteración de iterables.
- Utilización de 'from' en imports dinámicos para módulos.
- Implementación de 'target' en decorators para metaprogramación.

## Errores Comunes
- Usar 'async' como variable en contextos asíncronos, causando SyntaxError.
- Confundir 'await' fuera de async functions.
- Mal uso de 'of' en for-in loops.
- Olvidar contexto para 'from' en imports.
- Ignorar versiones de ECMAScript para contextual keywords.

## Recomendaciones
- Verificar contextos gramaticales en la spec de ECMAScript.
- Usar transpilers como Babel para compatibilidad.
- Configurar linters para warnings sobre contextual keywords.
- Testear código en diferentes entornos para consistencia.
- Documentar usos contextuales en comentarios de código.