const fs = require('fs');
const path = require('path');

// Estructura completa de la Parte I basada en el índice
const estructuraParteI = {
  "1 - Introducción y Configuración": {
    temas: [
      "1.1. Historia y Evolución de JavaScript",
      "1.2. Configuración del Entorno", 
      "1.3. Herramientas de Desarrollo",
      "1.4. Primer Programa",
      "1.5. Debugging Básico",
      "1.6. Configuración de Proyecto",
      "1.7. Herramientas de Build"
    ]
  },
  "2 - Sintaxis Fundamental": {
    temas: [
      "2.1. Comentarios y Documentación",
      "2.2. Palabras Reservadas",
      "2.3. Identificadores y Convenciones", 
      "2.4. Literales",
      "2.5. Separadores y Delimitadores",
      "2.6. Estructura de Código",
      "2.7. Encoding y Unicode",
      "2.8. Strict Mode"
    ]
  },
  "3 - Variables y Tipos de Datos": {
    temas: [
      "3.1. Declaración de Variables",
      "3.2. Tipos Primitivos",
      "3.3. Objetos y Referencias",
      "3.4. Conversión de Tipos", 
      "3.5. Scope y Hoisting",
      "3.6. Temporal Dead Zone",
      "3.7. Constantes e Inmutabilidad",
      "3.8. Destructuring Básico"
    ]
  },
  "4 - Operadores": {
    temas: [
      "4.1. Operadores Aritméticos",
      "4.2. Operadores de Asignación",
      "4.3. Operadores de Comparación",
      "4.4. Operadores Lógicos",
      "4.5. Operadores Unarios", 
      "4.6. Operador Ternario",
      "4.7. Operadores de Cadena",
      "4.8. Precedencia y Asociatividad"
    ]
  },
  "5 - Estructuras de Control": {
    temas: [
      "5.1. Estructuras Condicionales",
      "5.2. Bucles Básicos",
      "5.3. Bucles Avanzados",
      "5.4. Control de Flujo",
      "5.5. Switch Statement",
      "5.6. Etiquetas y Saltos", 
      "5.7. Patrones de Control"
    ]
  },
  "6 - Manejo de Errores Básico": {
    temas: [
      "6.1. Try-Catch-Finally",
      "6.2. Tipos de Errores", 
      "6.3. Throw Statement",
      "6.4. Error Objects",
      "6.5. Debugging Strategies",
      "6.6. Best Practices"
    ]
  }
};

function crearDirectorio(ruta) {
  if (!fs.existsSync(ruta)) {
    fs.mkdirSync(ruta, { recursive: true });
    console.log(`✅ Creado: ${ruta}`);
  }
}

function crearEstructuraTema(rutaTema, numeroTema, nombreTema) {
  // Crear carpetas principales según ESTRUCTURA-MEJORADA.md
  const carpetas = [
    'CONTENIDO',
    'EJEMPLOS', 
    'EJERCICIOS',
    'RECURSOS',
    'EVALUACION',
    'VISUALIZACIONES',
    'REFERENCIAS'
  ];

  carpetas.forEach(carpeta => {
    const rutaCarpeta = path.join(rutaTema, carpeta);
    crearDirectorio(rutaCarpeta);

    // Crear subcarpetas específicas según la estructura mejorada
    switch(carpeta) {
      case 'CONTENIDO':
        ['teoria', 'sintaxis', 'ejemplos-inline', 'notas'].forEach(sub => {
          crearDirectorio(path.join(rutaCarpeta, sub));
        });
        break;
      case 'EJEMPLOS':
        ['basicos', 'intermedios', 'avanzados', 'interactivos'].forEach(sub => {
          crearDirectorio(path.join(rutaCarpeta, sub));
        });
        break;
      case 'EJERCICIOS':
        ['practicos', 'desafios', 'proyectos', 'soluciones'].forEach(sub => {
          crearDirectorio(path.join(rutaCarpeta, sub));
        });
        break;
      case 'RECURSOS':
        ['documentacion', 'herramientas', 'enlaces', 'cheatsheets'].forEach(sub => {
          crearDirectorio(path.join(rutaCarpeta, sub));
        });
        break;
      case 'EVALUACION':
        ['quiz', 'tests', 'rubrica', 'autoevaluacion'].forEach(sub => {
          crearDirectorio(path.join(rutaCarpeta, sub));
        });
        break;
      case 'VISUALIZACIONES':
        ['diagramas', 'flowcharts', 'mapas-mentales', 'infografias'].forEach(sub => {
          crearDirectorio(path.join(rutaCarpeta, sub));
        });
        break;
      case 'REFERENCIAS':
        ['mdn', 'especificaciones', 'articulos', 'videos'].forEach(sub => {
          crearDirectorio(path.join(rutaCarpeta, sub));
        });
        break;
    }
  });

  // Crear README del tema
  const readmeContent = `# **${nombreTema}**

## **📖 Descripción**
[Descripción detallada del tema]

## **🎯 Objetivos**
- [ ] Objetivo 1
- [ ] Objetivo 2
- [ ] Objetivo 3

## **📚 Contenido**
- [Teoría](CONTENIDO/teoria/README.md)
- [Sintaxis](CONTENIDO/sintaxis/README.md)
- [Ejemplos](CONTENIDO/ejemplos-inline/README.md)

## **💻 Ejemplos Prácticos**
- [Básicos](EJEMPLOS/basicos/README.md)
- [Intermedios](EJEMPLOS/intermedios/README.md)
- [Avanzados](EJEMPLOS/avanzados/README.md)

## **🏋️ Ejercicios**
- [Prácticos](EJERCICIOS/practicos/README.md)
- [Desafíos](EJERCICIOS/desafios/README.md)
- [Proyecto](EJERCICIOS/proyectos/README.md)

## **📊 Evaluación**
- [Quiz](EVALUACION/quiz/README.md)
- [Tests](EVALUACION/tests/README.md)
- [Autoevaluación](EVALUACION/autoevaluacion/README.md)

---

**¡Domina este tema y continúa tu aprendizaje!** 🚀`;

  fs.writeFileSync(path.join(rutaTema, 'README.md'), readmeContent);
}

function generarEstructuraParteI() {
  console.log('🚀 Generando estructura completa de Parte I...\n');
  
  const rutaBase = 'PARTE I - FUNDAMENTOS BÁSICOS';
  
  Object.entries(estructuraParteI).forEach(([nombreCapitulo, datos]) => {
    console.log(`📚 Procesando: ${nombreCapitulo}`);
    
    const rutaCapitulo = path.join(rutaBase, nombreCapitulo);
    crearDirectorio(rutaCapitulo);
    
    // Procesar cada tema
    datos.temas.forEach(tema => {
      console.log(`  📝 Procesando: ${tema}`);
      
      const rutaTema = path.join(rutaCapitulo, tema);
      crearDirectorio(rutaTema);
      
      // Crear estructura completa del tema
      crearEstructuraTema(rutaTema, tema.split('.')[0], tema);
    });
    
    console.log(`✅ Completado: ${nombreCapitulo}\n`);
  });
  
  console.log('🎉 ¡Estructura completa de Parte I generada exitosamente!');
}

// Ejecutar
generarEstructuraParteI();