## 4.3.2. Reglas de ASI en detalle

### Introducción
ASI sigue reglas específicas del spec ECMAScript.  
Inserta ; al final de línea si parse falla.  
No inserta dentro de bloques.  
Restricted productions limitan ASI.  
Offending token triggers ASI.  
¿Conoces las tres reglas principales de ASI?

### Código de Ejemplo
```javascript
// Regla 1: Parse error sin ;
a = b
+ c  // ASI inserta ; antes de +
console.log(a)

// Regla 2: Restricted production
let x = 1
[x] = [2]  // Error, no ASI por restricted

// Regla 3: No line terminator
return
42  // ASI inserta ; después return

// No ASI en continuaciones
let y = 1 +
  2
console.log(y)

// ASI antes de }
{ 1
2 } 3  // ASI después 1 y 2
```
### Resultados en Consola
- `undefined`
- `3`
- SyntaxError (para restricted)
- `undefined` (para return)
- `3`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> Regla1["Paso 1: a = b + c <br> - ASI por parse error"]
    Regla1 --> LogA["Paso 2: console.log(a) <br> - Resultado: undefined"]
    LogA --> Regla2["Paso 3: [x] = [2] <br> - No ASI, restricted"]
    Regla2 --> Error["Paso 4: SyntaxError <br> - Falla parse"]
    Error --> Regla3["Paso 5: return 42 <br> - ASI por line terminator"]
    Regla3 --> Undefined["Paso 6: undefined <br> - Return vacío"]
    Undefined --> NoASI["Paso 7: 1 + 2 <br> - Continuación línea"]
    NoASI --> LogY["Paso 8: console.log(y) <br> - Resultado: 3"]
    LogY --> ASIClosing["Paso 9: {1 2} 3 <br> - ASI antes }"]
    ASIClosing --> Fin["Paso 10: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> StudyRules["Estudiar spec ASI"]
    Regla2 --> NotaRestricted["Nota: No line terminator allowed"]
    Regla3 --> NotaTerminator["Nota: Line break triggers"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Regla1 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogA fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Regla2 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Error fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Regla3 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Undefined fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NoASI fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogY fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ASIClosing fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style StudyRules fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaRestricted fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaTerminator fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Parse["Parse <br> - Intenta parsear"]
        Parse --> Fail["Falla? <br> - Sin ;"]
        Fail -->|Sí| InsertASI["Inserta ; <br> - Regla 1"]
        Fail -->|No| CheckRestricted["Restricted? <br> - Regla 2"]
        CheckRestricted -->|Sí| NoInsert["No inserta <br> - Error"]
        CheckRestricted -->|No| CheckTerminator["Line terminator? <br> - Regla 3"]
        CheckTerminator -->|Sí| InsertASI
        CheckTerminator -->|No| Continue["Continúa <br> - Sin ASI"]
        InsertASI --> Success["Parse exitoso"]
        NoInsert --> Error["Syntax Error"]
        Continue --> Success
        Inicio --> Desarrollador(("Desarrollador")) --> Avoid["Evitar dependencias ASI"]
        Fail --> NotaFail["Nota: Offending token"]
        CheckRestricted --> NotaRestricted["Nota: postfix++, etc."]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Parse fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fail fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style InsertASI fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CheckRestricted fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NoInsert fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CheckTerminator fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Continue fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Success fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Error fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Avoid fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaFail fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaRestricted fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Código sin semicolons.
2. Multiline expressions.
3. Return statements.
4. Increment/decrement operators.
5. Block statements.

### Errores comunes
1. Ignorar restricted productions.
2. Line breaks en restricted places.
3. Confiar en ASI para separation.
4. Mezclar estilos con/ sin ;.
5. No probar parse edge cases.

### Recomendaciones
1. Lee ECMAScript spec.
2. Usa semicolons siempre.
3. Configura linter para ASI.
4. Evita line breaks riesgosos.
5. Testea código minificado.