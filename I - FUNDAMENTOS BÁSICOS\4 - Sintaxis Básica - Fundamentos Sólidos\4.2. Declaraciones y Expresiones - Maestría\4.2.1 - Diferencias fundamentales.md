## 4.2.1. Diferencias fundamentales

### Introducción
Las diferencias fundamentales entre declaraciones y expresiones en JavaScript son clave para entender la sintaxis.  
Las declaraciones crean entidades como variables o funciones, mientras que las expresiones producen valores.  
Esto afecta cómo se usan en el código.  
Comprenderlas previene errores comunes.  
Las declaraciones no retornan valores, a diferencia de las expresiones.  
¿ Cómo distingues entre una declaración y una expresión en tu código?

### Código de Ejemplo
```javascript
// Declaración
let a = 5;

// Expresión
let b = a + 3; // Produce 8

// Declaración de función
function sumar(x, y) {
    return x + y;
}

// Expresión de función
const restar = (x, y) => x - y;

console.log(b);
console.log(sumar(4, 2));
console.log(restar(4, 2));
```
### Resultados en Consola
- `8`
- `6`
- `2`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> Declaracion1["Paso 1: let a = 5 <br> - Declaración"]
    Declaracion1 --> Expresion1["Paso 2: let b = a + 3 <br> - Expresión produce 8"]
    Expresion1 --> Declaracion2["Paso 3: function sumar(x, y) <br> - Declaración de función"]
    Declaracion2 --> Expresion2["Paso 4: const restar = (x, y) => x - y <br> - Expresión de función"]
    Expresion2 --> Log1["Paso 5: console.log(b) <br> - Resultado: 8"]
    Log1 --> Log2["Paso 6: console.log(sumar(4, 2)) <br> - Resultado: 6"]
    Log2 --> Log3["Paso 7: console.log(restar(4, 2)) <br> - Resultado: 2"]
    Log3 --> Fin["Paso 8: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Diferencias["Diferencias"]
    Declaracion1 --> NotaDecl["Nota: No produce valor"]
    Expresion1 --> NotaExp["Nota: Produce valor"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Declaracion1 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Expresion1 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Declaracion2 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Expresion2 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log1 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log2 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log3 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Diferencias fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaDecl fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaExp fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Declaraciones["Declaraciones <br> - Crean entidades"]
        Declaraciones --> NoValor["NoValor <br> - No producen valores"]
        NoValor --> EjemplosDecl["Ejemplos: let, function"]
        Inicio --> Expresiones["Expresiones <br> - Producen valores"]
        Expresiones --> Valor["Valor <br> - Usables en asignaciones"]
        Valor --> EjemplosExp["Ejemplos: a + b, () => {}"]
        Inicio --> Desarrollador(("Desarrollador")) --> Comprension["Comprensión"]
        Declaraciones --> NotaDecl["Nota: Estructurales"]
        Expresiones --> NotaExp["Nota: Evaluables"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Declaraciones fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NoValor fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style EjemplosDecl fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Expresiones fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Valor fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style EjemplosExp fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Comprension fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaDecl fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaExp fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Usar expresiones en condicionales.
2. Declarar variables antes de usarlas en expresiones.
3. Asignar funciones como expresiones a variables.
4. Evitar declaraciones donde se esperan expresiones.
5. Optimizar código entendiendo evaluaciones.

### Errores comunes
1. Usar declaraciones en lugares de expresiones.
2. Confundir hoisting en declaraciones vs expresiones.
3. No capturar valores de expresiones.
4. Mezclar tipos en asignaciones.
5. Ignorar side effects en expresiones.

### Recomendaciones
1. Prefiere const para declaraciones inmutables.
2. Usa arrow functions para expresiones concisas.
3. Entiende contextos donde cada uno se usa.
4. Prueba código para validar diferencias.
5. Lee documentación ECMAScript para profundidad.