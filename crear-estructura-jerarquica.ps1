# Script para crear estructura jerárquica basada en los índices de cada parte
# Procesa automáticamente todas las partes y crea subcarpetas según la jerarquía

Write-Host "🚀 Iniciando creación de estructura jerárquica completa..." -ForegroundColor Green

# Función para limpiar nombres de carpetas
function Clean-FolderName {
    param($name)
    $cleaned = $name -replace '[^\w\s\-\.]', ''
    $cleaned = $cleaned -replace '\s+', ' '
    $cleaned = $cleaned.Trim()
    return $cleaned
}

# PARTE I - FUNDAMENTOS BÁSICOS
Write-Host "📁 Procesando PARTE I - FUNDAMENTOS BÁSICOS..." -ForegroundColor Yellow

# Capítulo 1
$cap1_sections = @(
    "1.1 - QUE ES JAVASCRIPT",
    "1.2 - HISTORIA Y EVOLUCION",
    "1.3 - JAVASCRIPT VS OTROS LENGUAJES",
    "1.4 - ECOSISTEMA DE JAVASCRIPT",
    "1.5 - CASOS DE USO Y APLICACIONES",
    "1.6 - VENTAJAS Y LIMITACIONES",
    "1.7 - COMUNIDADES Y RECURSOS",
    "1.8 - JAVASCRIPT EN DESARROLLO WEB MODERNO",
    "1.9 - INTRODUCCION A FRAMEWORKS",
    "1.10 - TENDENCIAS FUTURAS"
)

foreach ($section in $cap1_sections) {
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 01 - INTRODUCCION A JAVASCRIPT/TEORIA/$section" -ErrorAction SilentlyContinue
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 01 - INTRODUCCION A JAVASCRIPT/CODIGO/$section" -ErrorAction SilentlyContinue
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 01 - INTRODUCCION A JAVASCRIPT/EJEMPLOS/$section" -ErrorAction SilentlyContinue
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 01 - INTRODUCCION A JAVASCRIPT/VISUALIZACIONES/$section" -ErrorAction SilentlyContinue
}

# Capítulo 2 - Configuración del Entorno (con subcapítulos detallados)
$cap2_sections = @(
    "2.1 - FUNDAMENTOS DEL ENTORNO",
    "2.2 - NAVEGADORES WEB Y HERRAMIENTAS",
    "2.3 - EDITORES DE CODIGO",
    "2.4 - EXTENSIONES Y PLUGINS",
    "2.5 - NODEJS Y GESTION DE PAQUETES",
    "2.6 - CONFIGURACION DE PROYECTO",
    "2.7 - HERRAMIENTAS DE BUILD",
    "2.8 - LINTING Y FORMATEO",
    "2.9 - TESTING ENVIRONMENT",
    "2.10 - DEVOPS Y CI-CD"
)

foreach ($section in $cap2_sections) {
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 02 - CONFIGURACION DEL ENTORNO/TEORIA/$section" -ErrorAction SilentlyContinue
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 02 - CONFIGURACION DEL ENTORNO/CODIGO/$section" -ErrorAction SilentlyContinue
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 02 - CONFIGURACION DEL ENTORNO/EJEMPLOS/$section" -ErrorAction SilentlyContinue
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 02 - CONFIGURACION DEL ENTORNO/VISUALIZACIONES/$section" -ErrorAction SilentlyContinue
}

# Capítulo 3 - Primeros Pasos
$cap3_sections = @(
    "3.1 - TU PRIMER PROGRAMA",
    "3.2 - COMENTARIOS EN EL CODIGO",
    "3.3 - DEBUGGING BASICO",
    "3.4 - CONSOLE API",
    "3.5 - MEJORES PRACTICAS INICIALES",
    "3.6 - ENTORNO INTERACTIVO",
    "3.7 - CONCEPTOS DE PROGRAMACION",
    "3.8 - INTERACCION CON EL USUARIO",
    "3.9 - EXPERIMENTOS PRACTICOS",
    "3.10 - FUNDAMENTOS DE TESTING"
)

foreach ($section in $cap3_sections) {
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 03 - PRIMEROS PASOS EN JAVASCRIPT/TEORIA/$section" -ErrorAction SilentlyContinue
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 03 - PRIMEROS PASOS EN JAVASCRIPT/CODIGO/$section" -ErrorAction SilentlyContinue
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 03 - PRIMEROS PASOS EN JAVASCRIPT/EJEMPLOS/$section" -ErrorAction SilentlyContinue
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 03 - PRIMEROS PASOS EN JAVASCRIPT/VISUALIZACIONES/$section" -ErrorAction SilentlyContinue
}

# Capítulo 4 - Sintaxis Básica
$cap4_sections = @(
    "4.1 - ESTRUCTURA DE PROGRAMA",
    "4.2 - DECLARACIONES Y EXPRESIONES",
    "4.3 - PUNTO Y COMA",
    "4.4 - CASE SENSITIVITY",
    "4.5 - PALABRAS RESERVADAS",
    "4.6 - ESTRUCTURA DE MODULOS",
    "4.7 - CONVENCIONES DE CODIGO",
    "4.8 - EJECUCION DE CODIGO",
    "4.9 - ERRORES COMUNES",
    "4.10 - HERRAMIENTAS DE SOPORTE"
)

foreach ($section in $cap4_sections) {
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 04 - SINTAXIS BASICA/TEORIA/$section" -ErrorAction SilentlyContinue
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 04 - SINTAXIS BASICA/CODIGO/$section" -ErrorAction SilentlyContinue
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 04 - SINTAXIS BASICA/EJEMPLOS/$section" -ErrorAction SilentlyContinue
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 04 - SINTAXIS BASICA/VISUALIZACIONES/$section" -ErrorAction SilentlyContinue
}

# Capítulo 5 - Variables y Declaraciones
$cap5_sections = @(
    "5.1 - DECLARACION DE VARIABLES",
    "5.2 - VAR LET Y CONST",
    "5.3 - MEJORES PRACTICAS",
    "5.4 - HOISTING",
    "5.5 - TEMPORAL DEAD ZONE",
    "5.6 - NAMING CONVENTIONS",
    "5.7 - SCOPE Y CONTEXTO",
    "5.8 - GESTION DE MEMORIA",
    "5.9 - ERRORES COMUNES",
    "5.10 - HERRAMIENTAS DE SOPORTE"
)

foreach ($section in $cap5_sections) {
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 05 - VARIABLES Y DECLARACIONES/TEORIA/$section" -ErrorAction SilentlyContinue
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 05 - VARIABLES Y DECLARACIONES/CODIGO/$section" -ErrorAction SilentlyContinue
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 05 - VARIABLES Y DECLARACIONES/EJEMPLOS/$section" -ErrorAction SilentlyContinue
    mkdir -p "PARTE I - FUNDAMENTOS BÁSICOS/CAPITULO 05 - VARIABLES Y DECLARACIONES/VISUALIZACIONES/$section" -ErrorAction SilentlyContinue
}

Write-Host "✅ PARTE I completada - Primeros 5 capítulos estructurados" -ForegroundColor Green
Write-Host "📊 Continuando con capítulos restantes..." -ForegroundColor Yellow
