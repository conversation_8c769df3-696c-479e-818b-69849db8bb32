/**
 * MODELO DE USUARIO
 * =================
 * 
 * Modelo completo para usuarios de la plataforma CodeCraft Academy
 * Incluye autenticación, progreso, preferencias y gamificación
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

const userSchema = new mongoose.Schema({
    // Información básica
    username: {
        type: String,
        required: [true, 'El nombre de usuario es requerido'],
        unique: true,
        trim: true,
        minlength: [3, 'El nombre de usuario debe tener al menos 3 caracteres'],
        maxlength: [30, 'El nombre de usuario no puede exceder 30 caracteres'],
        match: [/^[a-zA-Z0-9_-]+$/, 'El nombre de usuario solo puede contener letras, números, guiones y guiones bajos']
    },
    
    email: {
        type: String,
        required: [true, 'El email es requerido'],
        unique: true,
        lowercase: true,
        trim: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Por favor ingresa un email válido']
    },
    
    password: {
        type: String,
        required: function() {
            return !this.googleId && !this.githubId; // Password requerido solo si no es OAuth
        },
        minlength: [8, 'La contraseña debe tener al menos 8 caracteres'],
        select: false // No incluir en queries por defecto
    },
    
    // Información personal
    profile: {
        firstName: {
            type: String,
            trim: true,
            maxlength: [50, 'El nombre no puede exceder 50 caracteres']
        },
        lastName: {
            type: String,
            trim: true,
            maxlength: [50, 'El apellido no puede exceder 50 caracteres']
        },
        avatar: {
            type: String,
            default: null
        },
        bio: {
            type: String,
            maxlength: [500, 'La biografía no puede exceder 500 caracteres']
        },
        location: {
            country: String,
            city: String,
            timezone: {
                type: String,
                default: 'UTC'
            }
        },
        birthDate: Date,
        website: String,
        github: String,
        linkedin: String,
        twitter: String
    },
    
    // Autenticación OAuth
    googleId: String,
    githubId: String,
    
    // Estado de la cuenta
    isEmailVerified: {
        type: Boolean,
        default: false
    },
    
    emailVerificationToken: String,
    emailVerificationExpires: Date,
    
    passwordResetToken: String,
    passwordResetExpires: Date,
    
    // Roles y permisos
    role: {
        type: String,
        enum: ['student', 'instructor', 'admin', 'moderator'],
        default: 'student'
    },
    
    permissions: [{
        type: String,
        enum: [
            'create_course',
            'edit_course',
            'delete_course',
            'moderate_forum',
            'manage_users',
            'view_analytics',
            'issue_certificates'
        ]
    }],
    
    // Suscripción y pagos
    subscription: {
        plan: {
            type: String,
            enum: ['free', 'basic', 'premium', 'enterprise'],
            default: 'free'
        },
        status: {
            type: String,
            enum: ['active', 'inactive', 'cancelled', 'past_due'],
            default: 'active'
        },
        startDate: Date,
        endDate: Date,
        stripeCustomerId: String,
        stripeSubscriptionId: String
    },
    
    // Progreso académico
    academicProgress: {
        totalCoursesEnrolled: {
            type: Number,
            default: 0
        },
        totalCoursesCompleted: {
            type: Number,
            default: 0
        },
        totalLessonsCompleted: {
            type: Number,
            default: 0
        },
        totalExercisesCompleted: {
            type: Number,
            default: 0
        },
        totalProjectsCompleted: {
            type: Number,
            default: 0
        },
        totalStudyTimeMinutes: {
            type: Number,
            default: 0
        },
        currentStreak: {
            type: Number,
            default: 0
        },
        longestStreak: {
            type: Number,
            default: 0
        },
        lastActivityDate: Date
    },
    
    // Gamificación
    gamification: {
        level: {
            type: Number,
            default: 1
        },
        experience: {
            type: Number,
            default: 0
        },
        totalPoints: {
            type: Number,
            default: 0
        },
        badges: [{
            badgeId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Badge'
            },
            earnedAt: {
                type: Date,
                default: Date.now
            }
        }],
        achievements: [{
            achievementId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Achievement'
            },
            progress: {
                type: Number,
                default: 0
            },
            completed: {
                type: Boolean,
                default: false
            },
            completedAt: Date
        }]
    },
    
    // Preferencias
    preferences: {
        language: {
            type: String,
            default: 'es',
            enum: ['es', 'en', 'fr', 'de', 'pt', 'it']
        },
        theme: {
            type: String,
            default: 'dark',
            enum: ['light', 'dark', 'auto']
        },
        notifications: {
            email: {
                courseUpdates: { type: Boolean, default: true },
                newCourses: { type: Boolean, default: true },
                achievements: { type: Boolean, default: true },
                reminders: { type: Boolean, default: true },
                newsletter: { type: Boolean, default: false }
            },
            push: {
                courseUpdates: { type: Boolean, default: true },
                achievements: { type: Boolean, default: true },
                reminders: { type: Boolean, default: true }
            }
        },
        privacy: {
            showProfile: { type: Boolean, default: true },
            showProgress: { type: Boolean, default: true },
            showBadges: { type: Boolean, default: true }
        }
    },
    
    // Cursos
    enrolledCourses: [{
        courseId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Course',
            required: true
        },
        enrolledAt: {
            type: Date,
            default: Date.now
        },
        progress: {
            type: Number,
            default: 0,
            min: 0,
            max: 100
        },
        completedAt: Date,
        lastAccessedAt: Date,
        certificateIssued: {
            type: Boolean,
            default: false
        }
    }],
    
    // Actividad reciente
    recentActivity: [{
        type: {
            type: String,
            enum: ['lesson_completed', 'exercise_completed', 'project_completed', 'badge_earned', 'course_enrolled', 'course_completed']
        },
        description: String,
        courseId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Course'
        },
        lessonId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Lesson'
        },
        timestamp: {
            type: Date,
            default: Date.now
        }
    }],
    
    // Configuración de cuenta
    accountSettings: {
        twoFactorEnabled: {
            type: Boolean,
            default: false
        },
        twoFactorSecret: String,
        loginNotifications: {
            type: Boolean,
            default: true
        },
        dataRetention: {
            type: Number,
            default: 365 // días
        }
    },
    
    // Metadatos
    lastLoginAt: Date,
    lastLoginIP: String,
    loginCount: {
        type: Number,
        default: 0
    },
    
    isActive: {
        type: Boolean,
        default: true
    },
    
    deactivatedAt: Date,
    deactivationReason: String

}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Índices para optimización
userSchema.index({ email: 1 });
userSchema.index({ username: 1 });
userSchema.index({ 'enrolledCourses.courseId': 1 });
userSchema.index({ role: 1 });
userSchema.index({ 'subscription.plan': 1 });
userSchema.index({ isActive: 1 });
userSchema.index({ createdAt: -1 });

// Virtuals
userSchema.virtual('fullName').get(function() {
    if (this.profile.firstName && this.profile.lastName) {
        return `${this.profile.firstName} ${this.profile.lastName}`;
    }
    return this.username;
});

userSchema.virtual('completionRate').get(function() {
    if (this.academicProgress.totalCoursesEnrolled === 0) return 0;
    return Math.round((this.academicProgress.totalCoursesCompleted / this.academicProgress.totalCoursesEnrolled) * 100);
});

userSchema.virtual('nextLevel').get(function() {
    return this.gamification.level + 1;
});

userSchema.virtual('experienceToNextLevel').get(function() {
    const currentLevelExp = this.gamification.level * 1000;
    const nextLevelExp = (this.gamification.level + 1) * 1000;
    return nextLevelExp - this.gamification.experience;
});

// Middleware pre-save
userSchema.pre('save', async function(next) {
    // Hash password si fue modificado
    if (this.isModified('password')) {
        this.password = await bcrypt.hash(this.password, 12);
    }
    
    // Actualizar nivel basado en experiencia
    if (this.isModified('gamification.experience')) {
        const newLevel = Math.floor(this.gamification.experience / 1000) + 1;
        if (newLevel > this.gamification.level) {
            this.gamification.level = newLevel;
        }
    }
    
    next();
});

// Métodos de instancia
userSchema.methods.comparePassword = async function(candidatePassword) {
    return await bcrypt.compare(candidatePassword, this.password);
};

userSchema.methods.generateAuthToken = function() {
    return jwt.sign(
        { 
            id: this._id,
            username: this.username,
            role: this.role 
        },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRE || '7d' }
    );
};

userSchema.methods.generateEmailVerificationToken = function() {
    const token = crypto.randomBytes(32).toString('hex');
    this.emailVerificationToken = crypto.createHash('sha256').update(token).digest('hex');
    this.emailVerificationExpires = Date.now() + 24 * 60 * 60 * 1000; // 24 horas
    return token;
};

userSchema.methods.generatePasswordResetToken = function() {
    const token = crypto.randomBytes(32).toString('hex');
    this.passwordResetToken = crypto.createHash('sha256').update(token).digest('hex');
    this.passwordResetExpires = Date.now() + 10 * 60 * 1000; // 10 minutos
    return token;
};

userSchema.methods.addExperience = function(points) {
    this.gamification.experience += points;
    this.gamification.totalPoints += points;
    
    // Actualizar nivel
    const newLevel = Math.floor(this.gamification.experience / 1000) + 1;
    if (newLevel > this.gamification.level) {
        this.gamification.level = newLevel;
        return { levelUp: true, newLevel };
    }
    
    return { levelUp: false };
};

userSchema.methods.enrollInCourse = function(courseId) {
    const alreadyEnrolled = this.enrolledCourses.some(
        course => course.courseId.toString() === courseId.toString()
    );
    
    if (!alreadyEnrolled) {
        this.enrolledCourses.push({
            courseId,
            enrolledAt: new Date()
        });
        this.academicProgress.totalCoursesEnrolled += 1;
    }
};

userSchema.methods.updateCourseProgress = function(courseId, progress) {
    const courseIndex = this.enrolledCourses.findIndex(
        course => course.courseId.toString() === courseId.toString()
    );
    
    if (courseIndex !== -1) {
        this.enrolledCourses[courseIndex].progress = progress;
        this.enrolledCourses[courseIndex].lastAccessedAt = new Date();
        
        if (progress === 100 && !this.enrolledCourses[courseIndex].completedAt) {
            this.enrolledCourses[courseIndex].completedAt = new Date();
            this.academicProgress.totalCoursesCompleted += 1;
        }
    }
};

userSchema.methods.addActivity = function(type, description, courseId = null, lessonId = null) {
    this.recentActivity.unshift({
        type,
        description,
        courseId,
        lessonId,
        timestamp: new Date()
    });
    
    // Mantener solo las últimas 50 actividades
    if (this.recentActivity.length > 50) {
        this.recentActivity = this.recentActivity.slice(0, 50);
    }
    
    this.academicProgress.lastActivityDate = new Date();
};

// Métodos estáticos
userSchema.statics.findByEmail = function(email) {
    return this.findOne({ email: email.toLowerCase() });
};

userSchema.statics.findByUsername = function(username) {
    return this.findOne({ username: new RegExp(`^${username}$`, 'i') });
};

userSchema.statics.getActiveUsers = function() {
    return this.find({ isActive: true });
};

userSchema.statics.getUsersByRole = function(role) {
    return this.find({ role, isActive: true });
};

module.exports = mongoose.model('User', userSchema);
