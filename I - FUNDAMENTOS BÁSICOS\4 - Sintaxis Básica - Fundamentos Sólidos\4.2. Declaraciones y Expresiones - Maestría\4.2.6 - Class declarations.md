## 4.2.6. Class declarations

### Introducción
Las class declarations en JavaScript proporcionan sintaxis para OOP.  
Son hoisted pero no inicializadas.  
Usan constructor para inicialización.  
Métodos son no enumerables.  
Extienden herencia prototipal.  
¿Usas classes o funciones constructoras?

### Código de Ejemplo
```javascript
// Class declaration
class Persona {
    constructor(nombre) {
        this.nombre = nombre;
    }
    saludar() {
        console.log(`Hola, soy ${this.nombre}`);
    }
}
const persona = new Persona('<PERSON>');
persona.saludar();

// Herencia
class Estudiante extends Persona {
    constructor(nombre, curso) {
        super(nombre);
        this.curso = curso;
    }
    estudiar() {
        console.log(`Estudiando ${this.curso}`);
    }
}
const estudiante = new Estudiante('Ana', 'JavaScript');
estudiante.saludar();
estudiante.estudiar();

// Hoisting
// const prof = new Profesor(); // E<PERSON><PERSON>, TDZ
class Profesor extends Persona {}
```
### Resultados en Consola
- `Ho<PERSON>, soy Juan`
- `<PERSON><PERSON>, soy Ana`
- `Estudiando <PERSON>`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> ClassDecl["Paso 1: class Persona <br> - Declaration"]
    ClassDecl --> Constructor["Paso 2: constructor(nombre) <br> - Inicialización"]
    Constructor --> Method["Paso 3: saludar() <br> - Método"]
    Method --> Instancia["Paso 4: new Persona('Juan') <br> - Instancia"]
    Instancia --> CallMethod["Paso 5: persona.saludar() <br> - Resultado: Hola, soy Juan"]
    CallMethod --> Herencia["Paso 6: class Estudiante extends Persona <br> - Herencia"]
    Herencia --> Super["Paso 7: super(nombre) <br> - Llamada a padre"]
    Super --> NewMethod["Paso 8: estudiar() <br> - Nuevo método"]
    NewMethod --> Instancia2["Paso 9: new Estudiante('Ana', 'JavaScript')"]
    Instancia2 --> Calls["Paso 10: saludar() y estudiar() <br> - Resultados"]
    Calls --> Hoist["Paso 11: // new Profesor() <br> - Error TDZ"]
    Hoist --> Fin["Paso 12: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> OOP["OOP en JS"]
    ClassDecl --> NotaClass["Nota: Hoisted pero TDZ"]
    Herencia --> NotaExt["Nota: Prototipal"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ClassDecl fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Constructor fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Method fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Instancia fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CallMethod fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Herencia fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Super fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NewMethod fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Instancia2 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Calls fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Hoist fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style OOP fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaClass fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaExt fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Class["Class <br> - Declaration, hoisted"]
        Class --> Constructor["Constructor <br> - Inicialización"]
        Constructor --> Methods["Métodos <br> - No enumerables"]
        Methods --> Herencia["Extends <br> - Herencia prototipal"]
        Herencia --> Super["Super <br> - Llamada a padre"]
        Super --> Instancia["New <br> - Creación de instancia"]
        Instancia --> Uso["Uso: Llamadas a métodos"]
        Uso --> TDZ["TDZ <br> - No inicializada antes"]
        Inicio --> Desarrollador(("Desarrollador")) --> Eleccion["Elección OOP"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Class fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Constructor fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Methods fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Herencia fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Super fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Instancia fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Uso fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style TDZ fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Eleccion fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
```

### Casos de uso
1. Modelar entidades con classes.
2. Herencia para reutilización.
3. Encapsulación con métodos.
4. Polimorfismo en overrides.
5. Classes en frameworks.

### Errores comunes
1. Acceder class antes de declaración.
2. Olvidar new al instanciar.
3. No llamar super en constructor.
4. Confundir con funciones constructoras.
5. Ignorar TDZ en hoisting.

### Recomendaciones
1. Usa classes para OOP moderno.
2. Prefiere composición sobre herencia.
3. Entiende prototipos subyacentes.
4. Usa private fields si disponible.
5. Prueba herencia con cuidado.