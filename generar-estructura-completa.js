#!/usr/bin/env node

/**
 * GENERADOR AUTOMÁTICO DE ESTRUCTURA COMPLETA DEL CURSO
 * ====================================================
 * 
 * Este script genera toda la estructura restante del curso
 * incluyendo las Partes XI-XV con todos sus capítulos y temas
 */

const fs = require('fs');
const path = require('path');

// Definición completa de las partes restantes
const partesRestantes = {
  "PARTE XI - SEGURIDAD": {
    descripcion: "Implementa seguridad robusta y protege contra vulnerabilidades comunes en aplicaciones JavaScript.",
    capitulos: 5,
    tiempo: "35-50 horas",
    nivel: "⭐⭐⭐⭐⭐",
    capitulosDetalle: [
      {
        numero: 55,
        nombre: "Fundamentos de Seguridad Web",
        temas: ["HTTPS y TLS", "Same-Origin Policy", "CORS", "CSP", "Security Headers"]
      },
      {
        numero: 56,
        nombre: "Vulnerabilidades Comunes",
        temas: ["XSS", "CSRF", "SQL Injection", "Clickjacking", "OWASP Top 10"]
      },
      {
        numero: 57,
        nombre: "Autenticación y Autorización",
        temas: ["JWT", "OAuth 2.0", "Session Management", "2FA", "RBAC"]
      },
      {
        numero: 58,
        nombre: "Criptografía en JavaScript",
        temas: ["Web Crypto API", "Hashing", "Encryption", "Digital Signatures", "Key Management"]
      },
      {
        numero: 59,
        nombre: "Security Testing",
        temas: ["Penetration Testing", "Security Audits", "Vulnerability Scanning", "SAST/DAST", "Security Monitoring"]
      }
    ]
  },
  "PARTE XII - FRAMEWORKS Y LIBRERÍAS": {
    descripcion: "Domina los frameworks y librerías más importantes del ecosistema JavaScript moderno.",
    capitulos: 12,
    tiempo: "120-160 horas",
    nivel: "⭐⭐⭐⭐",
    capitulosDetalle: [
      {
        numero: 60,
        nombre: "React Fundamentals",
        temas: ["Components", "JSX", "Props y State", "Event Handling", "Lifecycle"]
      },
      {
        numero: 61,
        nombre: "React Avanzado",
        temas: ["Hooks", "Context API", "Performance", "Patterns", "Testing"]
      },
      {
        numero: 62,
        nombre: "Vue.js Fundamentals",
        temas: ["Templates", "Directives", "Components", "Reactivity", "Router"]
      },
      {
        numero: 63,
        nombre: "Vue.js Avanzado",
        temas: ["Composition API", "Vuex", "Nuxt.js", "Testing", "Performance"]
      },
      {
        numero: 64,
        nombre: "Angular Fundamentals",
        temas: ["Components", "Services", "Dependency Injection", "Router", "Forms"]
      },
      {
        numero: 65,
        nombre: "Angular Avanzado",
        temas: ["RxJS", "NgRx", "Testing", "Performance", "PWA"]
      },
      {
        numero: 66,
        nombre: "Node.js Fundamentals",
        temas: ["Modules", "File System", "HTTP", "Streams", "Events"]
      },
      {
        numero: 67,
        nombre: "Node.js Avanzado",
        temas: ["Express.js", "Database Integration", "Authentication", "Testing", "Deployment"]
      },
      {
        numero: 68,
        nombre: "State Management",
        temas: ["Redux", "MobX", "Zustand", "Recoil", "Patterns"]
      },
      {
        numero: 69,
        nombre: "UI Libraries",
        temas: ["Material-UI", "Ant Design", "Chakra UI", "Styled Components", "Tailwind CSS"]
      },
      {
        numero: 70,
        nombre: "Utility Libraries",
        temas: ["Lodash", "Moment.js", "Axios", "RxJS", "Ramda"]
      },
      {
        numero: 71,
        nombre: "Framework Comparison",
        temas: ["Performance", "Learning Curve", "Ecosystem", "Use Cases", "Migration"]
      }
    ]
  },
  "PARTE XIII - HERRAMIENTAS DE DESARROLLO": {
    descripcion: "Domina las herramientas profesionales para desarrollo, deployment y colaboración.",
    capitulos: 6,
    tiempo: "40-60 horas",
    nivel: "⭐⭐⭐⭐",
    capitulosDetalle: [
      {
        numero: 72,
        nombre: "Git y Control de Versiones",
        temas: ["Git Fundamentals", "Branching", "Merging", "Workflows", "GitHub/GitLab"]
      },
      {
        numero: 73,
        nombre: "CI/CD Pipelines",
        temas: ["GitHub Actions", "GitLab CI", "Jenkins", "Deployment", "Automation"]
      },
      {
        numero: 74,
        nombre: "Docker y Containerización",
        temas: ["Docker Basics", "Dockerfile", "Docker Compose", "Kubernetes", "Deployment"]
      },
      {
        numero: 75,
        nombre: "Cloud Platforms",
        temas: ["AWS", "Google Cloud", "Azure", "Vercel", "Netlify"]
      },
      {
        numero: 76,
        nombre: "Monitoring y Logging",
        temas: ["Application Monitoring", "Error Tracking", "Logging", "Alerting", "Dashboards"]
      },
      {
        numero: 77,
        nombre: "Development Workflow",
        temas: ["IDE Setup", "Debugging", "Linting", "Formatting", "Documentation"]
      }
    ]
  },
  "PARTE XIV - PROYECTOS PRÁCTICOS": {
    descripcion: "Construye aplicaciones reales del mundo profesional aplicando todo lo aprendido.",
    capitulos: 10,
    tiempo: "150-200 horas",
    nivel: "⭐⭐⭐⭐⭐",
    capitulosDetalle: [
      {
        numero: 78,
        nombre: "E-commerce Platform",
        temas: ["Product Catalog", "Shopping Cart", "Payment Integration", "User Management", "Admin Panel"]
      },
      {
        numero: 79,
        nombre: "Social Media App",
        temas: ["User Profiles", "Posts and Comments", "Real-time Chat", "Notifications", "Media Upload"]
      },
      {
        numero: 80,
        nombre: "Task Management System",
        temas: ["Project Management", "Team Collaboration", "Time Tracking", "Reporting", "Integrations"]
      },
      {
        numero: 81,
        nombre: "Real-time Dashboard",
        temas: ["Data Visualization", "Real-time Updates", "Analytics", "Filtering", "Export"]
      },
      {
        numero: 82,
        nombre: "Learning Management System",
        temas: ["Course Management", "Video Streaming", "Assessments", "Progress Tracking", "Certificates"]
      },
      {
        numero: 83,
        nombre: "Financial Trading App",
        temas: ["Market Data", "Trading Interface", "Portfolio Management", "Risk Analysis", "Reporting"]
      },
      {
        numero: 84,
        nombre: "IoT Dashboard",
        temas: ["Device Management", "Sensor Data", "Real-time Monitoring", "Alerts", "Analytics"]
      },
      {
        numero: 85,
        nombre: "Content Management System",
        temas: ["Content Creation", "Media Management", "SEO", "Multi-language", "Themes"]
      },
      {
        numero: 86,
        nombre: "Video Streaming Platform",
        temas: ["Video Upload", "Streaming", "Playlists", "Comments", "Recommendations"]
      },
      {
        numero: 87,
        nombre: "Microservices Architecture",
        temas: ["Service Design", "API Gateway", "Service Discovery", "Load Balancing", "Monitoring"]
      }
    ]
  },
  "PARTE XV - JAVASCRIPT AVANZADO": {
    descripcion: "Explora temas avanzados y experimentales para estar a la vanguardia del desarrollo JavaScript.",
    capitulos: 8,
    tiempo: "60-80 horas",
    nivel: "⭐⭐⭐⭐⭐",
    capitulosDetalle: [
      {
        numero: 88,
        nombre: "Metaprogramming",
        temas: ["Proxies", "Reflect", "Symbols", "Decorators", "Dynamic Code"]
      },
      {
        numero: 89,
        nombre: "WebAssembly Integration",
        temas: ["WASM Basics", "Rust Integration", "C++ Integration", "Performance", "Use Cases"]
      },
      {
        numero: 90,
        nombre: "Machine Learning en JavaScript",
        temas: ["TensorFlow.js", "Brain.js", "ML5.js", "Neural Networks", "Computer Vision"]
      },
      {
        numero: 91,
        nombre: "Blockchain y Web3",
        temas: ["Ethereum", "Smart Contracts", "Web3.js", "DApps", "NFTs"]
      },
      {
        numero: 92,
        nombre: "AR/VR con JavaScript",
        temas: ["WebXR", "A-Frame", "Three.js", "WebGL", "Immersive Experiences"]
      },
      {
        numero: 93,
        nombre: "Edge Computing",
        temas: ["Cloudflare Workers", "Deno Deploy", "Edge Functions", "CDN", "Performance"]
      },
      {
        numero: 94,
        nombre: "Quantum Computing",
        temas: ["Quantum Concepts", "Qiskit", "Quantum Algorithms", "Future Applications", "Research"]
      },
      {
        numero: 95,
        nombre: "Future of JavaScript",
        temas: ["TC39 Proposals", "Experimental Features", "Language Evolution", "Ecosystem Trends", "Career Paths"]
      }
    ]
  }
};

// Función para crear directorio
function crearDirectorio(ruta) {
  if (!fs.existsSync(ruta)) {
    fs.mkdirSync(ruta, { recursive: true });
    console.log(`✅ Creado: ${ruta}`);
  }
}

// Función para crear README de parte
function crearREADMEParte(nombreParte, datos) {
  const rutaArchivo = path.join(nombreParte, 'README.md');
  
  if (!fs.existsSync(rutaArchivo)) {
    const contenido = `# **${nombreParte.toUpperCase()}**

## **📚 Descripción de la Parte**

${datos.descripcion}

## **🎯 Objetivos de Aprendizaje**

Al completar esta parte, serás capaz de:

- [ ] [Objetivo específico 1]
- [ ] [Objetivo específico 2]
- [ ] [Objetivo específico 3]
- [ ] [Objetivo específico 4]
- [ ] [Objetivo específico 5]

## **📊 Estadísticas de la Parte**

- **Capítulos:** ${datos.capitulos}
- **Temas principales:** ${datos.capitulos * 5}
- **Subtemas:** ${datos.capitulos * 50}
- **Tiempo estimado:** ${datos.tiempo}
- **Nivel:** ${datos.nivel}
- **Proyectos prácticos:** ${datos.capitulos * 3}

## **📋 Índice de Capítulos**

${datos.capitulosDetalle.map(cap => `### **[Capítulo ${cap.numero} - ${cap.nombre}](${cap.numero}%20-%20${cap.nombre.replace(/\s+/g, '%20')}/README.md)** ${datos.nivel}
**Tiempo estimado:** 6-12 horas | **Temas:** 5

[Descripción del capítulo]

${cap.temas.map((tema, index) => `- [${cap.numero}.${index + 1}. ${tema}](${cap.numero}%20-%20${cap.nombre.replace(/\s+/g, '%20')}/${cap.numero}.${index + 1}.%20${tema.replace(/\s+/g, '%20')}/README.md)`).join('\n')}

---`).join('\n\n')}

## **🎯 Rutas de Aprendizaje de la Parte**

### **🚀 Ruta Rápida**
Enfoque en conceptos esenciales.

### **📚 Ruta Completa**
Cobertura completa de todos los temas.

### **🔬 Ruta Experto**
Para dominio completo y casos avanzados.

---

## **📊 Sistema de Progreso**

\`\`\`
${datos.capitulosDetalle.map(cap => `Capítulo ${cap.numero}: [░░░░░░░░░░] 0% completado`).join('\n')}

Progreso Total: [░░░░░░░░░░] 0% completado
\`\`\`

## **🏆 Logros de la Parte**

${datos.capitulosDetalle.map((cap, index) => `- 🎯 **${cap.nombre.split(' ')[0]} Expert**: Completar ${cap.nombre}`).join('\n')}
- 👑 **Master**: Completar todos los capítulos

---

## **➡️ Navegación**

⬅️ **Anterior:** [Parte anterior]  
➡️ **Siguiente:** [Parte siguiente]  
🏠 **Curso:** [Curso Completo de JavaScript](../README.md)

---

**¡Domina esta parte y avanza en tu maestría de JavaScript!** 🚀`;

    fs.writeFileSync(rutaArchivo, contenido);
    console.log(`📄 Creado README: ${rutaArchivo}`);
  }
}

// Función para crear README de capítulo
function crearREADMECapitulo(rutaCapitulo, numeroCapitulo, nombreCapitulo, temas) {
  const rutaArchivo = path.join(rutaCapitulo, 'README.md');
  
  if (!fs.existsSync(rutaArchivo)) {
    const contenido = `# **Capítulo ${numeroCapitulo} - ${nombreCapitulo}**

## **📖 Descripción del Capítulo**

[Descripción detallada del capítulo]

## **🎯 Objetivos de Aprendizaje**

Al completar este capítulo, serás capaz de:

${temas.map(tema => `- [ ] Dominar ${tema.toLowerCase()}`).join('\n')}

## **📊 Información del Capítulo**

- **Dificultad:** ⭐⭐⭐⭐ (Avanzado)
- **Tiempo estimado:** 8-12 horas
- **Temas:** ${temas.length}
- **Subtemas:** ${temas.length * 10}
- **Ejercicios prácticos:** ${temas.length * 5}

## **📋 Índice de Temas**

${temas.map((tema, index) => `### **[${numeroCapitulo}.${index + 1}. ${tema}](${numeroCapitulo}.${index + 1}.%20${tema.replace(/\s+/g, '%20')}/README.md)** ⭐⭐⭐
**Tiempo:** 1-2 horas | **Subtemas:** 10

[Descripción del tema]`).join('\n\n---\n\n')}

---

## **🎯 Rutas de Aprendizaje del Capítulo**

### **🚀 Ruta Rápida (4-6 horas)**
Conceptos esenciales del capítulo.

### **📚 Ruta Completa (8-12 horas)**
Cobertura completa de todos los temas.

### **🔬 Ruta Experto (12-16 horas)**
Dominio completo y casos avanzados.

---

## **📊 Progreso del Capítulo**

\`\`\`
${temas.map((tema, index) => `Tema ${numeroCapitulo}.${index + 1}: [░░░░░░░░░░] 0% completado`).join('\n')}

Progreso Total: [░░░░░░░░░░] 0% completado
\`\`\`

## **🏆 Logros del Capítulo**

${temas.map((tema, index) => `- 🎯 **${tema.split(' ')[0]}**: Completar tema ${numeroCapitulo}.${index + 1}`).join('\n')}
- 🚀 **Chapter Master**: Completar todos los temas

---

## **📝 Evaluación del Capítulo**

### **Quiz Final**
- ${temas.length * 5} preguntas sobre los temas
- Tiempo límite: ${Math.ceil(temas.length * 2.5)} minutos
- Puntuación mínima: 80%

### **Proyecto Práctico**
[Descripción del proyecto del capítulo]

---

## **➡️ Navegación**

⬅️ **Anterior:** [Capítulo anterior]  
➡️ **Siguiente:** [Capítulo siguiente]  
🏠 **Parte:** [Volver a la parte](../README.md)

---

**¡Domina este capítulo y avanza en tu expertise!** 🚀`;

    fs.writeFileSync(rutaArchivo, contenido);
    console.log(`📄 Creado README capítulo: ${rutaArchivo}`);
  }
}

// Función para crear estructura de tema
function crearEstructuraTema(rutaTema, numeroCapitulo, numeroTema, nombreTema) {
  // Crear README del tema
  const rutaREADME = path.join(rutaTema, 'README.md');
  
  if (!fs.existsSync(rutaREADME)) {
    const contenido = `# **${numeroCapitulo}.${numeroTema}. ${nombreTema}**

## **📖 Descripción del Tema**

[Descripción detallada del tema]

## **🎯 Objetivos de Aprendizaje**

Al completar este tema, serás capaz de:

- [ ] [Objetivo específico 1]
- [ ] [Objetivo específico 2]
- [ ] [Objetivo específico 3]

## **📊 Información del Tema**

- **Dificultad:** ⭐⭐⭐ (Intermedio)
- **Tiempo estimado:** 1-2 horas
- **Subtemas:** 10
- **Ejercicios prácticos:** 5

## **📋 Índice de Contenido**

### **📚 Contenido Teórico**
[Pendiente de completar]

### **💻 Ejemplos Prácticos**
[Pendiente de completar]

### **🧪 Evaluación**
[Pendiente de completar]

---

## **➡️ Navegación**

⬅️ **Anterior:** [Tema anterior]  
➡️ **Siguiente:** [Tema siguiente]  
🏠 **Capítulo:** [Volver al capítulo](../README.md)

---

**¡Continúa aprendiendo y dominando JavaScript!** 🚀`;

    fs.writeFileSync(rutaREADME, contenido);
    console.log(`📄 Creado README tema: ${rutaREADME}`);
  }

  // Crear carpetas de contenido
  const carpetas = ['contenido', 'ejemplos', 'recursos', 'evaluacion'];
  carpetas.forEach(carpeta => {
    const rutaCarpeta = path.join(rutaTema, carpeta);
    crearDirectorio(rutaCarpeta);
    
    // Crear archivos básicos en cada carpeta
    if (carpeta === 'ejemplos') {
      const ejemplos = ['ejemplo-basico.js', 'ejemplo-avanzado.js', 'ejercicios.js'];
      ejemplos.forEach(ejemplo => {
        const rutaEjemplo = path.join(rutaCarpeta, ejemplo);
        if (!fs.existsSync(rutaEjemplo)) {
          fs.writeFileSync(rutaEjemplo, `// ${ejemplo.toUpperCase()} - ${nombreTema}\n// Código de ejemplo aquí\nconsole.log('${nombreTema}');`);
        }
      });
    }
  });
}

// Función principal
function generarEstructuraCompleta() {
  console.log('🚀 Generando estructura completa del curso...\n');
  
  Object.entries(partesRestantes).forEach(([nombreParte, datos]) => {
    console.log(`📚 Procesando: ${nombreParte}`);
    
    // Crear directorio de la parte
    crearDirectorio(nombreParte);
    
    // Crear README de la parte
    crearREADMEParte(nombreParte, datos);
    
    // Procesar cada capítulo
    datos.capitulosDetalle.forEach(capitulo => {
      const nombreCapitulo = `${capitulo.numero} - ${capitulo.nombre}`;
      const rutaCapitulo = path.join(nombreParte, nombreCapitulo);
      
      console.log(`  📖 Procesando: ${nombreCapitulo}`);
      
      // Crear directorio del capítulo
      crearDirectorio(rutaCapitulo);
      
      // Crear README del capítulo
      crearREADMECapitulo(rutaCapitulo, capitulo.numero, capitulo.nombre, capitulo.temas);
      
      // Procesar cada tema
      capitulo.temas.forEach((tema, index) => {
        const numeroTema = index + 1;
        const nombreTema = `${capitulo.numero}.${numeroTema}. ${tema}`;
        const rutaTema = path.join(rutaCapitulo, nombreTema);
        
        console.log(`    📝 Procesando: ${nombreTema}`);
        
        // Crear directorio del tema
        crearDirectorio(rutaTema);
        
        // Crear estructura del tema
        crearEstructuraTema(rutaTema, capitulo.numero, numeroTema, tema);
      });
    });
    
    console.log(`✅ Completado: ${nombreParte}\n`);
  });
  
  console.log('🎉 ¡Estructura completa generada exitosamente!');
  
  // Estadísticas finales
  const totalCapitulos = Object.values(partesRestantes).reduce((acc, parte) => acc + parte.capitulos, 0);
  const totalTemas = Object.values(partesRestantes).reduce((acc, parte) => 
    acc + parte.capitulosDetalle.reduce((acc2, cap) => acc2 + cap.temas.length, 0), 0);
  
  console.log('\n📊 Estadísticas generadas:');
  console.log(`- Partes: ${Object.keys(partesRestantes).length}`);
  console.log(`- Capítulos: ${totalCapitulos}`);
  console.log(`- Temas: ${totalTemas}`);
  console.log(`- Subtemas estimados: ${totalTemas * 10}`);
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  generarEstructuraCompleta();
}

module.exports = { generarEstructuraCompleta, partesRestantes };
