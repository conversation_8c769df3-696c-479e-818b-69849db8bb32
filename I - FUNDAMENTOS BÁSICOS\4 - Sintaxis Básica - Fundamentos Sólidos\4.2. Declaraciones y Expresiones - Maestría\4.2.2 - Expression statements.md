## 4.2.2. Expression statements

### Introducción
Los expression statements en JavaScript permiten que expresiones se usen como statements.  
Esto es útil para llamadas a funciones o asignaciones independientes.  
Se terminan con semicolon o por ASI.  
Comprenderlos ayuda a escribir código conciso.  
Muchos statements son en realidad expresiones evaluadas.  
¿ Dónde usas expression statements en tu código?

### Código de Ejemplo
```javascript
// Expression statements
let x = 10;
x++;
console.log('Hola');
let y = x * 2;
[1, 2, 3].forEach(num => console.log(num));

// Más ejemplos
true ? console.log('Verdadero') : console.log('Falso');

console.log(y);
```
### Resultados en Consola
- `Hola`
- `1`
- `2`
- `3`
- `Verdadero`
- `22`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> Declaracion["Paso 1: let x = 10 <br> - Declaración"]
    Declaracion --> Incremento["Paso 2: x++ <br> - Expression statement"]
    Incremento --> Log1["Paso 3: console.log('Hola') <br> - Llamada como statement"]
    Log1 --> Asignacion["Paso 4: let y = x * 2 <br> - Asignación"]
    Asignacion --> ForEach["Paso 5: [1, 2, 3].forEach(...) <br> - Método como statement"]
    ForEach --> Ternario["Paso 6: true ? ... : ... <br> - Ternario como statement"]
    Ternario --> Log2["Paso 7: console.log(y) <br> - Resultado: 22"]
    Log2 --> Fin["Paso 8: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Expressions["Expressions"]
    Incremento --> NotaInc["Nota: Side effect"]
    ForEach --> NotaFor["Nota: Iteración"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Declaracion fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Incremento fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log1 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Asignacion fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ForEach fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Ternario fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log2 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Expressions fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaInc fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaFor fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Expression["Expression <br> - Produce valor"]
        Expression --> Statement["Statement <br> - Ejecuta acción"]
        Statement --> SideEffect["SideEffect <br> - Cambios de estado"]
        SideEffect --> Ejemplos["Ejemplos: ++, log, forEach"]
        Ejemplos --> Uso["Uso <br> - Código conciso"]
        Inicio --> Desarrollador(("Desarrollador")) --> Comprension["Comprensión"]
        Expression --> NotaExp["Nota: Evaluada"]
        Statement --> NotaStat["Nota: Terminada con ;"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Expression fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Statement fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style SideEffect fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Ejemplos fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Uso fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Comprension fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaExp fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaStat fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Llamadas a funciones sin asignar resultados.
2. Operaciones de incremento/decremento independientes.
3. Evaluaciones ternarias para control de flujo.
4. Métodos de array como statements.
5. Asignaciones en bucles o condicionales.

### Errores comunes
1. Olvidar semicolons después de expressions.
2. Confundir con declarations en contextos.
3. Ignorar side effects no deseados.
4. Usar expressions complejas sin paréntesis.
5. No manejar valores retornados innecesarios.

### Recomendaciones
1. Usa semicolons para claridad.
2. Prefiere expressions simples como statements.
3. Entiende ASI para evitar pitfalls.
4. Prueba side effects en isolation.
5. Sigue guías de estilo para consistencia.