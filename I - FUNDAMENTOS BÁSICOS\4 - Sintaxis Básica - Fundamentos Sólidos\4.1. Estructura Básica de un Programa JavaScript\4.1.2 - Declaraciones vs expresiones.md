## 4.1.2. <PERSON>larac<PERSON>s vs expresiones

### Introducción
Las declaraciones en JavaScript son instrucciones como let o if que realizan acciones.  
Las expresiones, como 2 + 3, producen un valor y se evalúan como resultados.  
Esto es esencial para principiantes, ya que distingue cómo funciona el código.  
Además, te ayuda a entender la lógica desde el inicio del aprendizaje técnico.  
Configurarlo te da claridad en la sintaxis desde los primeros pasos del desarrollo.  
¿ Cómo crees que una expresión difiere de una declaración en un programa?

### Código de Ejemplo
```javascript
// Declaración
let x = 10;
// Expresión
let y = (2 + 3) * x;
console.log("Resultado: ", y);
```
### Resultados en Consola
- `Resultado: 50`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> Declaracion1["Paso 1: Declaración <br> - Asignar: let x = 10"]
    Declaracion1 --> Almacenar1["Paso 1.1: Almacenar <br> - Variable: x = 10"]
    Almacenar1 --> Expresion["Paso 2: Expresión <br> - Calcular: (2 + 3) * x = 50"]
    Expresion --> Evaluar["Paso 2.1: Evaluar <br> - Resultado: 50"]
    Evaluar --> Declaracion2["Paso 3: Declaración <br> - Asignar: let y = 50"]
    Declaracion2 --> Almacenar2["Paso 3.1: Almacenar <br> - Variable: y = 50"]
    Almacenar2 --> Log["Paso 4: console.log(y) <br> - Resultado: Resultado: 50"]
    Log --> Fin["Paso 5: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Diferencia["Diferencia"]
    Declaracion1 --> NotaInstruccion["Nota: Define variable"]
    Expresion --> NotaValor["Nota: Produce valor"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Declaracion1 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Almacenar1 fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:12px
    style Expresion fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Evaluar fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:12px
    style Declaracion2 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Almacenar2 fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:12px
    style Log fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Diferencia fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaInstruccion fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaValor fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Identificar["Identificar tipo <br> - Declaración vs Expresión"]
        Identificar --> Evaluar["Evaluar <br> - Calcular: (2 + 3) * 10 = 50"]
        Evaluar --> Ejecutar["Ejecutar <br> - Mostrar: Resultado 50"]
        Ejecutar --> Resultado["Resultado <br> - Almacenar: y = 50"]
        Resultado --> Aplicar["Aplicar <br> - Usar en lógica"]
        Inicio --> Desarrollador(("Desarrollador")) --> Comprension["Comprensión"]
        Identificar --> Tipo["Tipo <br> - Instrucción o valor"]
        Tipo --> NotaDiferencia["Nota: Distinguir roles"]
        Evaluar --> NotaCalculo["Nota: Calcular valor"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Identificar fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Evaluar fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:12px
    style Ejecutar fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Resultado fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Aplicar fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Comprension fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Tipo fill:#9ACD32,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaDiferencia fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaCalculo fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Uso de declaraciones para definir variables en un programa de cálculo básico.
2. Evaluación de expresiones matemáticas para realizar operaciones simples y complejas.
3. Combinación de declaraciones e expresiones en funciones para procesar datos.
4. Análisis de código para distinguir entre instrucciones y valores calculados.
5. Aplicación de expresiones en condicionales para tomar decisiones en el programa.

### Errores comunes
1. Confundir una declaración con una expresión al escribir código sin cuidado.
2. Usar expresiones donde se requiere una declaración, causando errores de sintaxis.
3. Ignorar el valor retornado por una expresión en asignaciones importantes.
4. No entender la diferencia entre ambos conceptos al depurar el código escrito.
5. Mezclar declaraciones y expresiones sin un propósito claro en el diseño.

### Recomendaciones
1. Aprende a identificar claramente las declaraciones y expresiones en tu código.
2. Usa declaraciones para definir estructuras y expresiones para cálculos siempre.
3. Verifica el valor de las expresiones al asignarlas a variables importantes.
4. Practica la depuración para comprender mejor las diferencias entre ambos.
5. Mantén un diseño organizado separando declaraciones de expresiones lógicas.