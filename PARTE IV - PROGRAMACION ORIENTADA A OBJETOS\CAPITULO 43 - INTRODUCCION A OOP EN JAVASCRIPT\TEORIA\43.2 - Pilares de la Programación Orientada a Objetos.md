# **43.2 - PILARES DE LA PROGRAMACIÓN ORIENTADA A OBJETOS**

## **📖 INTRODUCCIÓN**

Los pilares fundamentales de la Programación Orientada a Objetos constituyen los principios arquitectónicos que definen la esencia y el poder de este paradigma, proporcionando un marco conceptual robusto que permite crear software mantenible, escalable y reutilizable a través de la aplicación sistemática de encapsulación, herencia, polimorfismo y abstracción. Estos cuatro pilares no son meramente conceptos teóricos, sino herramientas prácticas que, cuando se comprenden profundamente y se aplican correctamente en JavaScript, transforman código caótico y difícil de mantener en sistemas elegantes y bien estructurados que pueden evolucionar con los requisitos cambiantes del negocio. En el contexto específico de JavaScript, estos pilares adquieren matices únicos debido a la naturaleza dinámica del lenguaje, donde la flexibilidad inherente permite implementaciones creativas que van más allá de las restricciones encontradas en lenguajes más rígidos, pero que también requieren una disciplina mayor para mantener la coherencia arquitectónica y evitar los antipatrones que pueden surgir del uso inadecuado de esta flexibilidad.

## **🔍 CONCEPTOS FUNDAMENTALES**

### **43.2.1. Encapsulación: concepto y aplicación**

![Encapsulación](../VISUALIZACIONES/anatomia/encapsulation.svg)

La encapsulación es el principio que agrupa datos y métodos relacionados dentro de una unidad cohesiva (objeto) y controla el acceso a los detalles internos.

```javascript
/**
 * ENCAPSULACIÓN EN JAVASCRIPT
 * 
 * Demostración de diferentes niveles de encapsulación
 * desde básica hasta avanzada con campos privados.
 */

// ===== ENCAPSULACIÓN BÁSICA CON CONVENCIONES =====
class CuentaBancaria {
    constructor(numeroCuenta, saldoInicial = 0) {
        this.numeroCuenta = numeroCuenta;
        this._saldo = saldoInicial; // Convención: _ indica "privado"
        this._transacciones = [];
        this._fechaCreacion = new Date();
    }
    
    // Método público para acceder al saldo
    obtenerSaldo() {
        return this._saldo;
    }
    
    // Método público con validación
    depositar(cantidad) {
        if (cantidad <= 0) {
            throw new Error('La cantidad debe ser positiva');
        }
        
        this._saldo += cantidad;
        this._registrarTransaccion('depósito', cantidad);
        
        console.log(`Depósito de $${cantidad}. Nuevo saldo: $${this._saldo}`);
        return this._saldo;
    }
    
    retirar(cantidad) {
        if (cantidad <= 0) {
            throw new Error('La cantidad debe ser positiva');
        }
        
        if (cantidad > this._saldo) {
            throw new Error('Saldo insuficiente');
        }
        
        this._saldo -= cantidad;
        this._registrarTransaccion('retiro', cantidad);
        
        console.log(`Retiro de $${cantidad}. Nuevo saldo: $${this._saldo}`);
        return this._saldo;
    }
    
    // Método "privado" por convención
    _registrarTransaccion(tipo, cantidad) {
        this._transacciones.push({
            tipo,
            cantidad,
            fecha: new Date(),
            saldoResultante: this._saldo
        });
    }
    
    // Método público que expone información controlada
    obtenerHistorial() {
        return this._transacciones.map(t => ({
            tipo: t.tipo,
            cantidad: t.cantidad,
            fecha: t.fecha.toISOString()
        }));
    }
}

// ===== ENCAPSULACIÓN AVANZADA CON CAMPOS PRIVADOS =====
class CuentaBancariaSegura {
    // Campos privados (ES2022)
    #saldo;
    #transacciones;
    #pin;
    #intentosFallidos;
    
    constructor(numeroCuenta, saldoInicial = 0, pin) {
        this.numeroCuenta = numeroCuenta; // Público
        this.#saldo = saldoInicial;       // Privado
        this.#transacciones = [];         // Privado
        this.#pin = pin;                  // Privado
        this.#intentosFallidos = 0;       // Privado
        this.fechaCreacion = new Date();  // Público
    }
    
    // Método privado para validar PIN
    #validarPin(pin) {
        if (pin !== this.#pin) {
            this.#intentosFallidos++;
            
            if (this.#intentosFallidos >= 3) {
                throw new Error('Cuenta bloqueada por intentos fallidos');
            }
            
            throw new Error('PIN incorrecto');
        }
        
        this.#intentosFallidos = 0; // Reset en caso de éxito
    }
    
    // Getter para saldo (requiere PIN)
    obtenerSaldo(pin) {
        this.#validarPin(pin);
        return this.#saldo;
    }
    
    // Método público que usa métodos privados
    realizarTransaccion(tipo, cantidad, pin) {
        this.#validarPin(pin);
        
        if (tipo === 'depósito') {
            return this.#depositar(cantidad);
        } else if (tipo === 'retiro') {
            return this.#retirar(cantidad);
        } else {
            throw new Error('Tipo de transacción no válido');
        }
    }
    
    // Métodos privados para operaciones
    #depositar(cantidad) {
        if (cantidad <= 0) {
            throw new Error('La cantidad debe ser positiva');
        }
        
        this.#saldo += cantidad;
        this.#registrarTransaccion('depósito', cantidad);
        
        return this.#saldo;
    }
    
    #retirar(cantidad) {
        if (cantidad <= 0) {
            throw new Error('La cantidad debe ser positiva');
        }
        
        if (cantidad > this.#saldo) {
            throw new Error('Saldo insuficiente');
        }
        
        this.#saldo -= cantidad;
        this.#registrarTransaccion('retiro', cantidad);
        
        return this.#saldo;
    }
    
    #registrarTransaccion(tipo, cantidad) {
        this.#transacciones.push({
            tipo,
            cantidad,
            fecha: new Date(),
            saldoResultante: this.#saldo
        });
    }
    
    // Método público que expone datos de forma controlada
    obtenerResumen(pin) {
        this.#validarPin(pin);
        
        return {
            numeroCuenta: this.numeroCuenta,
            saldo: this.#saldo,
            totalTransacciones: this.#transacciones.length,
            fechaCreacion: this.fechaCreacion,
            ultimaTransaccion: this.#transacciones[this.#transacciones.length - 1]?.fecha
        };
    }
}
```

### **43.2.2. Herencia: concepto y aplicación**

```javascript
/**
 * HERENCIA EN JAVASCRIPT
 * 
 * Demostración de herencia usando clases ES6 y
 * cómo permite reutilizar y extender funcionalidad.
 */

// ===== CLASE BASE =====
class Vehiculo {
    constructor(marca, modelo, año) {
        this.marca = marca;
        this.modelo = modelo;
        this.año = año;
        this.velocidad = 0;
        this.encendido = false;
    }
    
    // Métodos comunes a todos los vehículos
    encender() {
        this.encendido = true;
        console.log(`${this.marca} ${this.modelo} encendido`);
    }
    
    apagar() {
        if (this.velocidad > 0) {
            throw new Error('No se puede apagar un vehículo en movimiento');
        }
        this.encendido = false;
        console.log(`${this.marca} ${this.modelo} apagado`);
    }
    
    // Método que puede ser sobrescrito
    acelerar(incremento = 10) {
        if (!this.encendido) {
            throw new Error('El vehículo debe estar encendido');
        }
        
        this.velocidad += incremento;
        console.log(`Acelerando a ${this.velocidad} km/h`);
    }
    
    frenar(decremento = 10) {
        this.velocidad = Math.max(0, this.velocidad - decremento);
        console.log(`Frenando a ${this.velocidad} km/h`);
    }
    
    obtenerInfo() {
        return `${this.marca} ${this.modelo} (${this.año}) - ${this.velocidad} km/h`;
    }
}

// ===== HERENCIA: CLASE DERIVADA =====
class Automovil extends Vehiculo {
    constructor(marca, modelo, año, puertas, tipoTransmision) {
        // Llamar al constructor de la clase padre
        super(marca, modelo, año);
        
        // Propiedades específicas del automóvil
        this.puertas = puertas;
        this.tipoTransmision = tipoTransmision;
        this.velocidadMaxima = 180;
        this.aireAcondicionado = false;
    }
    
    // Sobrescribir método de la clase padre
    acelerar(incremento = 15) {
        if (!this.encendido) {
            throw new Error('El automóvil debe estar encendido');
        }
        
        // Límite específico para automóviles
        const nuevaVelocidad = this.velocidad + incremento;
        this.velocidad = Math.min(nuevaVelocidad, this.velocidadMaxima);
        
        console.log(`Automóvil acelerando a ${this.velocidad} km/h`);
        
        // Funcionalidad adicional
        if (this.velocidad > 60 && !this.aireAcondicionado) {
            this.activarAireAcondicionado();
        }
    }
    
    // Métodos específicos del automóvil
    activarAireAcondicionado() {
        this.aireAcondicionado = true;
        console.log('Aire acondicionado activado');
    }
    
    cambiarMarcha(marcha) {
        if (this.tipoTransmision === 'manual') {
            console.log(`Cambiando a marcha ${marcha}`);
        } else {
            console.log('Transmisión automática - cambio automático');
        }
    }
    
    // Extender método de la clase padre
    obtenerInfo() {
        const infoBase = super.obtenerInfo();
        return `${infoBase} | ${this.puertas} puertas | ${this.tipoTransmision}`;
    }
}

// ===== HERENCIA MÚLTIPLE SIMULADA CON MIXINS =====
const CapacidadVuelo = {
    despegar() {
        if (this.velocidad < this.velocidadDespegue) {
            throw new Error('Velocidad insuficiente para despegar');
        }
        this.volando = true;
        this.altitud = 100;
        console.log('Despegando...');
    },
    
    aterrizar() {
        this.volando = false;
        this.altitud = 0;
        this.velocidad = 0;
        console.log('Aterrizando...');
    },
    
    cambiarAltitud(nuevaAltitud) {
        if (!this.volando) {
            throw new Error('Debe estar volando para cambiar altitud');
        }
        this.altitud = nuevaAltitud;
        console.log(`Altitud cambiada a ${nuevaAltitud} metros`);
    }
};

class Avion extends Vehiculo {
    constructor(marca, modelo, año, capacidadPasajeros) {
        super(marca, modelo, año);
        this.capacidadPasajeros = capacidadPasajeros;
        this.velocidadDespegue = 250;
        this.velocidadMaxima = 900;
        this.volando = false;
        this.altitud = 0;
    }
    
    acelerar(incremento = 50) {
        if (!this.encendido) {
            throw new Error('El avión debe estar encendido');
        }
        
        const nuevaVelocidad = this.velocidad + incremento;
        this.velocidad = Math.min(nuevaVelocidad, this.velocidadMaxima);
        
        console.log(`Avión acelerando a ${this.velocidad} km/h`);
    }
}

// Aplicar mixin a la clase Avion
Object.assign(Avion.prototype, CapacidadVuelo);
```

### **43.2.3. Polimorfismo: concepto y aplicación**

```javascript
/**
 * POLIMORFISMO EN JAVASCRIPT
 * 
 * Capacidad de objetos de diferentes tipos de responder
 * al mismo mensaje de manera específica.
 */

// ===== POLIMORFISMO CLÁSICO =====
class Forma {
    calcularArea() {
        throw new Error('Método debe ser implementado por subclases');
    }
    
    calcularPerimetro() {
        throw new Error('Método debe ser implementado por subclases');
    }
    
    describir() {
        return `Forma con área ${this.calcularArea()} y perímetro ${this.calcularPerimetro()}`;
    }
}

class Rectangulo extends Forma {
    constructor(ancho, alto) {
        super();
        this.ancho = ancho;
        this.alto = alto;
    }
    
    calcularArea() {
        return this.ancho * this.alto;
    }
    
    calcularPerimetro() {
        return 2 * (this.ancho + this.alto);
    }
}

class Circulo extends Forma {
    constructor(radio) {
        super();
        this.radio = radio;
    }
    
    calcularArea() {
        return Math.PI * this.radio * this.radio;
    }
    
    calcularPerimetro() {
        return 2 * Math.PI * this.radio;
    }
}

// ===== POLIMORFISMO EN ACCIÓN =====
function procesarFormas(formas) {
    let areaTotal = 0;
    let perimetroTotal = 0;
    
    formas.forEach(forma => {
        // Polimorfismo: mismo método, comportamiento diferente
        areaTotal += forma.calcularArea();
        perimetroTotal += forma.calcularPerimetro();
        
        console.log(forma.describir());
    });
    
    return { areaTotal, perimetroTotal };
}

// ===== DUCK TYPING (POLIMORFISMO ESTRUCTURAL) =====
// En JavaScript, si "camina como pato y hace cuac como pato, es un pato"

class Pato {
    hacerSonido() {
        return 'Cuac cuac';
    }
    
    nadar() {
        return 'El pato está nadando';
    }
}

class Persona {
    hacerSonido() {
        return 'Hola mundo';
    }
    
    nadar() {
        return 'La persona está nadando';
    }
}

class Robot {
    hacerSonido() {
        return 'Beep beep';
    }
    
    nadar() {
        return 'El robot está simulando nadar';
    }
}

// Función que acepta cualquier objeto con los métodos requeridos
function activarEnPiscina(entidades) {
    entidades.forEach(entidad => {
        console.log(entidad.hacerSonido());
        console.log(entidad.nadar());
    });
}
```

### **43.2.4. Abstracción: concepto y aplicación**

```javascript
/**
 * ABSTRACCIÓN EN JAVASCRIPT
 * 
 * Ocultar complejidad innecesaria y exponer solo
 * las operaciones esenciales.
 */

// ===== ABSTRACCIÓN DE COMPLEJIDAD =====
class ReproductorMusica {
    constructor() {
        // Detalles internos complejos ocultos
        this._codec = new AudioCodec();
        this._buffer = new AudioBuffer();
        this._ecualizador = new Ecualizador();
        this._volumen = 50;
        this._cancionActual = null;
        this._estado = 'detenido';
    }
    
    // Interfaz simple para el usuario
    reproducir(cancion) {
        this._cancionActual = cancion;
        this._estado = 'reproduciendo';
        
        // Complejidad interna oculta
        this._codec.decodificar(cancion);
        this._buffer.cargar(cancion.datos);
        this._ecualizador.aplicar();
        
        console.log(`Reproduciendo: ${cancion.titulo}`);
    }
    
    pausar() {
        this._estado = 'pausado';
        this._buffer.pausar();
        console.log('Música pausada');
    }
    
    detener() {
        this._estado = 'detenido';
        this._buffer.limpiar();
        this._cancionActual = null;
        console.log('Música detenida');
    }
    
    ajustarVolumen(nivel) {
        this._volumen = Math.max(0, Math.min(100, nivel));
        this._ecualizador.ajustarVolumen(this._volumen);
        console.log(`Volumen ajustado a ${this._volumen}%`);
    }
    
    // Interfaz simple que oculta complejidad
    obtenerEstado() {
        return {
            estado: this._estado,
            cancion: this._cancionActual?.titulo || 'Ninguna',
            volumen: this._volumen
        };
    }
}

// Clases auxiliares (complejidad oculta)
class AudioCodec {
    decodificar(cancion) {
        // Lógica compleja de decodificación
        console.log(`Decodificando ${cancion.formato}...`);
    }
}

class AudioBuffer {
    cargar(datos) {
        console.log('Cargando datos en buffer...');
    }
    
    pausar() {
        console.log('Buffer pausado');
    }
    
    limpiar() {
        console.log('Buffer limpiado');
    }
}

class Ecualizador {
    aplicar() {
        console.log('Aplicando ecualización...');
    }
    
    ajustarVolumen(nivel) {
        console.log(`Ajustando volumen interno a ${nivel}`);
    }
}
```

### **💻 EMULACIÓN DE CONSOLA**

```bash
# Demostración de los cuatro pilares

# === ENCAPSULACIÓN ===
const cuenta = new CuentaBancariaSegura('12345', 1000, '1234');
console.log(cuenta.obtenerSaldo('1234')); // 1000
cuenta.realizarTransaccion('depósito', 500, '1234');
console.log(cuenta.obtenerResumen('1234'));
{
  numeroCuenta: '12345',
  saldo: 1500,
  totalTransacciones: 1,
  fechaCreacion: 2024-01-15T10:30:45.123Z,
  ultimaTransaccion: 2024-01-15T10:30:46.456Z
}

# === HERENCIA ===
const auto = new Automovil('Toyota', 'Corolla', 2023, 4, 'automática');
auto.encender();
auto.acelerar(70);
console.log(auto.obtenerInfo());

Toyota Corolla encendido
Automóvil acelerando a 70 km/h
Aire acondicionado activado
Toyota Corolla (2023) - 70 km/h | 4 puertas | automática

# === POLIMORFISMO ===
const formas = [
    new Rectangulo(5, 3),
    new Circulo(4),
    new Rectangulo(2, 8)
];

const resultado = procesarFormas(formas);
console.log(resultado);

Forma con área 15 y perímetro 16
Forma con área 50.26548245743669 y perímetro 25.132741228718345
Forma con área 16 y perímetro 20
{ areaTotal: 81.26548245743669, perimetroTotal: 61.132741228718345 }

# === ABSTRACCIÓN ===
const reproductor = new ReproductorMusica();
const cancion = { titulo: 'Bohemian Rhapsody', formato: 'mp3', datos: '...' };

reproductor.reproducir(cancion);
reproductor.ajustarVolumen(75);
console.log(reproductor.obtenerEstado());

Decodificando mp3...
Cargando datos en buffer...
Aplicando ecualización...
Reproduciendo: Bohemian Rhapsody
Ajustando volumen interno a 75
Volumen ajustado a 75%
{
  estado: 'reproduciendo',
  cancion: 'Bohemian Rhapsody',
  volumen: 75
}
```

## **🎯 CASOS DE USO PRÁCTICOS**

### **Caso 1: Sistema de Gestión de Empleados**
- **Encapsulación:** Datos personales protegidos
- **Herencia:** Diferentes tipos de empleados
- **Polimorfismo:** Cálculo de salarios específico por tipo
- **Abstracción:** Interfaz simple para RRHH

### **Caso 2: Framework de UI**
- **Encapsulación:** Estado interno de componentes
- **Herencia:** Componentes base y especializados
- **Polimorfismo:** Renderizado específico por tipo
- **Abstracción:** API simple para desarrolladores

### **Caso 3: Sistema de Pagos**
- **Encapsulación:** Datos sensibles protegidos
- **Herencia:** Diferentes métodos de pago
- **Polimorfismo:** Procesamiento específico por método
- **Abstracción:** Interfaz unificada de pago

## **💡 MEJORES PRÁCTICAS**

### **1. Encapsulación Efectiva**
- Usa campos privados para datos sensibles
- Proporciona interfaces públicas claras
- Valida datos en métodos públicos

### **2. Herencia Responsable**
- Prefiere composición sobre herencia profunda
- Usa herencia para relaciones "es-un"
- Evita jerarquías complejas

### **3. Polimorfismo Útil**
- Implementa interfaces consistentes
- Aprovecha duck typing de JavaScript
- Usa polimorfismo para extensibilidad

### **4. Abstracción Clara**
- Oculta complejidad innecesaria
- Expone solo operaciones esenciales
- Mantén interfaces simples e intuitivas

## **📚 RECURSOS ADICIONALES**

- **Implementación Completa:** Ver `CODIGO/pilares/OOPPillars.js`
- **Ejemplos Prácticos:** Ver `EJEMPLOS/intermedios/oop-pillars.js`
- **Tests:** Ver `TESTS/unit/oop-pillars.test.js`
- **Visualizaciones:** Ver `VISUALIZACIONES/anatomia/oop-pillars.svg`

---

**Continúa con:** `43.3 - Objetos en JavaScript.md`
