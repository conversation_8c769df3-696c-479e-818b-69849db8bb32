/**
 * CODECRAFT ACADEMY - SERVIDOR PRINCIPAL
 * =====================================
 * 
 * Servidor backend completo para la plataforma de aprendizaje de programación
 * Soporta múltiples lenguajes, cursos, usuarios, progreso y certificaciones
 */

const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss-clean');
const hpp = require('hpp');
const morgan = require('morgan');
const { createServer } = require('http');
const { Server } = require('socket.io');
const Redis = require('redis');

// Importar configuración
require('dotenv').config();
const config = require('./config/config');
const logger = require('./utils/logger');

// Importar middlewares
const errorHandler = require('./middleware/errorHandler');
const notFound = require('./middleware/notFound');

// Importar rutas
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const courseRoutes = require('./routes/courses');
const progressRoutes = require('./routes/progress');
const certificateRoutes = require('./routes/certificates');
const forumRoutes = require('./routes/forum');
const adminRoutes = require('./routes/admin');
const analyticsRoutes = require('./routes/analytics');
const paymentRoutes = require('./routes/payments');

// Crear aplicación Express
const app = express();
const server = createServer(app);

// Configurar Socket.IO para chat en tiempo real
const io = new Server(server, {
    cors: {
        origin: process.env.CLIENT_URL || "http://localhost:3000",
        methods: ["GET", "POST"]
    }
});

// Configurar Redis para cache y sesiones
let redisClient;
if (process.env.REDIS_URL) {
    redisClient = Redis.createClient({
        url: process.env.REDIS_URL
    });
    
    redisClient.on('error', (err) => {
        logger.error('Redis Client Error:', err);
    });
    
    redisClient.connect();
}

// Conectar a MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/codecraft-academy', {
    useNewUrlParser: true,
    useUnifiedTopology: true,
})
.then(() => {
    logger.info('✅ Conectado a MongoDB');
})
.catch((error) => {
    logger.error('❌ Error conectando a MongoDB:', error);
    process.exit(1);
});

// Middlewares de seguridad
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            imgSrc: ["'self'", "data:", "https://res.cloudinary.com"],
            scriptSrc: ["'self'"],
            connectSrc: ["'self'", "wss:", "ws:"]
        }
    }
}));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutos
    max: 100, // máximo 100 requests por ventana
    message: {
        error: 'Demasiadas solicitudes desde esta IP, intenta de nuevo en 15 minutos.'
    },
    standardHeaders: true,
    legacyHeaders: false,
});

app.use('/api/', limiter);

// Rate limiting más estricto para autenticación
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 5, // máximo 5 intentos de login por 15 minutos
    message: {
        error: 'Demasiados intentos de login, intenta de nuevo en 15 minutos.'
    }
});

app.use('/api/auth/login', authLimiter);
app.use('/api/auth/register', authLimiter);

// Middlewares generales
app.use(compression());
app.use(cors({
    origin: process.env.CLIENT_URL || "http://localhost:3000",
    credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Sanitización de datos
app.use(mongoSanitize());
app.use(xss());
app.use(hpp());

// Logging
if (process.env.NODE_ENV === 'development') {
    app.use(morgan('dev'));
} else {
    app.use(morgan('combined'));
}

// Middleware para añadir Redis y Socket.IO al request
app.use((req, res, next) => {
    req.redis = redisClient;
    req.io = io;
    next();
});

// Rutas de la API
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/courses', courseRoutes);
app.use('/api/progress', progressRoutes);
app.use('/api/certificates', certificateRoutes);
app.use('/api/forum', forumRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/payments', paymentRoutes);

// Ruta de salud del servidor
app.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV,
        version: process.env.npm_package_version || '1.0.0',
        services: {
            mongodb: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
            redis: redisClient ? 'connected' : 'not configured'
        }
    });
});

// Ruta de información de la API
app.get('/api', (req, res) => {
    res.json({
        name: 'CodeCraft Academy API',
        version: '1.0.0',
        description: 'API completa para plataforma de aprendizaje de programación',
        endpoints: {
            auth: '/api/auth',
            users: '/api/users',
            courses: '/api/courses',
            progress: '/api/progress',
            certificates: '/api/certificates',
            forum: '/api/forum',
            admin: '/api/admin',
            analytics: '/api/analytics',
            payments: '/api/payments'
        },
        documentation: '/api/docs',
        health: '/api/health'
    });
});

// Servir archivos estáticos en producción
if (process.env.NODE_ENV === 'production') {
    app.use(express.static('public'));
    
    app.get('*', (req, res) => {
        res.sendFile(path.join(__dirname, 'public', 'index.html'));
    });
}

// Manejo de rutas no encontradas
app.use(notFound);

// Manejo global de errores
app.use(errorHandler);

// Configuración de Socket.IO para chat y notificaciones en tiempo real
io.on('connection', (socket) => {
    logger.info(`Usuario conectado: ${socket.id}`);
    
    // Unirse a sala de curso
    socket.on('join-course', (courseId) => {
        socket.join(`course-${courseId}`);
        logger.info(`Usuario ${socket.id} se unió al curso ${courseId}`);
    });
    
    // Unirse a sala de foro
    socket.on('join-forum', (forumId) => {
        socket.join(`forum-${forumId}`);
        logger.info(`Usuario ${socket.id} se unió al foro ${forumId}`);
    });
    
    // Mensaje de chat
    socket.on('chat-message', (data) => {
        socket.to(`course-${data.courseId}`).emit('chat-message', {
            id: Date.now(),
            userId: data.userId,
            username: data.username,
            message: data.message,
            timestamp: new Date()
        });
    });
    
    // Progreso en tiempo real
    socket.on('progress-update', (data) => {
        socket.to(`course-${data.courseId}`).emit('user-progress', {
            userId: data.userId,
            progress: data.progress,
            timestamp: new Date()
        });
    });
    
    // Desconexión
    socket.on('disconnect', () => {
        logger.info(`Usuario desconectado: ${socket.id}`);
    });
});

// Manejo de errores no capturados
process.on('uncaughtException', (err) => {
    logger.error('Uncaught Exception:', err);
    process.exit(1);
});

process.on('unhandledRejection', (err) => {
    logger.error('Unhandled Rejection:', err);
    server.close(() => {
        process.exit(1);
    });
});

// Graceful shutdown
process.on('SIGTERM', () => {
    logger.info('SIGTERM recibido, cerrando servidor...');
    server.close(() => {
        mongoose.connection.close();
        if (redisClient) {
            redisClient.quit();
        }
        logger.info('Servidor cerrado correctamente');
        process.exit(0);
    });
});

// Iniciar servidor
const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
    logger.info(`🚀 Servidor ejecutándose en puerto ${PORT}`);
    logger.info(`🌍 Entorno: ${process.env.NODE_ENV || 'development'}`);
    logger.info(`📚 CodeCraft Academy Backend v1.0.0`);
    
    // Mostrar rutas disponibles en desarrollo
    if (process.env.NODE_ENV === 'development') {
        logger.info('📋 Rutas disponibles:');
        logger.info('   GET  /api - Información de la API');
        logger.info('   GET  /api/health - Estado del servidor');
        logger.info('   POST /api/auth/register - Registro de usuarios');
        logger.info('   POST /api/auth/login - Login de usuarios');
        logger.info('   GET  /api/courses - Lista de cursos');
        logger.info('   GET  /api/users/profile - Perfil de usuario');
        logger.info('   POST /api/progress - Actualizar progreso');
        logger.info('   GET  /api/certificates - Certificados del usuario');
    }
});

module.exports = { app, server, io };
