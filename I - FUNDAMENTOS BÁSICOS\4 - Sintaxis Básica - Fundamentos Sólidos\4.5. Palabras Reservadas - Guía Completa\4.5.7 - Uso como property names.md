# 4.5.7 - Uso como property names

## Introducción
En JavaScript, las palabras reservadas pueden usarse como nombres de propiedades en objetos si se encierran en comillas, permitiendo flexibilidad en la definición de keys como 'class' o 'function' sin causar errores de sintaxis, lo que es útil en escenarios como parsing de JSON o integración con APIs que usan tales nombres, aunque requiere acceso mediante bracket notation en lugar de dot notation para evitar conflictos con la sintaxis del lenguaje. Esta capacidad facilita el manejo de datos dinámicos donde keys podrían coincidir con reserved words, pero demanda precauciones para mantener la legibilidad y prevenir issues en entornos strict mode o al serializar objetos, promoviendo prácticas que equilibren conveniencia con claridad en código que interactúa con estructuras de datos externas o generadas.

## Ejemplo de Código
```javascript
const obj = { 'class': 'myClass' };
console.log(obj['class']); // Acceso con bracket notation
```

## Resultados en Consola
myClass

## Diagrama de Flujo de Código
<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg"><rect x="10" y="10" width="280" height="180" fill="none" stroke="black"/><text x="150" y="50" text-anchor="middle">Definir property con reserved</text><line x1="150" y1="60" x2="150" y2="100" stroke="black"/><text x="150" y="130" text-anchor="middle">Acceso con brackets</text><line x1="150" y1="140" x2="150" y2="180" stroke="black"/></svg>

## Visualización Conceptual
<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg"><rect x="20" y="20" width="160" height="160" fill="lightblue"/><text x="100" y="100" text-anchor="middle">Property Names</text><text x="100" y="120" text-anchor="middle">Reserved</text></svg>

## Casos de Uso
- Usar 'class' como key en objetos para estilos CSS.
- Definir 'function' en configs para callbacks dinámicos.
- Manejar 'let' en datos JSON de APIs externas.
- Incluir 'for' en estructuras de control personalizadas.
- Aplicar 'if' en objetos de condiciones lógicas.

## Errores Comunes
- Intentar acceso con dot notation a reserved properties, causando SyntaxError.
- Olvidar comillas al definir reserved words como keys.
- Confundir uso en objects con variables, rompiendo scope.
- No manejar reserved en JSON.parse, generando errores.
- Ignorar strict mode restricciones en property names.

## Recomendaciones
- Siempre usar bracket notation para reserved properties.
- Envolver keys en comillas en object literals.
- Usar Map en lugar de objects para keys arbitrarias.
- Validar keys en funciones de parsing.
- Documentar usos de reserved properties en código.