## 4.3.6. Problemas comunes sin semicolons

### Introducción
Omitir semicolons causa bugs sutiles.  
ASI no siempre inserta ; correctamente.  
Problemas en concatenación de scripts.  
Dificulta lectura en equipos.  
Afecta minificación.  
¿Evitas problemas con ;?

### Código de Ejemplo
```javascript
// Return multilínea
return
{
  a: 1
}  // ASI inserta ; después return, retorna undefined

// IIFE
let x = function() {}
x()  // OK

let y = function() {}
y()  // SyntaxError si no ;

// Array literal
[1,2,3]
.forEach(console.log)  // Error: ASI no inserta ;

// Postfix
let a = 1
++a  // Interpreta como 1++a, error
```
### Resultados en Consola
- SyntaxError inesperados
- Undefined en returns
- TypeError en llamadas
- Comportamiento no esperado

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> OmitSemi["Paso 1: <PERSON>mitir ; <br> - <PERSON><PERSON><PERSON> sin ;"]
    OmitSemi --> ASIApply["Paso 2: ASI aplica <br> - Insertar ;?"]
    ASIApply -->|Incorrecto| BugSubtle["Paso 3: Bug sutil <br> - Comportamiento erróneo"]
    ASIApply -->|Correcto| RunOK["Paso 4: Ejecuta OK <br> - Pero riesgoso"]
    BugSubtle --> Debug["Paso 5: Debug <br> - Difícil detectar"]
    Debug --> AddSemi["Paso 6: Añadir ; <br> - Fix bug"]
    AddSemi --> SafeCode["Paso 7: Código safe <br> - Consistente"]
    SafeCode --> Fin["Paso 8: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Avoid["Evitar omisiones"]
    ASIApply --> NotaASI["Nota: Reglas ASI limitadas"]
    BugSubtle --> NotaBug["Nota: Bugs comunes"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style OmitSemi fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ASIApply fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style BugSubtle fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style RunOK fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Debug fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style AddSemi fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style SafeCode fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Avoid fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaASI fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaBug fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> CodeNoSemi["Código sin ; <br> - Potencial issue"]
        CodeNoSemi --> ASI["ASI intenta fix <br> - Insertar ;"]
        ASI -->|Falla| Bug["Bug <br> - Syntax/Logic error"]
        ASI -->|OK| Risk["Riesgo latente <br> - Futuros issues"]
        Bug --> Detect["Detectar <br> - Debug"]
        Detect --> Fix["Fix <br> - Añadir ;"]
        Fix --> Safe["Código safe"]
        Inicio --> Desarrollador(("Desarrollador")) --> UseSemi["Usar ; siempre"]
        ASI --> NotaFalla["Nota: Casos falla ASI"]
        Bug --> NotaCommon["Nota: Problemas comunes"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CodeNoSemi fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ASI fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Bug fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Risk fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style Detect fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fix fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Safe fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style UseSemi fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaFalla fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaCommon fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Debugging en producción.
2. Concatenación de files.
3. Multilínea expressions.
4. IIFE en scripts.
5. Array methods chaining.

### Errores comunes
1. Return con newline.
2. Postfix operators.
3. Starting with ( o [.
4. No ; antes IIFE.
5. ASI en loops.

### Recomendaciones
1. Siempre usa ;.
2. Entiende ASI rules.
3. Usa linters.
4. Test multilínea.
5. Review código.