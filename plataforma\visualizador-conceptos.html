<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualizador de Conceptos JavaScript - Maestría</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #fff;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #f7df1e, #ffeb3b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .header p {
            color: #ccc;
            font-size: 1.1rem;
        }

        .concepts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .concept-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 2rem;
            transition: all 0.3s;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .concept-card:hover {
            transform: translateY(-5px);
            border-color: #f7df1e;
            box-shadow: 0 10px 30px rgba(247, 223, 30, 0.2);
        }

        .concept-card.active {
            border-color: #f7df1e;
            background: rgba(247, 223, 30, 0.1);
        }

        .concept-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .concept-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #f7df1e;
        }

        .concept-description {
            color: #ccc;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .concept-difficulty {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            font-size: 0.85rem;
            color: #f7df1e;
        }

        .visualization-area {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 2rem;
            min-height: 500px;
            position: relative;
        }

        .visualization-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .visualization-title {
            font-size: 1.5rem;
            color: #f7df1e;
        }

        .controls {
            display: flex;
            gap: 1rem;
        }

        .btn {
            background: #f7df1e;
            color: #000;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn:hover {
            background: #ffeb3b;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: transparent;
            color: #f7df1e;
            border: 1px solid #f7df1e;
        }

        .btn-secondary:hover {
            background: #f7df1e;
            color: #000;
        }

        .visualization-content {
            position: relative;
            min-height: 400px;
        }

        /* Estilos específicos para visualizaciones */
        .scope-visualization {
            display: none;
        }

        .scope-visualization.active {
            display: block;
        }

        .scope-level {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid #f7df1e;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            position: relative;
        }

        .scope-level.function-scope {
            border-color: #58a6ff;
            background: rgba(88, 166, 255, 0.1);
        }

        .scope-level.block-scope {
            border-color: #56d364;
            background: rgba(86, 211, 100, 0.1);
        }

        .scope-label {
            position: absolute;
            top: -12px;
            left: 1rem;
            background: #1a1a1a;
            padding: 0 0.5rem;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .variable {
            display: inline-block;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            border-radius: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .variable.var { background: rgba(255, 107, 107, 0.3); }
        .variable.let { background: rgba(88, 166, 255, 0.3); }
        .variable.const { background: rgba(86, 211, 100, 0.3); }

        .hoisting-visualization {
            display: none;
        }

        .hoisting-visualization.active {
            display: block;
        }

        .code-execution {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .code-panel {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .code-panel h3 {
            color: #f7df1e;
            margin-bottom: 1rem;
        }

        .code-line {
            font-family: 'Courier New', monospace;
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .code-line.executing {
            background: rgba(247, 223, 30, 0.2);
            border-left: 3px solid #f7df1e;
        }

        .code-line.hoisted {
            background: rgba(88, 166, 255, 0.2);
            border-left: 3px solid #58a6ff;
        }

        .memory-panel {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 1.5rem;
        }

        .memory-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            margin: 0.5rem 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            font-family: 'Courier New', monospace;
        }

        .memory-item.undefined {
            background: rgba(255, 107, 107, 0.2);
        }

        .memory-item.defined {
            background: rgba(86, 211, 100, 0.2);
        }

        .prototype-visualization {
            display: none;
        }

        .prototype-visualization.active {
            display: block;
        }

        .prototype-chain {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2rem;
        }

        .prototype-object {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid #f7df1e;
            border-radius: 12px;
            padding: 1.5rem;
            min-width: 200px;
            text-align: center;
            position: relative;
        }

        .prototype-object.instance {
            border-color: #58a6ff;
        }

        .prototype-object.constructor {
            border-color: #56d364;
        }

        .prototype-arrow {
            font-size: 2rem;
            color: #f7df1e;
            margin: 0.5rem 0;
        }

        .object-property {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 2rem 0;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .step.active {
            background: #f7df1e;
            color: #000;
        }

        .step.completed {
            background: #56d364;
            color: #000;
        }

        @media (max-width: 768px) {
            .concepts-grid {
                grid-template-columns: 1fr;
            }
            
            .code-execution {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Visualizador de Conceptos JavaScript</h1>
            <p>Comprende conceptos complejos a través de visualizaciones interactivas</p>
        </div>

        <div class="concepts-grid">
            <div class="concept-card" data-concept="scope">
                <div class="concept-icon">🔍</div>
                <div class="concept-title">Scope y Variables</div>
                <div class="concept-description">
                    Visualiza cómo funcionan los diferentes tipos de scope en JavaScript y el comportamiento de var, let y const.
                </div>
                <div class="concept-difficulty">Intermedio</div>
            </div>

            <div class="concept-card" data-concept="hoisting">
                <div class="concept-icon">⬆️</div>
                <div class="concept-title">Hoisting</div>
                <div class="concept-description">
                    Entiende cómo JavaScript "eleva" las declaraciones de variables y funciones al inicio del scope.
                </div>
                <div class="concept-difficulty">Intermedio</div>
            </div>

            <div class="concept-card" data-concept="prototype">
                <div class="concept-icon">🔗</div>
                <div class="concept-title">Prototype Chain</div>
                <div class="concept-description">
                    Explora la cadena de prototipos y cómo funciona la herencia en JavaScript.
                </div>
                <div class="concept-difficulty">Avanzado</div>
            </div>

            <div class="concept-card" data-concept="eventloop">
                <div class="concept-icon">🔄</div>
                <div class="concept-title">Event Loop</div>
                <div class="concept-description">
                    Comprende cómo JavaScript maneja la asincronía y el orden de ejecución.
                </div>
                <div class="concept-difficulty">Avanzado</div>
            </div>

            <div class="concept-card" data-concept="closures">
                <div class="concept-icon">📦</div>
                <div class="concept-title">Closures</div>
                <div class="concept-description">
                    Visualiza cómo las funciones "recuerdan" su entorno léxico y crean closures.
                </div>
                <div class="concept-difficulty">Avanzado</div>
            </div>

            <div class="concept-card" data-concept="this">
                <div class="concept-icon">👆</div>
                <div class="concept-title">This Binding</div>
                <div class="concept-description">
                    Entiende cómo se determina el valor de 'this' en diferentes contextos.
                </div>
                <div class="concept-difficulty">Intermedio</div>
            </div>
        </div>

        <div class="visualization-area">
            <div class="visualization-header">
                <div class="visualization-title" id="visualizationTitle">
                    Selecciona un concepto para visualizar
                </div>
                <div class="controls">
                    <button class="btn" id="playBtn" style="display: none;">▶ Reproducir</button>
                    <button class="btn-secondary" id="resetBtn" style="display: none;">🔄 Reiniciar</button>
                    <button class="btn-secondary" id="stepBtn" style="display: none;">👣 Paso a paso</button>
                </div>
            </div>

            <div class="visualization-content">
                <!-- Scope Visualization -->
                <div class="scope-visualization" id="scopeViz">
                    <div class="scope-level">
                        <div class="scope-label">Global Scope</div>
                        <div class="variable var">var globalVar = "global"</div>
                        <div class="variable let">let globalLet = "global"</div>
                        <div class="variable const">const globalConst = "global"</div>
                        
                        <div class="scope-level function-scope">
                            <div class="scope-label">Function Scope</div>
                            <div class="variable var">var functionVar = "function"</div>
                            <div class="variable let">let functionLet = "function"</div>
                            
                            <div class="scope-level block-scope">
                                <div class="scope-label">Block Scope</div>
                                <div class="variable let">let blockLet = "block"</div>
                                <div class="variable const">const blockConst = "block"</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 2rem; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 8px;">
                        <h3 style="color: #f7df1e; margin-bottom: 1rem;">Reglas de Scope:</h3>
                        <ul style="color: #ccc; line-height: 1.8;">
                            <li><span style="color: #ff6b6b;">var</span>: Function scope o global scope</li>
                            <li><span style="color: #58a6ff;">let</span>: Block scope</li>
                            <li><span style="color: #56d364;">const</span>: Block scope, no reasignable</li>
                        </ul>
                    </div>
                </div>

                <!-- Hoisting Visualization -->
                <div class="hoisting-visualization" id="hoistingViz">
                    <div class="code-execution">
                        <div class="code-panel">
                            <h3>Código Original</h3>
                            <div class="code-line" data-step="1">console.log(x); // undefined</div>
                            <div class="code-line" data-step="2">console.log(y); // ReferenceError</div>
                            <div class="code-line" data-step="3">console.log(myFunc); // function</div>
                            <div class="code-line" data-step="4">var x = 5;</div>
                            <div class="code-line" data-step="5">let y = 10;</div>
                            <div class="code-line" data-step="6">function myFunc() { return "hello"; }</div>
                        </div>
                        
                        <div class="code-panel">
                            <h3>Cómo lo ve JavaScript</h3>
                            <div class="code-line hoisted">var x; // hoisted</div>
                            <div class="code-line hoisted">function myFunc() { return "hello"; } // hoisted</div>
                            <div class="code-line">console.log(x); // undefined</div>
                            <div class="code-line">console.log(y); // TDZ</div>
                            <div class="code-line">console.log(myFunc); // function</div>
                            <div class="code-line">x = 5;</div>
                            <div class="code-line">let y = 10; // declaración</div>
                        </div>
                    </div>
                    
                    <div class="memory-panel">
                        <h3 style="color: #f7df1e; margin-bottom: 1rem;">Estado de la Memoria</h3>
                        <div class="memory-item undefined">
                            <span>x</span>
                            <span>undefined</span>
                        </div>
                        <div class="memory-item undefined">
                            <span>y</span>
                            <span>TDZ (Temporal Dead Zone)</span>
                        </div>
                        <div class="memory-item defined">
                            <span>myFunc</span>
                            <span>function</span>
                        </div>
                    </div>
                </div>

                <!-- Prototype Visualization -->
                <div class="prototype-visualization" id="prototypeViz">
                    <div class="prototype-chain">
                        <div class="prototype-object instance">
                            <h3 style="color: #58a6ff; margin-bottom: 1rem;">Instancia: person</h3>
                            <div class="object-property">name: "Juan"</div>
                            <div class="object-property">age: 30</div>
                            <div class="object-property">__proto__: Person.prototype</div>
                        </div>
                        
                        <div class="prototype-arrow">⬇️</div>
                        
                        <div class="prototype-object constructor">
                            <h3 style="color: #56d364; margin-bottom: 1rem;">Person.prototype</h3>
                            <div class="object-property">constructor: Person</div>
                            <div class="object-property">greet: function</div>
                            <div class="object-property">__proto__: Object.prototype</div>
                        </div>
                        
                        <div class="prototype-arrow">⬇️</div>
                        
                        <div class="prototype-object">
                            <h3 style="color: #f7df1e; margin-bottom: 1rem;">Object.prototype</h3>
                            <div class="object-property">toString: function</div>
                            <div class="object-property">valueOf: function</div>
                            <div class="object-property">hasOwnProperty: function</div>
                            <div class="object-property">__proto__: null</div>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; color: #666; margin-top: 2rem; display: none;" id="selectMessage">
                    <p>👆 Selecciona un concepto arriba para comenzar la visualización</p>
                </div>
            </div>

            <div class="step-indicator" id="stepIndicator" style="display: none;">
                <div class="step active" data-step="1">1</div>
                <div class="step" data-step="2">2</div>
                <div class="step" data-step="3">3</div>
                <div class="step" data-step="4">4</div>
                <div class="step" data-step="5">5</div>
            </div>
        </div>
    </div>

    <script>
        class ConceptVisualizer {
            constructor() {
                this.currentConcept = null;
                this.currentStep = 0;
                this.isPlaying = false;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.showSelectMessage();
            }

            setupEventListeners() {
                // Concept cards
                document.querySelectorAll('.concept-card').forEach(card => {
                    card.addEventListener('click', () => {
                        const concept = card.dataset.concept;
                        this.selectConcept(concept, card);
                    });
                });

                // Control buttons
                document.getElementById('playBtn').addEventListener('click', () => {
                    this.togglePlay();
                });

                document.getElementById('resetBtn').addEventListener('click', () => {
                    this.reset();
                });

                document.getElementById('stepBtn').addEventListener('click', () => {
                    this.nextStep();
                });

                // Step indicators
                document.querySelectorAll('.step').forEach(step => {
                    step.addEventListener('click', () => {
                        const stepNumber = parseInt(step.dataset.step);
                        this.goToStep(stepNumber);
                    });
                });
            }

            selectConcept(concept, cardElement) {
                // Update active card
                document.querySelectorAll('.concept-card').forEach(card => {
                    card.classList.remove('active');
                });
                cardElement.classList.add('active');

                // Hide all visualizations
                document.querySelectorAll('.scope-visualization, .hoisting-visualization, .prototype-visualization').forEach(viz => {
                    viz.classList.remove('active');
                });

                // Show selected visualization
                this.currentConcept = concept;
                this.showVisualization(concept);
                this.showControls();
                this.hideSelectMessage();
                this.reset();
            }

            showVisualization(concept) {
                const title = document.getElementById('visualizationTitle');
                
                switch(concept) {
                    case 'scope':
                        title.textContent = '🔍 Scope y Variables';
                        document.getElementById('scopeViz').classList.add('active');
                        this.animateScope();
                        break;
                    case 'hoisting':
                        title.textContent = '⬆️ Hoisting';
                        document.getElementById('hoistingViz').classList.add('active');
                        break;
                    case 'prototype':
                        title.textContent = '🔗 Prototype Chain';
                        document.getElementById('prototypeViz').classList.add('active');
                        this.animatePrototype();
                        break;
                    default:
                        title.textContent = `${concept.charAt(0).toUpperCase() + concept.slice(1)} (En desarrollo)`;
                        this.showComingSoon();
                }
            }

            animateScope() {
                const scopes = document.querySelectorAll('.scope-level');
                scopes.forEach((scope, index) => {
                    setTimeout(() => {
                        scope.style.opacity = '0';
                        scope.style.transform = 'translateY(20px)';
                        scope.style.transition = 'all 0.5s ease';
                        
                        setTimeout(() => {
                            scope.style.opacity = '1';
                            scope.style.transform = 'translateY(0)';
                        }, 100);
                    }, index * 200);
                });
            }

            animatePrototype() {
                const objects = document.querySelectorAll('.prototype-object');
                const arrows = document.querySelectorAll('.prototype-arrow');
                
                objects.forEach(obj => {
                    obj.style.opacity = '0';
                    obj.style.transform = 'scale(0.8)';
                });
                
                arrows.forEach(arrow => {
                    arrow.style.opacity = '0';
                });

                // Animate in sequence
                objects.forEach((obj, index) => {
                    setTimeout(() => {
                        obj.style.transition = 'all 0.5s ease';
                        obj.style.opacity = '1';
                        obj.style.transform = 'scale(1)';
                        
                        if (index < arrows.length) {
                            setTimeout(() => {
                                arrows[index].style.transition = 'all 0.3s ease';
                                arrows[index].style.opacity = '1';
                            }, 250);
                        }
                    }, index * 600);
                });
            }

            showComingSoon() {
                const content = document.querySelector('.visualization-content');
                content.innerHTML = `
                    <div style="text-align: center; padding: 4rem; color: #666;">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">🚧</div>
                        <h3 style="color: #f7df1e; margin-bottom: 1rem;">Próximamente</h3>
                        <p>Esta visualización está en desarrollo. ¡Pronto estará disponible!</p>
                    </div>
                `;
            }

            togglePlay() {
                const playBtn = document.getElementById('playBtn');
                
                if (this.isPlaying) {
                    this.pause();
                    playBtn.textContent = '▶ Reproducir';
                } else {
                    this.play();
                    playBtn.textContent = '⏸ Pausar';
                }
                
                this.isPlaying = !this.isPlaying;
            }

            play() {
                if (this.currentConcept === 'hoisting') {
                    this.playHoistingAnimation();
                }
            }

            pause() {
                // Clear any running animations
                clearInterval(this.animationInterval);
            }

            playHoistingAnimation() {
                const codeLines = document.querySelectorAll('.code-line[data-step]');
                const memoryItems = document.querySelectorAll('.memory-item');
                
                let step = 1;
                this.animationInterval = setInterval(() => {
                    // Reset previous highlights
                    codeLines.forEach(line => line.classList.remove('executing'));
                    
                    // Highlight current line
                    const currentLine = document.querySelector(`[data-step="${step}"]`);
                    if (currentLine) {
                        currentLine.classList.add('executing');
                    }
                    
                    // Update memory state
                    this.updateMemoryState(step);
                    
                    step++;
                    if (step > 6) {
                        this.pause();
                        document.getElementById('playBtn').textContent = '▶ Reproducir';
                        this.isPlaying = false;
                        step = 1;
                    }
                }, 1500);
            }

            updateMemoryState(step) {
                const memoryItems = document.querySelectorAll('.memory-item');
                
                switch(step) {
                    case 1:
                        // console.log(x) - x is undefined
                        memoryItems[0].classList.add('undefined');
                        break;
                    case 4:
                        // x = 5
                        memoryItems[0].classList.remove('undefined');
                        memoryItems[0].classList.add('defined');
                        memoryItems[0].querySelector('span:last-child').textContent = '5';
                        break;
                    case 5:
                        // let y = 10
                        memoryItems[1].classList.remove('undefined');
                        memoryItems[1].classList.add('defined');
                        memoryItems[1].querySelector('span:last-child').textContent = '10';
                        break;
                }
            }

            nextStep() {
                this.currentStep++;
                // Implement step-by-step logic here
                console.log(`Step ${this.currentStep}`);
            }

            goToStep(stepNumber) {
                this.currentStep = stepNumber;
                // Update step indicators
                document.querySelectorAll('.step').forEach((step, index) => {
                    step.classList.remove('active', 'completed');
                    if (index + 1 < stepNumber) {
                        step.classList.add('completed');
                    } else if (index + 1 === stepNumber) {
                        step.classList.add('active');
                    }
                });
            }

            reset() {
                this.currentStep = 0;
                this.pause();
                this.isPlaying = false;
                document.getElementById('playBtn').textContent = '▶ Reproducir';
                
                // Reset all visual states
                document.querySelectorAll('.code-line').forEach(line => {
                    line.classList.remove('executing');
                });
                
                document.querySelectorAll('.memory-item').forEach(item => {
                    item.classList.remove('defined');
                    item.classList.add('undefined');
                });
                
                // Reset memory values
                const memoryItems = document.querySelectorAll('.memory-item');
                if (memoryItems.length >= 2) {
                    memoryItems[0].querySelector('span:last-child').textContent = 'undefined';
                    memoryItems[1].querySelector('span:last-child').textContent = 'TDZ (Temporal Dead Zone)';
                }
            }

            showControls() {
                document.getElementById('playBtn').style.display = 'inline-block';
                document.getElementById('resetBtn').style.display = 'inline-block';
                document.getElementById('stepBtn').style.display = 'inline-block';
            }

            showSelectMessage() {
                document.getElementById('selectMessage').style.display = 'block';
            }

            hideSelectMessage() {
                document.getElementById('selectMessage').style.display = 'none';
            }
        }

        // Initialize the visualizer
        document.addEventListener('DOMContentLoaded', () => {
            new ConceptVisualizer();
        });
    </script>
</body>
</html>
