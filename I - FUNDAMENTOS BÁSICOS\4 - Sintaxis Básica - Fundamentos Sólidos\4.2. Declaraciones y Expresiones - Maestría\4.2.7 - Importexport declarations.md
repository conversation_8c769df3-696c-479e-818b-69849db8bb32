## 4.2.7. Import/export declarations

### Introducción
Las import y export declarations en JavaScript permiten modularidad.  
Exports nombran o default valores.  
Imports usan from para módulos.  
Son hoisted y estáticas.  
Facilitan código reutilizable.  
¿Usas named o default exports?

### Código de Ejemplo
```javascript
// modulo.js
export const PI = 3.14159;
export function sumar(a, b) {
    return a + b;
}
export default function multiplicar(a, b) {
    return a * b;
}

// main.js
import multiplicar, { PI, sumar } from './modulo.js';
console.log(PI);
console.log(sumar(2, 3));
console.log(multiplicar(4, 5));

// Import all
import * as Modulo from './modulo.js';
console.log(Modulo.PI);

// Dynamic import
// import('./modulo.js').then(module => console.log(module.PI));
```
### Resultados en Consola
- `3.14159`
- `5`
- `20`
- `3.14159`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> ExportDecl["Paso 1: export const PI <br> - Named export"]
    ExportDecl --> ExportFunc["Paso 2: export function sumar <br> - Function export"]
    ExportFunc --> DefaultExport["Paso 3: export default function multiplicar <br> - Default export"]
    DefaultExport --> ImportDecl["Paso 4: import multiplicar, { PI, sumar } <br> - Import"]
    ImportDecl --> LogPI["Paso 5: console.log(PI) <br> - Resultado: 3.14159"]
    LogPI --> LogSumar["Paso 6: console.log(sumar(2, 3)) <br> - Resultado: 5"]
    LogSumar --> LogMult["Paso 7: console.log(multiplicar(4, 5)) <br> - Resultado: 20"]
    LogMult --> ImportAll["Paso 8: import * as Modulo <br> - Import all"]
    ImportAll --> LogModulo["Paso 9: console.log(Modulo.PI) <br> - Resultado: 3.14159"]
    LogModulo --> Dynamic["Paso 10: // import('./modulo.js') <br> - Dynamic import"]
    Dynamic --> Fin["Paso 11: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Modularidad["Modularidad"]
    ExportDecl --> NotaExport["Nota: Hoisted"]
    ImportDecl --> NotaImport["Nota: Estática"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ExportDecl fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ExportFunc fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style DefaultExport fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ImportDecl fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogPI fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogSumar fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogMult fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ImportAll fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogModulo fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Dynamic fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Modularidad fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaExport fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaImport fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Export["Export <br> - Named o default"]
        Export --> Import["Import <br> - From módulo"]
        Import --> Uso["Uso: Acceso a exports"]
        Uso --> ImportAll["Import * as <br> - Todo el módulo"]
        ImportAll --> DynamicImport["Dynamic import <br> - Asíncrono"]
        DynamicImport --> Modularidad["Modularidad <br> - Código reutilizable"]
        Inicio --> Desarrollador(("Desarrollador")) --> Eleccion["Elección de exports"]
        Export --> NotaExp["Nota: Hoisted"]
        Import --> NotaImp["Nota: Estática"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Export fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Import fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Uso fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ImportAll fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style DynamicImport fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Modularidad fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Eleccion fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaExp fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaImp fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Modularizar código en archivos.
2. Reutilizar funciones entre módulos.
3. Exports default para componentes.
4. Named exports para utilidades.
5. Dynamic imports para lazy loading.

### Errores comunes
1. Olvidar extensión .js en imports.
2. Circular dependencies.
3. Mezclar default y named imports.
4. Usar import sin type=module.
5. Ignorar hoisting en exports.

### Recomendaciones
1. Prefiere named exports para claridad.
2. Usa default para export principal.
3. Evita circular dependencies.
4. Usa dynamic imports para performance.
5. Organiza módulos lógicamente.