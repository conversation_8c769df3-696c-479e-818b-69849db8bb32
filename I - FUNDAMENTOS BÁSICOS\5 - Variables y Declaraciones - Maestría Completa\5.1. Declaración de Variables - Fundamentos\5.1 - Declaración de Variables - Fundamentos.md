# 5.1 - Declaración de Variables - Fundamentos

## Introducción

La declaración de variables constituye uno de los conceptos más fundamentales en JavaScript y la base sobre la cual se construye toda la lógica de programación. En JavaScript moderno, disponemos de tres palabras clave principales para declarar variables: `var`, `let` y `const`, cada una con características específicas que determinan su comportamiento en términos de scope (alcance), hoisting (elevación), reasignación y redeclaración. Comprender profundamente estas diferencias es crucial para escribir código JavaScript robusto, predecible y libre de errores.

La evolución de JavaScript ha introducido `let` y `const` en ES6 (ECMAScript 2015) para abordar las limitaciones y comportamientos confusos de `var`, proporcionando un control más granular sobre el ciclo de vida de las variables. Esta sección explora exhaustivamente cada método de declaración, sus casos de uso apropiados, las mejores prácticas de la industria y cómo evitar los errores más comunes que pueden surgir por un uso inadecuado de estas declaraciones.

El dominio de la declaración de variables no solo mejora la calidad del código, sino que también facilita el debugging, optimiza el rendimiento y hace que el código sea más mantenible y comprensible para otros desarrolladores del equipo.

## Código de Ejemplo

### Declaraciones de Variables Fundamentales

```javascript
// 1. Declaración con var (función scope)
function demonstrateVar() {
    console.log('Antes de declarar:', typeof varVariable); // undefined
    
    if (true) {
        var varVariable = 'Soy var';
        console.log('Dentro del bloque:', varVariable);
    }
    
    console.log('Fuera del bloque:', varVariable); // Accesible
    
    // Redeclaración permitida
    var varVariable = 'Redeclarada';
    console.log('Redeclarada:', varVariable);
}

// 2. Declaración con let (bloque scope)
function demonstrateLet() {
    // console.log('Antes de declarar:', letVariable); // ReferenceError
    
    if (true) {
        let letVariable = 'Soy let';
        console.log('Dentro del bloque:', letVariable);
    }
    
    // console.log('Fuera del bloque:', letVariable); // ReferenceError
    
    let letVariable = 'Nueva declaración';
    console.log('Nueva declaración:', letVariable);
}

// 3. Declaración con const (bloque scope, inmutable)
function demonstrateConst() {
    const constVariable = 'Soy const';
    console.log('Declaración inicial:', constVariable);
    
    // constVariable = 'Intento cambiar'; // TypeError
    
    // Objetos const son mutables en contenido
    const user = { name: 'Juan', age: 25 };
    user.age = 26; // Permitido
    console.log('Objeto modificado:', user);
    
    // Arrays const son mutables en contenido
    const numbers = [1, 2, 3];
    numbers.push(4); // Permitido
    console.log('Array modificado:', numbers);
}

// 4. Comparación de comportamientos
function compareDeclarations() {
    console.log('=== Comparación de Declaraciones ===');
    
    // Hoisting con var
    console.log('var antes:', varHoisted); // undefined
    var varHoisted = 'Valor var';
    
    // let y const en Temporal Dead Zone
    try {
        console.log('let antes:', letHoisted); // ReferenceError
    } catch (e) {
        console.log('Error con let:', e.name);
    }
    
    let letHoisted = 'Valor let';
    const constHoisted = 'Valor const';
    
    console.log('Valores finales:', { varHoisted, letHoisted, constHoisted });
}

// Ejecutar demostraciones
demonstrateVar();
demonstrateeLet();
demonstrateConst();
compareDeclarations();
```

## Resultado en Consola

```
=== Demostración de var ===
Antes de declarar: undefined
Dentro del bloque: Soy var
Fuera del bloque: Soy var
Redeclarada: Redeclarada

=== Demostración de let ===
Dentro del bloque: Soy let
Nueva declaración: Nueva declaración

=== Demostración de const ===
Declaración inicial: Soy const
Objeto modificado: { name: 'Juan', age: 26 }
Array modificado: [1, 2, 3, 4]

=== Comparación de Declaraciones ===
var antes: undefined
Error con let: ReferenceError
Valores finales: {
  varHoisted: 'Valor var',
  letHoisted: 'Valor let',
  constHoisted: 'Valor const'
}
```

## Diagrama de Flujo

![Diagrama de Declaración de Variables](SVG/js_variable_declaration_flowchart.svg)

## Visualización Conceptual

```mermaid
graph TB
    A[Necesidad de Variable] --> B{Tipo de Declaración}
    B -->|Valor Constante| C[const]
    B -->|Valor Variable| D{Scope Requerido}
    B -->|Legacy/Compatibilidad| E[var]
    
    D -->|Bloque Scope| F[let]
    D -->|Función Scope| E
    
    C --> G[Inmutable Referencia]
    F --> H[Mutable + Block Scope]
    E --> I[Mutable + Function Scope]
    
    G --> J{Tipo de Valor}
    J -->|Primitivo| K[Completamente Inmutable]
    J -->|Objeto/Array| L[Referencia Inmutable, Contenido Mutable]
    
    H --> M[Temporal Dead Zone]
    I --> N[Hoisting con undefined]
    
    K --> O[Mejor Práctica]
    L --> O
    M --> O
    N --> P[Evitar en Código Nuevo]
    
    style A fill:#e1f5fe
    style C fill:#c8e6c9
    style F fill:#c8e6c9
    style E fill:#ffecb3
    style O fill:#c8e6c9
    style P fill:#ffcdd2
```

## Casos de Uso Extensivos

### 1. Uso de const para Valores Inmutables
```javascript
// Configuraciones de aplicación
const APP_CONFIG = {
    API_URL: 'https://api.example.com',
    VERSION: '1.0.0',
    MAX_RETRIES: 3,
    TIMEOUT: 5000
};

// Funciones puras
const calculateTax = (amount, rate) => amount * rate;
const formatCurrency = (amount) => `$${amount.toFixed(2)}`;

// Referencias a elementos DOM
const loginForm = document.getElementById('login-form');
const submitButton = document.querySelector('#submit-btn');

// Expresiones regulares
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const PHONE_REGEX = /^\+?[1-9]\d{1,14}$/;
```

### 2. Uso de let para Variables Mutables
```javascript
// Contadores y acumuladores
function processItems(items) {
    let totalPrice = 0;
    let processedCount = 0;
    
    for (let i = 0; i < items.length; i++) {
        let currentItem = items[i];
        
        if (currentItem.isValid) {
            totalPrice += currentItem.price;
            processedCount++;
        }
    }
    
    return { totalPrice, processedCount };
}

// Variables de estado temporal
function handleUserInput(input) {
    let sanitizedInput = input.trim().toLowerCase();
    let validationResult = validateInput(sanitizedInput);
    
    if (!validationResult.isValid) {
        let errorMessage = `Error: ${validationResult.message}`;
        console.error(errorMessage);
        return null;
    }
    
    return sanitizedInput;
}
```

### 3. Evitar var en Código Moderno
```javascript
// ❌ Problemático con var
function problematicVar() {
    for (var i = 0; i < 3; i++) {
        setTimeout(() => {
            console.log('var i:', i); // Siempre imprime 3
        }, 100);
    }
}

// ✅ Correcto con let
function correctLet() {
    for (let i = 0; i < 3; i++) {
        setTimeout(() => {
            console.log('let i:', i); // Imprime 0, 1, 2
        }, 100);
    }
}

// ✅ Alternativa con const y funciones
function alternativeConst() {
    const numbers = [0, 1, 2];
    numbers.forEach(i => {
        setTimeout(() => {
            console.log('const i:', i); // Imprime 0, 1, 2
        }, 100);
    });
}
```

### 4. Gestión de Scope y Temporal Dead Zone
```javascript
// Demostración de Temporal Dead Zone
function temporalDeadZoneDemo() {
    console.log('Inicio de función');
    
    // TDZ para letVariable
    try {
        console.log(letVariable); // ReferenceError
    } catch (e) {
        console.log('TDZ Error:', e.message);
    }
    
    let letVariable = 'Ahora está disponible';
    console.log('let variable:', letVariable);
    
    // Bloque anidado
    {
        // Nueva TDZ para blockScoped
        try {
            console.log(blockScoped); // ReferenceError
        } catch (e) {
            console.log('Block TDZ Error:', e.message);
        }
        
        const blockScoped = 'Solo en este bloque';
        console.log('Block scoped:', blockScoped);
    }
    
    // console.log(blockScoped); // ReferenceError - fuera de scope
}
```

## Errores Comunes

### 1. **Confusión entre let/const y var**
```javascript
// ❌ Incorrecto - Esperando block scope con var
function incorrectVarUsage() {
    if (true) {
        var message = 'Disponible fuera';
    }
    console.log(message); // Funciona, pero no es la intención
}

// ✅ Correcto - Usando let para block scope
function correctLetUsage() {
    if (true) {
        let message = 'Solo dentro del bloque';
        console.log(message);
    }
    // console.log(message); // ReferenceError - como se espera
}
```

### 2. **Reasignación de const**
```javascript
// ❌ Incorrecto - Intentar reasignar const
const PI = 3.14159;
// PI = 3.14; // TypeError: Assignment to constant variable

// ✅ Correcto - Usar let para valores que cambian
let radius = 5;
radius = 10; // Permitido

const area = PI * radius * radius; // Correcto
```

### 3. **Hoisting Malentendido**
```javascript
// ❌ Incorrecto - Dependiendo del hoisting
function badHoisting() {
    console.log(hoistedVar); // undefined (confuso)
    var hoistedVar = 'Valor';
}

// ✅ Correcto - Declarar antes de usar
function goodDeclaration() {
    const value = 'Valor';
    console.log(value); // Claro y predecible
}
```

### 4. **Loops y Closures**
```javascript
// ❌ Incorrecto - var en loops con closures
const buttons = document.querySelectorAll('button');
for (var i = 0; i < buttons.length; i++) {
    buttons[i].onclick = function() {
        console.log('Button', i); // Siempre el último valor
    };
}

// ✅ Correcto - let en loops
for (let i = 0; i < buttons.length; i++) {
    buttons[i].onclick = function() {
        console.log('Button', i); // Valor correcto
    };
}
```

### 5. **Mutación de const Objects**
```javascript
// ❌ Malentendido - Pensar que const hace objetos inmutables
const user = { name: 'Juan' };
user.name = 'Pedro'; // Esto SÍ está permitido

// ✅ Correcto - Entender que const protege la referencia
const config = Object.freeze({
    apiUrl: 'https://api.example.com',
    version: '1.0'
}); // Ahora es realmente inmutable
```

## Recomendaciones Profesionales

### 1. **Jerarquía de Preferencia**
```javascript
// 1. Preferir const por defecto
const DEFAULT_CONFIG = { timeout: 5000 };
const calculateTotal = (items) => items.reduce((sum, item) => sum + item.price, 0);

// 2. Usar let cuando la reasignación es necesaria
let currentUser = null;
let retryCount = 0;

// 3. Evitar var en código nuevo
// Solo usar var en casos específicos de compatibilidad
```

### 2. **Patrones de Declaración**
```javascript
// Declaraciones agrupadas y organizadas
function processOrder(orderData) {
    // Constantes primero
    const TAX_RATE = 0.08;
    const SHIPPING_COST = 10;
    
    // Variables que cambiarán
    let subtotal = 0;
    let errors = [];
    
    // Procesamiento...
    orderData.items.forEach(item => {
        if (item.price > 0) {
            subtotal += item.price;
        } else {
            errors.push(`Invalid price for item: ${item.name}`);
        }
    });
    
    const tax = subtotal * TAX_RATE;
    const total = subtotal + tax + SHIPPING_COST;
    
    return { subtotal, tax, total, errors };
}
```

### 3. **Herramientas de Linting**
```javascript
// Configuración ESLint recomendada
{
  "rules": {
    "no-var": "error",
    "prefer-const": "error",
    "no-undef": "error",
    "no-unused-vars": "warn",
    "block-scoped-var": "error"
  }
}
```

### 4. **Documentación y Comentarios**
```javascript
/**
 * Configuración global de la aplicación
 * @constant {Object}
 */
const APP_SETTINGS = {
    theme: 'dark',
    language: 'es',
    notifications: true
};

/**
 * Procesa los datos del usuario
 * @param {Object} userData - Datos del usuario
 * @returns {Object} Usuario procesado
 */
function processUserData(userData) {
    // Variable temporal para validación
    let validationErrors = [];
    
    // Resultado final (inmutable una vez asignado)
    const processedUser = {
        id: userData.id,
        name: userData.name?.trim() || 'Usuario Anónimo',
        email: userData.email?.toLowerCase(),
        createdAt: new Date().toISOString()
    };
    
    return processedUser;
}
```

### 5. **Testing y Debugging**
```javascript
// Facilitar debugging con nombres descriptivos
function debugFriendlyFunction() {
    const startTime = performance.now();
    
    let processedItems = 0;
    let failedItems = 0;
    
    // Procesamiento con variables claras
    const items = getItemsToProcess();
    
    for (const currentItem of items) {
        try {
            processItem(currentItem);
            processedItems++;
        } catch (error) {
            console.error(`Failed to process item ${currentItem.id}:`, error);
            failedItems++;
        }
    }
    
    const endTime = performance.now();
    const executionTime = endTime - startTime;
    
    console.log(`Processing complete: ${processedItems} success, ${failedItems} failed, ${executionTime}ms`);
}
```

La correcta declaración de variables es fundamental para escribir JavaScript moderno, mantenible y libre de errores. Seguir estas prácticas garantiza código más robusto y facilita la colaboración en equipos de desarrollo.