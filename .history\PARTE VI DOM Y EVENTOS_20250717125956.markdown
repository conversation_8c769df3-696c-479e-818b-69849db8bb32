## **PARTE VI: DOM Y EVENTOS**

### **Capítulo 69: Introducción al DOM**

#### **69.1. Fundamentos del DOM**
69.1.1. ¿Qué es el DOM?
69.1.2. DOM Tree Structure
69.1.3. Nodes y Elements
69.1.4. DOM Interfaces
69.1.5. DOM Levels
69.1.6. Browser Implementation
69.1.7. Performance Considerations
69.1.8. DOM vs Virtual DOM
69.1.9. Shadow DOM
69.1.10. Best Practices

#### **69.2. Navegación del DOM**
69.2.1. Parent-Child Relationships
69.2.2. Sibling Navigation
69.2.3. Tree Traversal
69.2.4. NodeList vs HTMLCollection
69.2.5. Live vs Static Collections
69.2.6. Performance Optimization
69.2.7. Common Patterns
69.2.8. <PERSON><PERSON>r Handling
69.2.9. Cross-browser Compatibility
69.2.10. Best Practices

#### **69.3. DOM Properties**
69.3.1. Element Properties
69.3.2. Node Properties
69.3.3. Document Properties
69.3.4. Window Properties
69.3.5. Computed Styles
69.3.6. Bounding Rectangles
69.3.7. Scroll Properties
69.3.8. Visibility Properties
69.3.9. Performance Impact
69.3.10. Best Practices

### **Capítulo 70: Selección y Manipulación de Elementos**

#### **70.1. Métodos de Selección**
70.1.1. getElementById()
70.1.2. getElementsByClassName()
70.1.3. getElementsByTagName()
70.1.4. querySelector()
70.1.5. querySelectorAll()
70.1.6. Performance Comparison
70.1.7. CSS Selector Syntax
70.1.8. Pseudo-selectors
70.1.9. Attribute Selectors
70.1.10. Best Practices

#### **70.2. Manipulación de Contenido**
70.2.1. innerHTML vs textContent
70.2.2. innerText vs textContent
70.2.3. outerHTML
70.2.4. insertAdjacentHTML()
70.2.5. Security Considerations
70.2.6. Performance Optimization
70.2.7. Memory Management
70.2.8. Cross-browser Issues
70.2.9. Sanitization
70.2.10. Best Practices

#### **70.3. Manipulación de Atributos**
70.3.1. getAttribute() y setAttribute()
70.3.2. removeAttribute()
70.3.3. hasAttribute()
70.3.4. Data Attributes
70.3.5. Boolean Attributes
70.3.6. Custom Attributes
70.3.7. Attribute vs Property
70.3.8. Performance Considerations
70.3.9. Validation
70.3.10. Best Practices

### **Capítulo 71: Creación y Modificación de Elementos**

#### **71.1. Creación de Elementos**
71.1.1. createElement()
71.1.2. createTextNode()
71.1.3. createDocumentFragment()
71.1.4. cloneNode()
71.1.5. importNode()
71.1.6. Template Elements
71.1.7. Custom Elements
71.1.8. Performance Optimization
71.1.9. Memory Management
71.1.10. Best Practices

#### **71.2. Inserción de Elementos**
71.2.1. appendChild()
71.2.2. insertBefore()
71.2.3. insertAdjacentElement()
71.2.4. prepend() y append()
71.2.5. before() y after()
71.2.6. replaceWith()
71.2.7. Document Fragments
71.2.8. Batch Operations
71.2.9. Performance Optimization
71.2.10. Best Practices

#### **71.3. Eliminación de Elementos**
71.3.1. removeChild()
71.3.2. remove()
71.3.3. replaceChild()
71.3.4. Memory Cleanup
71.3.5. Event Listener Cleanup
71.3.6. Reference Cleanup
71.3.7. Performance Considerations
71.3.8. Garbage Collection
71.3.9. Common Pitfalls
71.3.10. Best Practices

### **Capítulo 72: Estilos y CSS desde JavaScript**

#### **72.1. Manipulación de Estilos**
72.1.1. style Property
72.1.2. cssText Property
72.1.3. getComputedStyle()
72.1.4. CSS Custom Properties
72.1.5. CSS-in-JS
72.1.6. Style Sheets API
72.1.7. CSS Object Model
72.1.8. Performance Optimization
72.1.9. Browser Compatibility
72.1.10. Best Practices

#### **72.2. Clases CSS**
72.2.1. className Property
72.2.2. classList API
72.2.3. add() y remove()
72.2.4. toggle()
72.2.5. contains()
72.2.6. replace()
72.2.7. Multiple Classes
72.2.8. Conditional Classes
72.2.9. Performance Considerations
72.2.10. Best Practices

#### **72.3. Animaciones CSS**
72.3.1. CSS Transitions
72.3.2. CSS Animations
72.3.3. Transform Properties
72.3.4. Animation Events
72.3.5. Performance Optimization
72.3.6. Hardware Acceleration
72.3.7. Composite Layers
72.3.8. Animation Libraries
72.3.9. Debugging Animations
72.3.10. Best Practices

### **Capítulo 73: Eventos - Fundamentos**

#### **73.1. Sistema de Eventos**
73.1.1. Event-driven Programming
73.1.2. Event Types
73.1.3. Event Objects
73.1.4. Event Flow
73.1.5. Event Phases
73.1.6. Event Propagation
73.1.7. Event Bubbling
73.1.8. Event Capturing
73.1.9. Event Delegation
73.1.10. Performance Considerations

#### **73.2. Event Listeners**
73.2.1. addEventListener()
73.2.2. removeEventListener()
73.2.3. Event Handler Properties
73.2.4. Inline Event Handlers
73.2.5. Event Options
73.2.6. Passive Listeners
73.2.7. Once Option
73.2.8. Signal Option
73.2.9. Memory Management
73.2.10. Best Practices

#### **73.3. Event Object**
73.3.1. Event Properties
73.3.2. target vs currentTarget
73.3.3. preventDefault()
73.3.4. stopPropagation()
73.3.5. stopImmediatePropagation()
73.3.6. Event Coordinates
73.3.7. Keyboard Events
73.3.8. Mouse Events
73.3.9. Touch Events
73.3.10. Custom Properties

### **Capítulo 74: Eventos - Tipos y Manejo**

#### **74.1. Mouse Events**
74.1.1. click y dblclick
74.1.2. mousedown y mouseup
74.1.3. mouseover y mouseout
74.1.4. mouseenter y mouseleave
74.1.5. mousemove
74.1.6. contextmenu
74.1.7. wheel
74.1.8. Event Coordinates
74.1.9. Button Detection
74.1.10. Performance Optimization

#### **74.2. Keyboard Events**
74.2.1. keydown y keyup
74.2.2. keypress (deprecated)
74.2.3. Key Codes
74.2.4. Key Values
74.2.5. Modifier Keys
74.2.6. Input Method Editor
74.2.7. Accessibility Considerations
74.2.8. Cross-browser Issues
74.2.9. Security Considerations
74.2.10. Best Practices

#### **74.3. Form Events**
74.3.1. submit
74.3.2. input
74.3.3. change
74.3.4. focus y blur
74.3.5. focusin y focusout
74.3.6. invalid
74.3.7. reset
74.3.8. select
74.3.9. Validation Events
74.3.10. Performance Optimization

#### **74.4. Touch Events**
74.4.1. touchstart
74.4.2. touchmove
74.4.3. touchend
74.4.4. touchcancel
74.4.5. Touch Objects
74.4.6. Multi-touch Handling
74.4.7. Gesture Recognition
74.4.8. Performance Optimization
74.4.9. Accessibility
74.4.10. Best Practices

### **Capítulo 75: Event Delegation y Bubbling**

#### **75.1. Event Bubbling**
75.1.1. Bubbling Mechanism
75.1.2. Event Path
75.1.3. Stopping Bubbling
75.1.4. Bubbling Performance
75.1.5. Common Use Cases
75.1.6. Debugging Bubbling
75.1.7. Cross-browser Issues
75.1.8. Best Practices
75.1.9. Anti-patterns
75.1.10. Optimization Techniques

#### **75.2. Event Capturing**
75.2.1. Capturing Phase
75.2.2. useCapture Parameter
75.2.3. Capturing vs Bubbling
75.2.4. Use Cases
75.2.5. Performance Implications
75.2.6. Debugging Capturing
75.2.7. Browser Support
75.2.8. Best Practices
75.2.9. Common Pitfalls
75.2.10. Optimization

#### **75.3. Event Delegation**
75.3.1. Delegation Pattern
75.3.2. Benefits y Drawbacks
75.3.3. Implementation Strategies
75.3.4. Dynamic Content
75.3.5. Performance Benefits
75.3.6. Memory Optimization
75.3.7. Event Filtering
75.3.8. Debugging Delegation
75.3.9. Testing Strategies
75.3.10. Best Practices
