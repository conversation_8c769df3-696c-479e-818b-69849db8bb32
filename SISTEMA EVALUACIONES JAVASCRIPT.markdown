# **SISTEMA DE EVALUACIONES JAVASCRIPT**

## **📝 ESTRUCTURA DE EVALUACIONES**

### **Nivel 1: Fundamentos (Capítulos 1-50)**
- **Duración:** 45 minutos
- **Preguntas:** 30 (20 múltiple opción, 10 código)
- **Puntaje mínimo:** 70%

### **Nivel 2: Intermedio (Capítulos 51-100)**
- **Duración:** 60 minutos
- **Preguntas:** 40 (25 múltiple opción, 15 código)
- **Puntaje mínimo:** 75%

### **Nivel 3: Avanzado (Capítulos 101-150)**
- **Duración:** 75 minutos
- **Preguntas:** 50 (30 múltiple opción, 20 código)
- **Puntaje mínimo:** 80%

### **Nivel 4: Experto (Capítulos 151-200)**
- **Duración:** 90 minutos
- **Preguntas:** 60 (35 múltiple opción, 25 código)
- **Puntaje mínimo:** 85%

---

## **🧪 QUIZ NIVEL 1: FUNDAMENTOS**

### **Pregunta 1 - Variables y Tipos**
**¿Cuál es la diferencia principal entre `let` y `var`?**

A) `let` tiene function scope, `var` tiene block scope
B) `let` tiene block scope, `var` tiene function scope ✅
C) No hay diferencia
D) `let` no puede ser reasignado

**Explicación:** `let` introduce block scope en ES6, mientras que `var` tiene function scope, lo que puede causar problemas de hoisting y scope inesperados.

### **Pregunta 2 - Funciones**
**¿Qué imprime el siguiente código?**
```javascript
function test() {
    console.log(a);
    var a = 5;
}
test();
```

A) 5
B) undefined ✅
C) ReferenceError
D) null

**Explicación:** Debido al hoisting, la declaración de `a` se mueve al inicio de la función, pero la asignación permanece en su lugar, por lo que `a` es `undefined` cuando se imprime.

### **Pregunta 3 - Objetos**
**¿Cuál es la forma correcta de acceder a una propiedad de objeto cuando el nombre de la propiedad está en una variable?**

A) `obj.propertyName`
B) `obj[propertyName]` ✅
C) `obj->propertyName`
D) `obj::propertyName`

### **Pregunta 4 - Arrays**
**¿Qué método de array NO modifica el array original?**

A) `push()`
B) `pop()`
C) `map()` ✅
D) `splice()`

### **Pregunta 5 - Código Práctico**
**Completa la función para que retorne la suma de todos los números en un array:**

```javascript
function sumarArray(numeros) {
    // Tu código aquí
    return numeros.reduce((suma, num) => suma + num, 0);
}
```

**Opciones:**
A) `return numeros.reduce((suma, num) => suma + num, 0);` ✅
B) `return numeros.map(num => num).sum();`
C) `return numeros.forEach(num => sum += num);`
D) `return numeros.join('+');`

---

## **🔬 QUIZ NIVEL 2: INTERMEDIO**

### **Pregunta 1 - Closures**
**¿Qué imprime el siguiente código?**
```javascript
function crearContador() {
    let count = 0;
    return function() {
        return ++count;
    };
}

const contador1 = crearContador();
const contador2 = crearContador();
console.log(contador1()); // ?
console.log(contador1()); // ?
console.log(contador2()); // ?
```

A) 1, 2, 3
B) 1, 2, 1 ✅
C) 1, 1, 1
D) Error

**Explicación:** Cada llamada a `crearContador()` crea un nuevo closure con su propia variable `count`.

### **Pregunta 2 - Async/Await**
**¿Cuál es la forma correcta de manejar errores con async/await?**

A) 
```javascript
async function getData() {
    const data = await fetch('/api');
    return data.json();
}
```

B) 
```javascript
async function getData() {
    try {
        const data = await fetch('/api');
        return data.json();
    } catch (error) {
        console.error(error);
    }
}
``` ✅

C) 
```javascript
async function getData() {
    const data = await fetch('/api').catch(console.error);
    return data.json();
}
```

D) Todas son correctas

### **Pregunta 3 - Prototipos**
**¿Qué imprime el siguiente código?**
```javascript
function Animal(name) {
    this.name = name;
}

Animal.prototype.speak = function() {
    return `${this.name} hace ruido`;
};

function Dog(name) {
    Animal.call(this, name);
}

Dog.prototype = Object.create(Animal.prototype);
Dog.prototype.constructor = Dog;

Dog.prototype.speak = function() {
    return `${this.name} ladra`;
};

const perro = new Dog('Rex');
console.log(perro.speak());
```

A) "Rex hace ruido"
B) "Rex ladra" ✅
C) Error
D) undefined

### **Pregunta 4 - Código Práctico**
**Implementa una función que aplique debounce a otra función:**

```javascript
function debounce(func, delay) {
    let timeoutId;
    return function(...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}
```

**¿Esta implementación es correcta?**
A) Sí ✅
B) No, falta el return
C) No, no maneja el contexto correctamente
D) No, no funciona con argumentos

---

## **⚡ QUIZ NIVEL 3: AVANZADO**

### **Pregunta 1 - Event Loop**
**¿En qué orden se ejecutan los siguientes console.log?**
```javascript
console.log('1');

setTimeout(() => console.log('2'), 0);

Promise.resolve().then(() => console.log('3'));

console.log('4');
```

A) 1, 2, 3, 4
B) 1, 4, 2, 3
C) 1, 4, 3, 2 ✅
D) 1, 3, 4, 2

**Explicación:** Las promesas tienen mayor prioridad que los timeouts en el event loop (microtasks vs macrotasks).

### **Pregunta 2 - Proxies**
**¿Qué hace este código?**
```javascript
const obj = new Proxy({}, {
    get(target, prop) {
        return prop in target ? target[prop] : `Propiedad ${prop} no existe`;
    }
});

console.log(obj.nombre);
```

A) Lanza un error
B) Retorna undefined
C) Retorna "Propiedad nombre no existe" ✅
D) Retorna null

### **Pregunta 3 - Generators**
**¿Qué imprime este código?**
```javascript
function* fibonacci() {
    let a = 0, b = 1;
    while (true) {
        yield a;
        [a, b] = [b, a + b];
    }
}

const fib = fibonacci();
console.log(fib.next().value);
console.log(fib.next().value);
console.log(fib.next().value);
```

A) 0, 1, 1 ✅
B) 1, 1, 2
C) 0, 1, 2
D) Error

### **Pregunta 4 - Código Práctico**
**Implementa un sistema de cache con TTL:**

```javascript
class Cache {
    constructor() {
        this.cache = new Map();
        this.timers = new Map();
    }
    
    set(key, value, ttl = 60000) {
        // Limpiar timer existente
        if (this.timers.has(key)) {
            clearTimeout(this.timers.get(key));
        }
        
        // Establecer valor
        this.cache.set(key, value);
        
        // Establecer timer de expiración
        const timer = setTimeout(() => {
            this.cache.delete(key);
            this.timers.delete(key);
        }, ttl);
        
        this.timers.set(key, timer);
    }
    
    get(key) {
        return this.cache.get(key);
    }
    
    has(key) {
        return this.cache.has(key);
    }
    
    delete(key) {
        if (this.timers.has(key)) {
            clearTimeout(this.timers.get(key));
            this.timers.delete(key);
        }
        return this.cache.delete(key);
    }
}
```

**¿Esta implementación es correcta?**
A) Sí ✅
B) No, falta manejo de memoria
C) No, el TTL no funciona
D) No, falta validación

---

## **🚀 QUIZ NIVEL 4: EXPERTO**

### **Pregunta 1 - Memory Management**
**¿Cuál de estos patrones puede causar memory leaks?**

A) 
```javascript
const obj = { data: new Array(1000000) };
obj.self = obj; // Referencia circular ✅
```

B) 
```javascript
const weakMap = new WeakMap();
weakMap.set(obj, data);
```

C) 
```javascript
const arr = [1, 2, 3];
arr.length = 0;
```

D) Ninguno

### **Pregunta 2 - Performance**
**¿Cuál es la complejidad temporal de buscar un elemento en un Map?**

A) O(n)
B) O(log n)
C) O(1) ✅
D) O(n²)

### **Pregunta 3 - Código Práctico Avanzado**
**Implementa un sistema de observables básico:**

```javascript
class Observable {
    constructor(subscribeFn) {
        this._subscribe = subscribeFn;
    }
    
    subscribe(observer) {
        return this._subscribe(observer);
    }
    
    map(transformFn) {
        return new Observable(observer => {
            return this.subscribe({
                next: value => observer.next(transformFn(value)),
                error: error => observer.error(error),
                complete: () => observer.complete()
            });
        });
    }
    
    static of(...values) {
        return new Observable(observer => {
            values.forEach(value => observer.next(value));
            observer.complete();
        });
    }
}
```

**¿Esta implementación básica es funcional?**
A) Sí ✅
B) No, falta error handling
C) No, el map no funciona
D) No, falta unsubscribe

---

## **📊 SISTEMA DE PUNTUACIÓN**

### **Criterios de Evaluación:**

1. **Preguntas de Opción Múltiple:** 2 puntos cada una
2. **Preguntas de Código:** 4 puntos cada una
3. **Bonificación por tiempo:** +5% si se completa en menos del 75% del tiempo

### **Niveles de Certificación:**

- **🥉 Bronce (70-79%):** Fundamentos sólidos
- **🥈 Plata (80-89%):** Competencia intermedia
- **🥇 Oro (90-95%):** Competencia avanzada
- **💎 Diamante (96-100%):** Maestría experta

### **Certificados Especializados:**

1. **JavaScript Fundamentals** - Nivel 1 completado
2. **JavaScript Developer** - Niveles 1-2 completados
3. **JavaScript Expert** - Niveles 1-3 completados
4. **JavaScript Master** - Todos los niveles completados

---

## **🎯 PROYECTOS DE CERTIFICACIÓN**

### **Proyecto Final Nivel 1: Todo App**
- Implementar CRUD básico
- Persistencia en localStorage
- Validación de formularios

### **Proyecto Final Nivel 2: E-commerce Frontend**
- Catálogo de productos
- Carrito de compras
- Filtros y búsqueda

### **Proyecto Final Nivel 3: Chat Application**
- WebSocket communication
- Real-time messaging
- User authentication

### **Proyecto Final Nivel 4: Framework Propio**
- Sistema de componentes
- Virtual DOM básico
- State management
- Routing system

Cada proyecto debe incluir:
- ✅ Código fuente completo
- ✅ Documentación técnica
- ✅ Tests unitarios
- ✅ Demo funcional
- ✅ Presentación de 10 minutos

Este sistema de evaluaciones proporciona una ruta clara de certificación desde principiante hasta experto en JavaScript.
