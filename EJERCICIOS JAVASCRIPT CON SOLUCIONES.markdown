# **EJERCICIOS JAVASCRIPT CON SOLUCIONES**

## **📚 NIVEL PRINCIPIANTE**

### **Ejercicio 1: Manipulación de Arrays**

**Problema:**
Crea una función que tome un array de números y retorne un objeto con las siguientes propiedades:
- `suma`: suma de todos los números
- `promedio`: promedio de los números
- `maximo`: número más grande
- `minimo`: número más pequeño
- `pares`: array con solo números pares
- `impares`: array con solo números impares

```javascript
// Ejemplo de uso:
// analizarNumeros([1, 2, 3, 4, 5, 6]) 
// Debe retornar: {
//   suma: 21,
//   promedio: 3.5,
//   maximo: 6,
//   minimo: 1,
//   pares: [2, 4, 6],
//   impares: [1, 3, 5]
// }
```

**Solución:**
```javascript
function analizarNumeros(numeros) {
    if (!Array.isArray(numeros) || numeros.length === 0) {
        throw new Error('Debe proporcionar un array no vacío de números');
    }
    
    // Validar que todos los elementos sean números
    if (!numeros.every(num => typeof num === 'number' && !isNaN(num))) {
        throw new Error('Todos los elementos deben ser números válidos');
    }
    
    const suma = numeros.reduce((acc, num) => acc + num, 0);
    const promedio = suma / numeros.length;
    const maximo = Math.max(...numeros);
    const minimo = Math.min(...numeros);
    const pares = numeros.filter(num => num % 2 === 0);
    const impares = numeros.filter(num => num % 2 !== 0);
    
    return {
        suma,
        promedio,
        maximo,
        minimo,
        pares,
        impares
    };
}

// Pruebas
console.log(analizarNumeros([1, 2, 3, 4, 5, 6]));
console.log(analizarNumeros([10, -5, 0, 15, -3]));
```

### **Ejercicio 2: Validador de Formulario**

**Problema:**
Crea una clase `ValidadorFormulario` que valide diferentes tipos de campos de formulario. Debe incluir métodos para validar:
- Email
- Contraseña (mínimo 8 caracteres, al menos una mayúscula, una minúscula y un número)
- Teléfono (formato: +XX-XXX-XXX-XXXX)
- Edad (entre 18 y 120 años)

```javascript
// Ejemplo de uso:
// const validador = new ValidadorFormulario();
// validador.validarEmail('<EMAIL>') // true
// validador.validarPassword('MiPass123') // true
```

**Solución:**
```javascript
class ValidadorFormulario {
    constructor() {
        this.errores = [];
    }
    
    validarEmail(email) {
        const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        const esValido = regex.test(email);
        
        if (!esValido) {
            this.errores.push('Email inválido');
        }
        
        return esValido;
    }
    
    validarPassword(password) {
        const criterios = [
            { regex: /.{8,}/, mensaje: 'Mínimo 8 caracteres' },
            { regex: /[A-Z]/, mensaje: 'Al menos una mayúscula' },
            { regex: /[a-z]/, mensaje: 'Al menos una minúscula' },
            { regex: /\d/, mensaje: 'Al menos un número' }
        ];
        
        const erroresPassword = criterios
            .filter(criterio => !criterio.regex.test(password))
            .map(criterio => criterio.mensaje);
        
        if (erroresPassword.length > 0) {
            this.errores.push(`Contraseña inválida: ${erroresPassword.join(', ')}`);
            return false;
        }
        
        return true;
    }
    
    validarTelefono(telefono) {
        const regex = /^\+\d{1,3}-\d{3}-\d{3}-\d{4}$/;
        const esValido = regex.test(telefono);
        
        if (!esValido) {
            this.errores.push('Teléfono inválido (formato: +XX-XXX-XXX-XXXX)');
        }
        
        return esValido;
    }
    
    validarEdad(edad) {
        const edadNum = parseInt(edad);
        const esValido = !isNaN(edadNum) && edadNum >= 18 && edadNum <= 120;
        
        if (!esValido) {
            this.errores.push('Edad debe estar entre 18 y 120 años');
        }
        
        return esValido;
    }
    
    validarFormulario(datos) {
        this.errores = []; // Limpiar errores previos
        
        const resultados = {
            email: this.validarEmail(datos.email),
            password: this.validarPassword(datos.password),
            telefono: this.validarTelefono(datos.telefono),
            edad: this.validarEdad(datos.edad)
        };
        
        const esValido = Object.values(resultados).every(Boolean);
        
        return {
            esValido,
            errores: [...this.errores],
            resultados
        };
    }
    
    obtenerErrores() {
        return [...this.errores];
    }
    
    limpiarErrores() {
        this.errores = [];
    }
}

// Pruebas
const validador = new ValidadorFormulario();

const datosFormulario = {
    email: '<EMAIL>',
    password: 'MiPassword123',
    telefono: '+34-************',
    edad: '25'
};

const resultado = validador.validarFormulario(datosFormulario);
console.log('Validación:', resultado);

// Caso con errores
const datosInvalidos = {
    email: 'email-invalido',
    password: '123',
    telefono: '123456789',
    edad: '15'
};

const resultadoInvalido = validador.validarFormulario(datosInvalidos);
console.log('Validación con errores:', resultadoInvalido);
```

## **📊 NIVEL INTERMEDIO**

### **Ejercicio 3: Sistema de Gestión de Tareas**

**Problema:**
Implementa un sistema de gestión de tareas con las siguientes características:
- Crear, editar, eliminar y listar tareas
- Marcar tareas como completadas
- Filtrar tareas por estado (pendientes, completadas, todas)
- Buscar tareas por título o descripción
- Persistir datos en localStorage

```javascript
// Ejemplo de uso:
// const gestor = new GestorTareas();
// gestor.crearTarea('Estudiar JavaScript', 'Completar ejercicios del curso');
// gestor.listarTareas('pendientes');
```

**Solución:**
```javascript
class Tarea {
    constructor(titulo, descripcion = '', prioridad = 'media') {
        this.id = Date.now() + Math.random();
        this.titulo = titulo;
        this.descripcion = descripcion;
        this.prioridad = prioridad; // 'alta', 'media', 'baja'
        this.completada = false;
        this.fechaCreacion = new Date();
        this.fechaCompletada = null;
    }
    
    completar() {
        this.completada = true;
        this.fechaCompletada = new Date();
    }
    
    descompletar() {
        this.completada = false;
        this.fechaCompletada = null;
    }
    
    editar(nuevosDatos) {
        Object.assign(this, nuevosDatos);
    }
    
    toJSON() {
        return {
            id: this.id,
            titulo: this.titulo,
            descripcion: this.descripcion,
            prioridad: this.prioridad,
            completada: this.completada,
            fechaCreacion: this.fechaCreacion,
            fechaCompletada: this.fechaCompletada
        };
    }
    
    static fromJSON(data) {
        const tarea = new Tarea(data.titulo, data.descripcion, data.prioridad);
        Object.assign(tarea, data);
        tarea.fechaCreacion = new Date(data.fechaCreacion);
        tarea.fechaCompletada = data.fechaCompletada ? new Date(data.fechaCompletada) : null;
        return tarea;
    }
}

class GestorTareas {
    constructor() {
        this.tareas = new Map();
        this.cargarDatos();
    }
    
    crearTarea(titulo, descripcion = '', prioridad = 'media') {
        if (!titulo.trim()) {
            throw new Error('El título es requerido');
        }
        
        const tarea = new Tarea(titulo.trim(), descripcion.trim(), prioridad);
        this.tareas.set(tarea.id, tarea);
        this.guardarDatos();
        
        return tarea;
    }
    
    obtenerTarea(id) {
        const tarea = this.tareas.get(id);
        if (!tarea) {
            throw new Error('Tarea no encontrada');
        }
        return tarea;
    }
    
    editarTarea(id, nuevosDatos) {
        const tarea = this.obtenerTarea(id);
        
        // Validar datos
        if (nuevosDatos.titulo !== undefined && !nuevosDatos.titulo.trim()) {
            throw new Error('El título no puede estar vacío');
        }
        
        tarea.editar(nuevosDatos);
        this.guardarDatos();
        
        return tarea;
    }
    
    eliminarTarea(id) {
        if (!this.tareas.has(id)) {
            throw new Error('Tarea no encontrada');
        }
        
        const eliminada = this.tareas.delete(id);
        this.guardarDatos();
        
        return eliminada;
    }
    
    completarTarea(id) {
        const tarea = this.obtenerTarea(id);
        tarea.completar();
        this.guardarDatos();
        
        return tarea;
    }
    
    descompletarTarea(id) {
        const tarea = this.obtenerTarea(id);
        tarea.descompletar();
        this.guardarDatos();
        
        return tarea;
    }
    
    listarTareas(filtro = 'todas') {
        let tareas = Array.from(this.tareas.values());
        
        switch (filtro) {
            case 'pendientes':
                tareas = tareas.filter(t => !t.completada);
                break;
            case 'completadas':
                tareas = tareas.filter(t => t.completada);
                break;
            case 'alta':
            case 'media':
            case 'baja':
                tareas = tareas.filter(t => t.prioridad === filtro);
                break;
        }
        
        // Ordenar por prioridad y fecha
        const prioridadOrden = { alta: 3, media: 2, baja: 1 };
        
        return tareas.sort((a, b) => {
            if (a.completada !== b.completada) {
                return a.completada ? 1 : -1; // Pendientes primero
            }
            
            if (a.prioridad !== b.prioridad) {
                return prioridadOrden[b.prioridad] - prioridadOrden[a.prioridad];
            }
            
            return new Date(a.fechaCreacion) - new Date(b.fechaCreacion);
        });
    }
    
    buscarTareas(termino) {
        const terminoLower = termino.toLowerCase();
        
        return Array.from(this.tareas.values()).filter(tarea =>
            tarea.titulo.toLowerCase().includes(terminoLower) ||
            tarea.descripcion.toLowerCase().includes(terminoLower)
        );
    }
    
    obtenerEstadisticas() {
        const tareas = Array.from(this.tareas.values());
        
        return {
            total: tareas.length,
            completadas: tareas.filter(t => t.completada).length,
            pendientes: tareas.filter(t => !t.completada).length,
            porPrioridad: {
                alta: tareas.filter(t => t.prioridad === 'alta').length,
                media: tareas.filter(t => t.prioridad === 'media').length,
                baja: tareas.filter(t => t.prioridad === 'baja').length
            }
        };
    }
    
    guardarDatos() {
        const datos = Array.from(this.tareas.values()).map(t => t.toJSON());
        localStorage.setItem('tareas', JSON.stringify(datos));
    }
    
    cargarDatos() {
        try {
            const datos = localStorage.getItem('tareas');
            if (datos) {
                const tareasArray = JSON.parse(datos);
                tareasArray.forEach(data => {
                    const tarea = Tarea.fromJSON(data);
                    this.tareas.set(tarea.id, tarea);
                });
            }
        } catch (error) {
            console.error('Error cargando datos:', error);
        }
    }
    
    exportarDatos() {
        return JSON.stringify(Array.from(this.tareas.values()).map(t => t.toJSON()), null, 2);
    }
    
    importarDatos(datosJSON) {
        try {
            const datos = JSON.parse(datosJSON);
            this.tareas.clear();
            
            datos.forEach(data => {
                const tarea = Tarea.fromJSON(data);
                this.tareas.set(tarea.id, tarea);
            });
            
            this.guardarDatos();
            return true;
        } catch (error) {
            console.error('Error importando datos:', error);
            return false;
        }
    }
}

// Pruebas del sistema
const gestor = new GestorTareas();

// Crear tareas
const tarea1 = gestor.crearTarea('Estudiar JavaScript', 'Completar ejercicios del curso', 'alta');
const tarea2 = gestor.crearTarea('Hacer compras', 'Comprar ingredientes para la cena', 'media');
const tarea3 = gestor.crearTarea('Ejercicio', 'Ir al gimnasio', 'baja');

console.log('Tareas creadas:', gestor.listarTareas());

// Completar una tarea
gestor.completarTarea(tarea2.id);

// Buscar tareas
console.log('Búsqueda "JavaScript":', gestor.buscarTareas('JavaScript'));

// Estadísticas
console.log('Estadísticas:', gestor.obtenerEstadisticas());

// Filtrar tareas pendientes
console.log('Tareas pendientes:', gestor.listarTareas('pendientes'));
```

Estos ejercicios cubren conceptos fundamentales e intermedios de JavaScript, proporcionando problemas prácticos que los estudiantes encontrarán en el desarrollo real de aplicaciones.
