# **GLOSARIO COMPLETO DE JAVASCRIPT**

## **A**

**Abstract Class** - Clase que no puede ser instanciada directamente y está diseñada para ser heredada por otras clases.

**AJAX (Asynchronous JavaScript and XML)** - Técnica para realizar peticiones HTTP asíncronas sin recargar la página.

**API (Application Programming Interface)** - Conjunto de definiciones y protocolos para construir e integrar software de aplicaciones.

**Arrow Function** - Función expresada con sintaxis `=>` que no tiene su propio `this`, `arguments`, `super` o `new.target`.

**Async/Await** - Sintaxis para trabajar con código asíncrono de manera más legible, basada en Promises.

**Asynchronous** - Operaciones que no bloquean la ejecución del código y pueden completarse en el futuro.

## **B**

**Babel** - Transpilador que convierte código JavaScript moderno a versiones compatibles con navegadores antiguos.

**Binding** - Proceso de asociar un valor a una variable o función en un contexto específico.

**Block Scope** - Ámbito de variables declaradas con `let` o `const` dentro de un bloque `{}`.

**Boolean** - Tipo de dato primitivo que puede ser `true` o `false`.

**Bundler** - Herramienta que combina múltiples archivos JavaScript en uno o pocos archivos optimizados.

## **C**

**Callback** - Función que se pasa como argumento a otra función para ser ejecutada posteriormente.

**Closure** - Función que tiene acceso a variables de su ámbito exterior incluso después de que la función exterior haya terminado.

**CommonJS** - Sistema de módulos usado principalmente en Node.js con `require()` y `module.exports`.

**Constructor** - Función especial usada para crear e inicializar objetos.

**CORS (Cross-Origin Resource Sharing)** - Mecanismo que permite recursos restringidos en una página web desde otro dominio.

**CSS-in-JS** - Técnica de escribir CSS dentro de JavaScript.

## **D**

**Debouncing** - Técnica para limitar la frecuencia de ejecución de una función.

**Destructuring** - Sintaxis para extraer valores de arrays u objetos en variables distintas.

**DOM (Document Object Model)** - Representación en memoria de la estructura de un documento HTML.

**Duck Typing** - Concepto donde el tipo de un objeto se determina por los métodos y propiedades que posee.

## **E**

**ECMAScript (ES)** - Estándar en el que se basa JavaScript.

**Event Bubbling** - Propagación de eventos desde el elemento hijo hacia el padre.

**Event Delegation** - Técnica de manejar eventos en un elemento padre en lugar de elementos hijos.

**Event Loop** - Mecanismo que permite a JavaScript realizar operaciones no bloqueantes.

**Expression** - Código que produce un valor.

## **F**

**Fetch API** - API moderna para realizar peticiones HTTP que reemplaza XMLHttpRequest.

**First-Class Function** - Funciones que pueden ser tratadas como valores (asignadas, pasadas como argumentos, etc.).

**Function Declaration** - Declaración de función con la palabra clave `function`.

**Function Expression** - Función definida dentro de una expresión.

**Functional Programming** - Paradigma de programación basado en funciones puras y inmutabilidad.

## **G**

**Garbage Collection** - Proceso automático de liberación de memoria no utilizada.

**Generator** - Función que puede pausar y reanudar su ejecución, definida con `function*`.

**Global Scope** - Ámbito más amplio donde las variables son accesibles desde cualquier parte del código.

**GraphQL** - Lenguaje de consulta y runtime para APIs.

## **H**

**Higher-Order Function** - Función que toma otras funciones como argumentos o retorna una función.

**Hoisting** - Comportamiento donde declaraciones de variables y funciones se mueven al inicio de su ámbito.

**HTTP** - Protocolo de transferencia de hipertexto usado para comunicación web.

## **I**

**IIFE (Immediately Invoked Function Expression)** - Función que se ejecuta inmediatamente después de ser definida.

**Immutability** - Principio donde los objetos no pueden ser modificados después de su creación.

**Inheritance** - Mecanismo donde una clase puede heredar propiedades y métodos de otra clase.

**Iterator** - Objeto que define una secuencia y potencialmente un valor de retorno al terminar.

## **J**

**JSON (JavaScript Object Notation)** - Formato de intercambio de datos basado en texto.

**JSX** - Extensión de sintaxis para JavaScript que permite escribir elementos HTML en JavaScript.

**JWT (JSON Web Token)** - Estándar para transmitir información de forma segura entre partes.

## **L**

**Lexical Scope** - Ámbito determinado por donde las variables y funciones son declaradas en el código.

**Library** - Colección de funciones y objetos predefinidos que pueden ser utilizados en aplicaciones.

**Linting** - Proceso de análisis estático de código para encontrar errores y problemas de estilo.

## **M**

**Map** - Estructura de datos que almacena pares clave-valor donde las claves pueden ser de cualquier tipo.

**Method** - Función que es propiedad de un objeto.

**Module** - Archivo que exporta funcionalidad para ser usada en otros archivos.

**Mutable** - Objeto que puede ser modificado después de su creación.

## **N**

**Node.js** - Runtime de JavaScript que permite ejecutar JavaScript fuera del navegador.

**NPM (Node Package Manager)** - Gestor de paquetes para JavaScript.

**Null** - Valor primitivo que representa la ausencia intencional de cualquier valor de objeto.

## **O**

**Object** - Tipo de dato que representa una colección de propiedades.

**OOP (Object-Oriented Programming)** - Paradigma de programación basado en objetos.

**Operator** - Símbolo que realiza operaciones en operandos.

## **P**

**Polyfill** - Código que implementa una característica en navegadores que no la soportan nativamente.

**Promise** - Objeto que representa la eventual finalización o falla de una operación asíncrona.

**Prototype** - Mecanismo por el cual los objetos JavaScript heredan características de otros objetos.

**Pure Function** - Función que siempre retorna el mismo resultado para los mismos argumentos y no tiene efectos secundarios.

## **R**

**React** - Biblioteca de JavaScript para construir interfaces de usuario.

**Recursion** - Técnica donde una función se llama a sí misma.

**REST API** - Arquitectura para servicios web que usa HTTP para comunicación.

**Runtime** - Entorno donde se ejecuta el código JavaScript.

## **S**

**Scope** - Contexto donde las variables y funciones son accesibles.

**Set** - Estructura de datos que almacena valores únicos de cualquier tipo.

**SPA (Single Page Application)** - Aplicación web que carga una sola página HTML y actualiza dinámicamente el contenido.

**Statement** - Instrucción que realiza una acción.

**Symbol** - Tipo de dato primitivo único e inmutable usado como identificador de propiedades de objeto.

## **T**

**Template Literal** - Cadena de texto que permite expresiones embebidas usando backticks (`).

**This** - Palabra clave que se refiere al objeto en el contexto actual.

**Throttling** - Técnica para limitar la frecuencia de ejecución de una función.

**Transpiler** - Herramienta que convierte código de un lenguaje a otro del mismo nivel.

**TypeScript** - Superset de JavaScript que añade tipado estático.

## **U**

**Undefined** - Valor primitivo asignado automáticamente a variables declaradas pero no inicializadas.

**Unicode** - Estándar de codificación de caracteres.

**URL (Uniform Resource Locator)** - Dirección de un recurso en la web.

## **V**

**Variable** - Contenedor para almacenar valores de datos.

**Virtual DOM** - Representación en memoria del DOM real mantenida en memoria y sincronizada con el DOM real.

**Vue.js** - Framework progresivo de JavaScript para construir interfaces de usuario.

## **W**

**Webpack** - Bundler de módulos estáticos para aplicaciones JavaScript modernas.

**Web API** - APIs proporcionadas por el navegador web.

**WebSocket** - Protocolo de comunicación que proporciona canales de comunicación full-duplex.

## **X**

**XMLHttpRequest** - API que proporciona funcionalidad para transferir datos entre un cliente y servidor.

**XSS (Cross-Site Scripting)** - Vulnerabilidad de seguridad web que permite inyectar scripts maliciosos.

## **Y**

**Yarn** - Gestor de paquetes alternativo a NPM.

**Yield** - Palabra clave usada en funciones generadoras para pausar y reanudar la ejecución.

## **Z**

**Zero-based Indexing** - Sistema de numeración donde el primer elemento tiene índice 0.
