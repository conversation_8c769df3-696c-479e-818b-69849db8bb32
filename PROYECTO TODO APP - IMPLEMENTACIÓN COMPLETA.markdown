# **PROYECTO: TODO APP AVANZADA - IMPLEMENTACIÓN COMPLETA**

## **📋 DESCRIPCIÓN DEL PROYECTO**

Una aplicación de gestión de tareas completa con las siguientes características:
- ✅ CRUD completo de tareas
- 🏷️ Categorías y etiquetas
- 🔍 Búsqueda y filtros avanzados
- 💾 Persistencia en localStorage
- 📱 Diseño responsive
- 🌙 Modo oscuro/claro
- 📊 Estadísticas y reportes
- 🔔 Notificaciones
- 📤 Exportar/Importar datos

## **🏗️ ESTRUCTURA DEL PROYECTO**

```
todo-app/
├── index.html
├── css/
│   └── styles.css
├── js/
│   ├── models/
│   │   ├── Task.js
│   │   └── Category.js
│   ├── services/
│   │   ├── StorageService.js
│   │   └── NotificationService.js
│   ├── components/
│   │   ├── TaskList.js
│   │   ├── TaskForm.js
│   │   └── FilterPanel.js
│   ├── utils/
│   │   └── helpers.js
│   └── app.js
└── README.md
```

## **📄 HTML - index.html**

```html
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo App Avanzada</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container" id="app">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1><i class="fas fa-tasks"></i> Todo App</h1>
                <div class="header-controls">
                    <button id="theme-toggle" class="btn btn-icon" title="Cambiar tema">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button id="export-btn" class="btn btn-icon" title="Exportar datos">
                        <i class="fas fa-download"></i>
                    </button>
                    <input type="file" id="import-input" accept=".json" style="display: none;">
                    <button id="import-btn" class="btn btn-icon" title="Importar datos">
                        <i class="fas fa-upload"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="stats-panel">
                    <h3>Estadísticas</h3>
                    <div class="stat-item">
                        <span class="stat-label">Total:</span>
                        <span class="stat-value" id="total-tasks">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Pendientes:</span>
                        <span class="stat-value" id="pending-tasks">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Completadas:</span>
                        <span class="stat-value" id="completed-tasks">0</span>
                    </div>
                </div>

                <div class="filter-panel">
                    <h3>Filtros</h3>
                    
                    <div class="filter-group">
                        <label>Estado:</label>
                        <select id="status-filter">
                            <option value="all">Todas</option>
                            <option value="pending">Pendientes</option>
                            <option value="completed">Completadas</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label>Prioridad:</label>
                        <select id="priority-filter">
                            <option value="all">Todas</option>
                            <option value="high">Alta</option>
                            <option value="medium">Media</option>
                            <option value="low">Baja</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label>Categoría:</label>
                        <select id="category-filter">
                            <option value="all">Todas</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label>Búsqueda:</label>
                        <input type="text" id="search-input" placeholder="Buscar tareas...">
                    </div>

                    <button id="clear-filters" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Limpiar filtros
                    </button>
                </div>
            </aside>

            <!-- Content Area -->
            <div class="content-area">
                <!-- Task Form -->
                <section class="task-form-section">
                    <h2>Nueva Tarea</h2>
                    <form id="task-form" class="task-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="task-title">Título *</label>
                                <input type="text" id="task-title" required maxlength="100">
                            </div>
                            <div class="form-group">
                                <label for="task-priority">Prioridad</label>
                                <select id="task-priority">
                                    <option value="low">Baja</option>
                                    <option value="medium" selected>Media</option>
                                    <option value="high">Alta</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="task-description">Descripción</label>
                            <textarea id="task-description" rows="3" maxlength="500"></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="task-category">Categoría</label>
                                <select id="task-category">
                                    <option value="">Sin categoría</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="task-due-date">Fecha límite</label>
                                <input type="date" id="task-due-date">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="task-tags">Etiquetas (separadas por comas)</label>
                            <input type="text" id="task-tags" placeholder="trabajo, urgente, personal">
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Agregar Tarea
                            </button>
                            <button type="button" id="cancel-edit" class="btn btn-secondary" style="display: none;">
                                <i class="fas fa-times"></i> Cancelar
                            </button>
                        </div>
                    </form>
                </section>

                <!-- Task List -->
                <section class="task-list-section">
                    <div class="section-header">
                        <h2>Mis Tareas</h2>
                        <div class="list-controls">
                            <select id="sort-select">
                                <option value="created">Fecha de creación</option>
                                <option value="priority">Prioridad</option>
                                <option value="dueDate">Fecha límite</option>
                                <option value="title">Título</option>
                            </select>
                            <button id="sort-order" class="btn btn-icon" title="Cambiar orden">
                                <i class="fas fa-sort-amount-down"></i>
                            </button>
                        </div>
                    </div>

                    <div id="task-list" class="task-list">
                        <div class="empty-state" id="empty-state">
                            <i class="fas fa-clipboard-list"></i>
                            <h3>No hay tareas</h3>
                            <p>Agrega tu primera tarea para comenzar</p>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Modal para confirmaciones -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Confirmar</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p id="modal-message">¿Estás seguro?</p>
            </div>
            <div class="modal-footer">
                <button id="modal-confirm" class="btn btn-danger">Confirmar</button>
                <button id="modal-cancel" class="btn btn-secondary">Cancelar</button>
            </div>
        </div>
    </div>

    <!-- Toast para notificaciones -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Scripts -->
    <script type="module" src="js/app.js"></script>
</body>
</html>
```

## **🎨 CSS - styles.css (Parte 1)**

```css
/* Variables CSS */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.2s ease;
}

/* Tema oscuro */
[data-theme="dark"] {
    --background-color: #111827;
    --surface-color: #1f2937;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --border-color: #374151;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
}

/* Reset y base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    transition: var(--transition);
}

/* Layout principal */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.app-header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content h1 {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 700;
}

.header-controls {
    display: flex;
    gap: 0.5rem;
}

.main-content {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
}

/* Sidebar */
.sidebar {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.stats-panel,
.filter-panel {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.stats-panel h3,
.filter-panel h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: var(--text-secondary);
}

.stat-value {
    font-weight: 600;
    color: var(--primary-color);
}

/* Filtros */
.filter-group {
    margin-bottom: 1rem;
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.filter-group input,
.filter-group select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--surface-color);
    color: var(--text-primary);
    transition: var(--transition);
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Botones */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-icon {
    padding: 0.5rem;
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-icon:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

/* Formularios */
.task-form-section {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
}

.task-form-section h2 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--surface-color);
    color: var(--text-primary);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Lista de tareas */
.task-list-section {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    color: var(--text-primary);
}

.list-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.task-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

/* Responsive */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        padding: 1rem;
    }
    
    .header-content {
        padding: 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
}
```

Este es el comienzo de la implementación completa del proyecto Todo App. El código incluye una estructura HTML semántica, estilos CSS modernos con soporte para tema oscuro, y está preparado para la funcionalidad JavaScript que implementaremos en los siguientes archivos.
