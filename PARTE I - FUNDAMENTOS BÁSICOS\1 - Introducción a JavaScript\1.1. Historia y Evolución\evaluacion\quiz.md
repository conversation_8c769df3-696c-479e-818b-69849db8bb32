# Quiz - Historia y Evolución de JavaScript

## **📋 Información del Quiz**
- **Preguntas:** 15
- **Tiempo límite:** 20 minutos
- **Puntuación mínima:** 80% (12/15 correctas)
- **Intentos permitidos:** 3

---

## **📝 Preguntas**

### **Pregunta 1**
**¿En qué año fue creado JavaScript y quién fue su creador?**

a) 1994, <PERSON>-Lee  
b) 1995, <PERSON>  
c) 1996, <PERSON>  
d) 1997, <PERSON>  

*Respuesta correcta: **b***

---

### **Pregunta 2**
**¿Cuánto tiempo le tomó a Brendan <PERSON>ich crear el primer prototipo de JavaScript?**

a) 1 mes  
b) 3 semanas  
c) 10 días  
d) 6 meses  

*Respuesta correcta: **c***

---

### **Pregunta 3**
**¿Cuáles fueron los nombres que tuvo JavaScript antes de su nombre final?**

a) LiveScript y WebScript  
b) Mocha y LiveScript  
c) NetScript y Mocha  
d) WebScript y NetScript  

*Respuesta correcta: **b***

---

### **Pregunta 4**
**¿Qué organización es responsable de mantener el estándar ECMAScript?**

a) W3C (World Wide Web Consortium)  
b) Mozilla Foundation  
c) ECMA International  
d) JavaScript Foundation  

*Respuesta correcta: **c***

---

### **Pregunta 5**
**¿En qué año se lanzó ECMAScript 3, que introdujo características importantes como try/catch?**

a) 1997  
b) 1998  
c) 1999  
d) 2000  

*Respuesta correcta: **c***

---

### **Pregunta 6**
**¿Por qué fue abandonado ECMAScript 4?**

a) Falta de financiamiento  
b) Era demasiado complejo y controvertido  
c) Los navegadores no lo soportaban  
d) Se decidió esperar a ES5  

*Respuesta correcta: **b***

---

### **Pregunta 7**
**¿Cuál fue la característica más importante introducida en ECMAScript 5 (2009)?**

a) Arrow functions  
b) Classes  
c) Strict mode  
d) Promises  

*Respuesta correcta: **c***

---

### **Pregunta 8**
**¿Quién creó Node.js y en qué año?**

a) Ryan Dahl, 2009  
b) Brendan Eich, 2008  
c) Douglas Crockford, 2010  
d) John Resig, 2009  

*Respuesta correcta: **a***

---

### **Pregunta 9**
**¿Qué cambio importante ocurrió en la nomenclatura a partir de ECMAScript 2015?**

a) Se cambió el nombre a JavaScript  
b) Se adoptaron releases anuales  
c) Se eliminó el número de versión  
d) Se cambió la organización responsable  

*Respuesta correcta: **b***

---

### **Pregunta 10**
**¿Cuáles son las características principales introducidas en ES6/ES2015?**

a) async/await y Promises  
b) let/const, arrow functions, classes, modules  
c) BigInt y optional chaining  
d) Array.includes() y operador exponencial  

*Respuesta correcta: **b***

---

### **Pregunta 11**
**¿En qué versión de ECMAScript se introdujo async/await?**

a) ES2015 (ES6)  
b) ES2016 (ES7)  
c) ES2017 (ES8)  
d) ES2018 (ES9)  

*Respuesta correcta: **c***

---

### **Pregunta 12**
**¿Qué características introdujo ES2020?**

a) Classes y modules  
b) BigInt, nullish coalescing (??) y optional chaining (?.)  
c) async/await y Object.values  
d) Array.flat() y Object.fromEntries  

*Respuesta correcta: **b***

---

### **Pregunta 13**
**¿Cuál es el proceso de 5 etapas que sigue TC39 para las propuestas de ECMAScript?**

a) Idea → Propuesta → Borrador → Candidato → Finalizado  
b) Stage 0 → Stage 1 → Stage 2 → Stage 3 → Stage 4  
c) Ambas respuestas son correctas  
d) Ninguna de las anteriores  

*Respuesta correcta: **c***

---

### **Pregunta 14**
**¿Cuál fue el impacto principal de Node.js en el ecosistema JavaScript?**

a) Mejoró la velocidad de los navegadores  
b) Permitió usar JavaScript en el servidor  
c) Introdujo nuevas características del lenguaje  
d) Creó el primer framework de JavaScript  

*Respuesta correcta: **b***

---

### **Pregunta 15**
**¿Qué herramienta revolucionó la distribución de paquetes JavaScript junto con Node.js?**

a) Bower  
b) Yarn  
c) NPM (Node Package Manager)  
d) PNPM  

*Respuesta correcta: **c***

---

## **🎯 Respuestas Correctas**

1. **b** - 1995, Brendan Eich
2. **c** - 10 días
3. **b** - Mocha y LiveScript
4. **c** - ECMA International
5. **c** - 1999
6. **b** - Era demasiado complejo y controvertido
7. **c** - Strict mode
8. **a** - Ryan Dahl, 2009
9. **b** - Se adoptaron releases anuales
10. **b** - let/const, arrow functions, classes, modules
11. **c** - ES2017 (ES8)
12. **b** - BigInt, nullish coalescing (??) y optional chaining (?.)
13. **c** - Ambas respuestas son correctas
14. **b** - Permitió usar JavaScript en el servidor
15. **c** - NPM (Node Package Manager)

---

## **📊 Evaluación**

### **Excelente (14-15 correctas)**
¡Felicitaciones! Tienes un conocimiento excepcional de la historia de JavaScript. Estás listo para avanzar al siguiente tema.

### **Bueno (12-13 correctas)**
Buen trabajo. Tienes una comprensión sólida de la historia de JavaScript. Revisa los temas donde tuviste dudas.

### **Necesita Mejora (9-11 correctas)**
Tienes conocimientos básicos, pero necesitas repasar algunos conceptos importantes. Te recomendamos revisar el material teórico.

### **Insuficiente (menos de 9 correctas)**
Necesitas estudiar más el material antes de continuar. Revisa la teoría y los ejemplos, luego intenta el quiz nuevamente.

---

## **📚 Recursos para Repasar**

Si no alcanzaste la puntuación mínima, revisa estos recursos:

1. **Teoría completa**: [contenido/teoria.md](../contenido/teoria.md)
2. **Ejemplos prácticos**: [ejemplos/ejemplo-basico.js](../ejemplos/ejemplo-basico.js)
3. **Timeline interactivo**: [ejercicios/ejercicio-1.js](../ejercicios/ejercicio-1.js)

### **Enlaces Externos Recomendados**
- [MDN - Historia de JavaScript](https://developer.mozilla.org/en-US/docs/Web/JavaScript)
- [ECMAScript Specifications](https://www.ecma-international.org/publications-and-standards/standards/ecma-262/)
- [JavaScript Timeline](https://auth0.com/blog/a-brief-history-of-javascript/)

---

## **⏱️ Instrucciones para Tomar el Quiz**

1. **Preparación**: Asegúrate de haber estudiado todo el material del tema
2. **Tiempo**: Tienes 20 minutos para completar las 15 preguntas
3. **Intentos**: Puedes intentar hasta 3 veces
4. **Puntuación**: Necesitas al menos 12 respuestas correctas (80%)
5. **Recursos**: No puedes consultar material durante el quiz
6. **Honestidad**: Responde basándote en tu conocimiento real

---

**¡Buena suerte con tu quiz! 🍀**
