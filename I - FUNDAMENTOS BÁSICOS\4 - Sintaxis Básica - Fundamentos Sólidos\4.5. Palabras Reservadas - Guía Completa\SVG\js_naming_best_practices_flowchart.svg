<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="processGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#388E3C;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="decisionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F57C00;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="resultGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7B1FA2;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- Title -->
  <rect x="10" y="10" width="980" height="50" fill="url(#headerGradient)" rx="5" filter="url(#shadow)"/>
  <text x="500" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">
    Mejores Prácticas de Naming en JavaScript
  </text>
  
  <!-- Start: Element to Name -->
  <rect x="400" y="80" width="200" height="40" fill="url(#processGradient)" rx="5" filter="url(#shadow)"/>
  <text x="500" y="105" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Elemento a Nombrar
  </text>
  
  <!-- Arrow 1 -->
  <line x1="500" y1="120" x2="500" y2="140" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Decision: Type of Element -->
  <polygon points="500,140 580,170 500,200 420,170" fill="url(#decisionGradient)" filter="url(#shadow)"/>
  <text x="500" y="175" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Tipo de Elemento?
  </text>
  
  <!-- Branches -->
  <!-- Variable/Function -->
  <line x1="420" y1="170" x2="150" y2="170" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <rect x="50" y="150" width="100" height="40" fill="url(#processGradient)" rx="5" filter="url(#shadow)"/>
  <text x="100" y="175" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    camelCase
  </text>
  <text x="100" y="210" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="10">
    userName, calculateTotal
  </text>
  
  <!-- Class/Constructor -->
  <line x1="450" y1="140" x2="300" y2="100" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <rect x="200" y="80" width="100" height="40" fill="url(#processGradient)" rx="5" filter="url(#shadow)"/>
  <text x="250" y="105" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    PascalCase
  </text>
  <text x="250" y="140" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="10">
    UserAccount, ApiClient
  </text>
  
  <!-- Global Constant -->
  <line x1="550" y1="140" x2="700" y2="100" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <rect x="650" y="80" width="120" height="40" fill="url(#processGradient)" rx="5" filter="url(#shadow)"/>
  <text x="710" y="105" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    UPPER_SNAKE_CASE
  </text>
  <text x="710" y="140" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="10">
    MAX_RETRY, API_URL
  </text>
  
  <!-- Private/Internal -->
  <line x1="580" y1="170" x2="850" y2="170" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <rect x="800" y="150" width="100" height="40" fill="url(#processGradient)" rx="5" filter="url(#shadow)"/>
  <text x="850" y="175" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    _prefijo
  </text>
  <text x="850" y="210" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="10">
    _generateId, _internal
  </text>
  
  <!-- Convergence to Descriptive -->
  <line x1="100" y1="190" x2="400" y2="280" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="250" y1="120" x2="400" y2="280" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="710" y1="120" x2="400" y2="280" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="850" y1="190" x2="400" y2="280" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Descriptive and Clear -->
  <rect x="350" y="280" width="150" height="40" fill="url(#processGradient)" rx="5" filter="url(#shadow)"/>
  <text x="425" y="305" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Descriptivo y Claro
  </text>
  
  <!-- Arrow to Boolean Check -->
  <line x1="425" y1="320" x2="425" y2="340" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Boolean Check -->
  <polygon points="425,340 505,370 425,400 345,370" fill="url(#decisionGradient)" filter="url(#shadow)"/>
  <text x="425" y="375" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    ¿Es Boolean?
  </text>
  
  <!-- Boolean Yes -->
  <line x1="345" y1="370" x2="200" y2="370" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="270" y="365" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="10">Sí</text>
  <rect x="100" y="350" width="100" height="40" fill="url(#resultGradient)" rx="5" filter="url(#shadow)"/>
  <text x="150" y="375" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    is/has/can +
  </text>
  <text x="150" y="405" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="10">
    isActive, hasPermission
  </text>
  
  <!-- Boolean No -->
  <line x1="505" y1="370" x2="650" y2="370" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="580" y="365" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="10">No</text>
  <rect x="650" y="350" width="120" height="40" fill="url(#resultGradient)" rx="5" filter="url(#shadow)"/>
  <text x="710" y="375" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Sustantivo/Verbo
  </text>
  <text x="710" y="405" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="10">
    userName, calculatePrice
  </text>
  
  <!-- Convergence to Validation -->
  <line x1="150" y1="390" x2="425" y2="450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="710" y1="390" x2="425" y2="450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Validation -->
  <rect x="350" y="450" width="150" height="40" fill="url(#processGradient)" rx="5" filter="url(#shadow)"/>
  <text x="425" y="475" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Validar Convenciones
  </text>
  
  <!-- Arrow to Standards Check -->
  <line x1="425" y1="490" x2="425" y2="510" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Standards Check -->
  <polygon points="425,510 505,540 425,570 345,540" fill="url(#decisionGradient)" filter="url(#shadow)"/>
  <text x="425" y="545" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    ¿Cumple Estándares?
  </text>
  
  <!-- Standards No -->
  <line x1="345" y1="540" x2="150" y2="540" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="245" y="535" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="10">No</text>
  <rect x="50" y="520" width="100" height="40" fill="#f44336" rx="5" filter="url(#shadow)"/>
  <text x="100" y="545" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Revisar y Ajustar
  </text>
  
  <!-- Loop back -->
  <line x1="50" y1="540" x2="30" y2="540" stroke="#333" stroke-width="2"/>
  <line x1="30" y1="540" x2="30" y2="100" stroke="#333" stroke-width="2"/>
  <line x1="30" y1="100" x2="400" y2="100" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Standards Yes -->
  <line x1="505" y1="540" x2="650" y2="540" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="580" y="535" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="10">Sí</text>
  <rect x="650" y="520" width="100" height="40" fill="url(#processGradient)" rx="5" filter="url(#shadow)"/>
  <text x="700" y="545" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Implementar
  </text>
  
  <!-- Arrow to Document -->
  <line x1="700" y1="560" x2="700" y2="580" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Document -->
  <rect x="650" y="580" width="100" height="40" fill="url(#processGradient)" rx="5" filter="url(#shadow)"/>
  <text x="700" y="605" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Documentar
  </text>
  
  <!-- Arrow to Code Review -->
  <line x1="700" y1="620" x2="700" y2="640" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Code Review -->
  <rect x="650" y="640" width="100" height="40" fill="url(#processGradient)" rx="5" filter="url(#shadow)"/>
  <text x="700" y="665" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Code Review
  </text>
  
  <!-- Arrow to Final Result -->
  <line x1="700" y1="680" x2="700" y2="700" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Final Result -->
  <rect x="620" y="700" width="160" height="40" fill="url(#resultGradient)" rx="5" filter="url(#shadow)"/>
  <text x="700" y="725" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Código Mantenible
  </text>
  
  <!-- Best Practices Box -->
  <rect x="20" y="600" width="300" height="180" fill="#f5f5f5" stroke="#ddd" stroke-width="2" rx="5"/>
  <text x="170" y="620" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Mejores Prácticas
  </text>
  
  <text x="30" y="640" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • Variables: camelCase (userName)
  </text>
  <text x="30" y="655" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • Clases: PascalCase (UserAccount)
  </text>
  <text x="30" y="670" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • Constantes: UPPER_SNAKE_CASE
  </text>
  <text x="30" y="685" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • Privados: _prefijo (_generateId)
  </text>
  <text x="30" y="700" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • Booleanos: is/has/can (isActive)
  </text>
  <text x="30" y="715" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • Funciones: verbo + objeto
  </text>
  <text x="30" y="730" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • Evitar abreviaciones excesivas
  </text>
  <text x="30" y="745" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • Usar nombres descriptivos
  </text>
  <text x="30" y="760" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • Mantener consistencia
  </text>
  
</svg>