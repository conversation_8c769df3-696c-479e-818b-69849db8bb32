## 4.3.5. Linting rules para semicolons

### Introducción
Linting rules en ESLint controlan uso de semicolons.  
Reglas como 'semi' enforce o disallow ;.  
Previene inconsistencias.  
Integra con CI/CD.  
Personaliza por proyecto.  
¿Configuras linting para semicolons?

### Código de Ejemplo
```javascript
// Con regla semi: 'error'
let a = 1;  // OK
let b = 2   // Error: Missing semicolon

// Con semi: ['error', 'never']
let c = 3
let d = 4;  // Error: Unexpected semicolon

// no-extra-semi
let e = 5;;  // Error: Extra semicolon

// semi-style
let f = 6
;function g() {}  // Error: Semicolon style

// semi-spacing
let h = 7 ;  // Error: Space before semicolon
```
### Resultados en Consola
- Errores de lint para código inválido
- No output runtime, pero previene bugs
- Warnings en editor
- Fallos en build si enforced

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> ConfigRule["Paso 1: Configurar 'semi' <br> - Enforce/Disallow"]
    ConfigRule --> CheckCode["Paso 2: Lint código <br> - Verificar ;"]
    CheckCode -->|Missing| ErrorMiss["Paso 3: Error missing ; <br> - Si enforced"]
    CheckCode -->|Extra| ErrorExtra["Paso 4: Error extra ; <br> - no-extra-semi"]
    ErrorExtra --> StyleCheck["Paso 5: Verificar estilo <br> - semi-style"]
    StyleCheck --> SpacingCheck["Paso 6: Verificar spacing <br> - semi-spacing"]
    SpacingCheck --> Fix["Paso 7: Auto-fix si posible <br> - eslint --fix"]
    Fix --> Pass["Paso 8: Lint pasa <br> - Código consistente"]
    Pass --> Fin["Paso 9: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Customize["Personalizar rules"]
    CheckCode --> NotaSemi["Nota: 'always' o 'never'"]
    ErrorExtra --> NotaExtra["Nota: Evita extras"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ConfigRule fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CheckCode fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ErrorMiss fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ErrorExtra fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style StyleCheck fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style SpacingCheck fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fix fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Pass fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Customize fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaSemi fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaExtra fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> ESLintConfig["Config ESLint <br> - .eslintrc"]
        ESLintConfig --> Rules["Rules <br> - semi, no-extra-semi"]
        Rules --> StyleRules["Style <br> - semi-style, semi-spacing"]
        StyleRules --> RunLint["Run eslint <br> - Check código"]
        RunLint --> Errors["Detect errors <br> - Missing/extra ;"]
        Errors --> FixErrors["Fix <br> - Manual o auto"]
        FixErrors --> ConsistentCode["Código consistente <br> - Sin issues"]
        Inicio --> Desarrollador(("Desarrollador")) --> Integrate["Integrar en workflow"]
        Rules --> NotaRules["Nota: Personalizables"]
        RunLint --> NotaRun["Nota: En editor o CLI"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ESLintConfig fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Rules fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style StyleRules fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style RunLint fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Errors fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style FixErrors fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ConsistentCode fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Integrate fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaRules fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaRun fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Enforce semicolons en proyectos.
2. Disallow en estilos sin ;.
3. Integración con VS Code.
4. Pre-commit hooks.
5. CI pipelines para quality.

### Errores comunes
1. Ignorar lint warnings.
2. Config inconsistente.
3. No usar auto-fix.
4. Mezclar rules en repos.
5. No documentar config.

### Recomendaciones
1. Elige 'always' o 'never'.
2. Usa Prettier con ESLint.
3. Configura para equipo.
4. Run lint regularmente.
5. Educa sobre benefits.