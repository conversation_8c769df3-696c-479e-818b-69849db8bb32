## 4.2.4. Function declarations vs expressions

### Introducción
Las function declarations y expressions en JavaScript difieren en hoisting y uso.  
Declarations son hoisted, permitiendo llamadas antes de definición.  
Expressions no lo son, y suelen asignarse a variables.  
Esto impacta el orden del código.  
Declarations tienen nombre implícito, expressions pueden ser anónimas.  
¿ Prefieres declarations o expressions para funciones?

### Código de Ejemplo
```javascript
// Function declaration
console.log(sumar(1, 2)); // Funciona por hoisting
function sumar(a, b) {
    return a + b;
}

// Function expression
// console.log(restar(3, 1)); // Error, no hoisted
const restar = function(a, b) {
    return a - b;
};
console.log(restar(3, 1));

// Arrow function expression
const multiplicar = (a, b) => a * b;
console.log(multiplicar(2, 3));
```
### Resultados en Consola
- `3`
- `2`
- `6`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> CallDecl["Paso 1: console.log(sumar(1, 2)) <br> - Llamada antes de declaration"]
    CallDecl --> Decl["Paso 2: function sumar(a, b) <br> - Declaration hoisted"]
    Decl --> Comment["Paso 3: // console.log(restar(3, 1)) <br> - Error si llamado"]
    Comment --> Expr["Paso 4: const restar = function(a, b) <br> - Expression no hoisted"]
    Expr --> CallExpr["Paso 5: console.log(restar(3, 1)) <br> - Resultado: 2"]
    CallExpr --> Arrow["Paso 6: const multiplicar = (a, b) => a * b <br> - Arrow expression"]
    Arrow --> CallArrow["Paso 7: console.log(multiplicar(2, 3)) <br> - Resultado: 6"]
    CallArrow --> Fin["Paso 8: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Diferencias["Diferencias"]
    Decl --> NotaDecl["Nota: Hoisted"]
    Expr --> NotaExpr["Nota: Asignada"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CallDecl fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Decl fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Comment fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:12px
    style Expr fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CallExpr fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Arrow fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CallArrow fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Diferencias fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaDecl fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaExpr fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Declaration["Declaration <br> - Hoisted, named"]
        Declaration --> UsoDecl["Uso: Llamadas tempranas"]
        Inicio --> Expression["Expression <br> - No hoisted, assignable"]
        Expression --> UsoExpr["Uso: Anónimas, en variables"]
        UsoDecl --> Diferencia["Diferencia: Orden de código"]
        UsoExpr --> Diferencia
        Inicio --> Desarrollador(("Desarrollador")) --> Eleccion["Elección"]
        Declaration --> NotaDecl["Nota: Function keyword"]
        Expression --> NotaExpr["Nota: Arrow o function"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Declaration fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style UsoDecl fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Expression fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style UsoExpr fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Diferencia fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Eleccion fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaDecl fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaExpr fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Usar declarations para funciones top-level.
2. Expressions para callbacks anónimos.
3. Arrow expressions para concisión.
4. Declarations en módulos para exports.
5. Expressions en objetos o arrays.

### Errores comunes
1. Llamar expressions antes de asignación.
2. Olvidar nombre en declarations.
3. Confundir hoisting con var.
4. Usar expressions sin asignar.
5. Ignorar this en arrows vs functions.

### Recomendaciones
1. Usa declarations para hoisting necesario.
2. Prefiere arrows para simplicidad.
3. Asigna expressions a const.
4. Entiende scope y hoisting.
5. Prueba ambos en contextos diferentes.