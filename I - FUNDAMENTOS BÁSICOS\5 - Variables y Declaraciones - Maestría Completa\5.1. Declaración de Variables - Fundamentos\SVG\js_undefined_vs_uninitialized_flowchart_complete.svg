<svg xmlns="http://www.w3.org/2000/svg" width="900" height="1200" viewBox="0 0 900 1200">
  <!-- Definición de estilos y gradientes -->
  <defs>
    <!-- Gradientes para los nodos -->
    <linearGradient id="gradientStart" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="gradientError" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#F44336;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C62828;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="gradientUndefined" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FFC107;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFA000;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="gradientTDZ" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1565C0;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="gradientNull" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6A1B9A;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="gradientValue" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
    
    <!-- Estilos de flechas -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    
    <!-- Estilos de texto -->
    <style>
      .title { font-family: Arial; font-size: 24px; font-weight: bold; fill: #333; }
      .subtitle { font-family: Arial; font-size: 18px; font-weight: bold; fill: #333; }
      .node-title { font-family: Arial; font-size: 14px; font-weight: bold; fill: white; }
      .node-subtitle { font-family: Arial; font-size: 12px; fill: white; }
      .code { font-family: 'Courier New'; font-size: 12px; fill: #333; }
      .code-output { font-family: 'Courier New'; font-size: 12px; fill: #555; }
      .note { font-family: Arial; font-size: 12px; font-style: italic; fill: #666; }
      .legend-text { font-family: Arial; font-size: 12px; fill: #333; }
      .arrow { stroke: #333; stroke-width: 2px; fill: none; marker-end: url(#arrowhead); }
      .dashed-arrow { stroke: #666; stroke-width: 1.5px; stroke-dasharray: 5,3; fill: none; marker-end: url(#arrowhead); }
    </style>
  </defs>
  
  <!-- Título principal -->
  <text x="450" y="40" text-anchor="middle" class="title">Flujo Completo: Undefined vs. No Inicializada en JavaScript</text>
  
  <!-- Leyenda de colores -->
  <rect x="50" y="60" width="800" height="60" rx="5" ry="5" fill="#f5f5f5" stroke="#ccc" stroke-width="1" />
  <text x="450" y="80" text-anchor="middle" class="subtitle">Leyenda</text>
  
  <!-- Elementos de la leyenda -->
  <rect x="70" y="90" width="20" height="20" rx="3" ry="3" fill="url(#gradientError)" />
  <text x="100" y="105" class="legend-text">Error (ReferenceError, SyntaxError)</text>
  
  <rect x="270" y="90" width="20" height="20" rx="3" ry="3" fill="url(#gradientUndefined)" />
  <text x="300" y="105" class="legend-text">undefined</text>
  
  <rect x="400" y="90" width="20" height="20" rx="3" ry="3" fill="url(#gradientValue)" />
  <text x="430" y="105" class="legend-text">Valor inicializado</text>
  
  <rect x="570" y="90" width="20" height="20" rx="3" ry="3" fill="url(#gradientTDZ)" />
  <text x="600" y="105" class="legend-text">Temporal Dead Zone</text>
  
  <rect x="740" y="90" width="20" height="20" rx="3" ry="3" fill="url(#gradientNull)" />
  <text x="770" y="105" class="legend-text">null</text>
  
  <!-- Inicio del diagrama de flujo -->
  <rect x="400" y="140" width="100" height="50" rx="10" ry="10" fill="url(#gradientStart)" />
  <text x="450" y="165" text-anchor="middle" class="node-title">Inicio</text>
  <text x="450" y="185" text-anchor="middle" class="node-subtitle">del programa</text>
  
  <!-- Sección 1: Variable No Declarada -->
  <rect x="150" y="240" width="200" height="60" rx="10" ry="10" fill="url(#gradientError)" />
  <text x="250" y="265" text-anchor="middle" class="node-title">Variable No Declarada</text>
  <text x="250" y="285" text-anchor="middle" class="node-subtitle">variableNoDeclarada</text>
  
  <rect x="150" y="330" width="200" height="40" rx="5" ry="5" fill="#f5f5f5" stroke="#ccc" />
  <text x="250" y="355" text-anchor="middle" class="code">ReferenceError: not defined</text>
  
  <!-- Sección 2: Variable Declarada con var -->
  <rect x="450" y="240" width="200" height="60" rx="10" ry="10" fill="url(#gradientUndefined)" />
  <text x="550" y="265" text-anchor="middle" class="node-title">Variable con var</text>
  <text x="550" y="285" text-anchor="middle" class="node-subtitle">var varVariable;</text>
  
  <rect x="450" y="330" width="200" height="40" rx="5" ry="5" fill="#f5f5f5" stroke="#ccc" />
  <text x="550" y="355" text-anchor="middle" class="code">undefined (hoisting)</text>
  
  <!-- Sección 3: Variable Declarada con let (TDZ) -->
  <rect x="700" y="240" width="200" height="60" rx="10" ry="10" fill="url(#gradientTDZ)" />
  <text x="800" y="265" text-anchor="middle" class="node-title">Variable con let</text>
  <text x="800" y="285" text-anchor="middle" class="node-subtitle">let letVariable;</text>
  
  <rect x="700" y="330" width="200" height="40" rx="5" ry="5" fill="#f5f5f5" stroke="#ccc" />
  <text x="800" y="355" text-anchor="middle" class="code">ReferenceError (TDZ)</text>
  
  <!-- Sección 4: Comparación de Estados -->
  <rect x="150" y="420" width="200" height="60" rx="10" ry="10" fill="url(#gradientUndefined)" />
  <text x="250" y="445" text-anchor="middle" class="node-title">Variable sin inicializar</text>
  <text x="250" y="465" text-anchor="middle" class="node-subtitle">let sinInicializar;</text>
  
  <rect x="450" y="420" width="200" height="60" rx="10" ry="10" fill="url(#gradientUndefined)" />
  <text x="550" y="445" text-anchor="middle" class="node-title">Variable con undefined</text>
  <text x="550" y="465" text-anchor="middle" class="node-subtitle">let conUndefined = undefined;</text>
  
  <rect x="300" y="510" width="200" height="40" rx="5" ry="5" fill="#f5f5f5" stroke="#ccc" />
  <text x="400" y="535" text-anchor="middle" class="code">sinInicializar === conUndefined (true)</text>
  
  <!-- Sección 5: Temporal Dead Zone Detallada -->
  <rect x="150" y="600" width="200" height="60" rx="10" ry="10" fill="url(#gradientTDZ)" />
  <text x="250" y="625" text-anchor="middle" class="node-title">Temporal Dead Zone</text>
  <text x="250" y="645" text-anchor="middle" class="node-subtitle">function demonstrateTDZ()</text>
  
  <rect x="150" y="690" width="200" height="60" rx="5" ry="5" fill="#f5f5f5" stroke="#ccc" />
  <text x="250" y="715" text-anchor="middle" class="code">ReferenceError al acceder</text>
  <text x="250" y="735" text-anchor="middle" class="code">ReferenceError con typeof</text>
  
  <rect x="450" y="600" width="200" height="60" rx="10" ry="10" fill="url(#gradientValue)" />
  <text x="550" y="625" text-anchor="middle" class="node-title">Después de declaración</text>
  <text x="550" y="645" text-anchor="middle" class="node-subtitle">let variableEnTDZ = "valor";</text>
  
  <rect x="450" y="690" width="200" height="40" rx="5" ry="5" fill="#f5f5f5" stroke="#ccc" />
  <text x="550" y="715" text-anchor="middle" class="code">Acceso normal al valor</text>
  
  <!-- Sección 6: Verificación de Existencia -->
  <rect x="150" y="800" width="200" height="60" rx="10" ry="10" fill="#f5f5f5" stroke="#333" />
  <text x="250" y="825" text-anchor="middle" class="subtitle" style="fill: #333;">Verificación</text>
  <text x="250" y="845" text-anchor="middle" class="code">isVariableDeclared()</text>
  
  <rect x="450" y="800" width="200" height="60" rx="10" ry="10" fill="#f5f5f5" stroke="#333" />
  <text x="550" y="825" text-anchor="middle" class="subtitle" style="fill: #333;">Inicialización</text>
  <text x="550" y="845" text-anchor="middle" class="code">isVariableInitialized()</text>
  
  <!-- Sección 7: Patrones de Inicialización Segura -->
  <rect x="150" y="910" width="200" height="60" rx="10" ry="10" fill="url(#gradientUndefined)" />
  <text x="250" y="935" text-anchor="middle" class="node-title">Operador ||</text>
  <text x="250" y="955" text-anchor="middle" class="node-subtitle">value || "default"</text>
  
  <rect x="450" y="910" width="200" height="60" rx="10" ry="10" fill="url(#gradientNull)" />
  <text x="550" y="935" text-anchor="middle" class="node-title">Operador ??</text>
  <text x="550" y="955" text-anchor="middle" class="node-subtitle">value ?? "default"</text>
  
  <rect x="700" y="910" width="200" height="60" rx="10" ry="10" fill="#f5f5f5" stroke="#333" />
  <text x="800" y="935" text-anchor="middle" class="subtitle" style="fill: #333;">Verificación explícita</text>
  <text x="800" y="955" text-anchor="middle" class="code">value !== undefined && value !== null</text>
  
  <!-- Sección 8: Debugging de Estados -->
  <rect x="300" y="1020" width="300" height="60" rx="10" ry="10" fill="#f5f5f5" stroke="#333" />
  <text x="450" y="1045" text-anchor="middle" class="subtitle" style="fill: #333;">Debugging de Estados</text>
  <text x="450" y="1065" text-anchor="middle" class="code">debugVariableState()</text>
  
  <rect x="300" y="1110" width="300" height="60" rx="5" ry="5" fill="#f5f5f5" stroke="#ccc" />
  <text x="450" y="1135" text-anchor="middle" class="code">Análisis de undefined, null, falsy, nullish</text>
  <text x="450" y="1155" text-anchor="middle" class="code">Comparación de comportamientos</text>
  
  <!-- Flechas de conexión -->
  <!-- Desde Inicio a las primeras secciones -->
  <path d="M 450,190 L 450,220 L 250,220 L 250,240" class="arrow" />
  <path d="M 450,190 L 450,220 L 550,220 L 550,240" class="arrow" />
  <path d="M 450,190 L 450,220 L 800,220 L 800,240" class="arrow" />
  
  <!-- Conexiones de Variable No Declarada -->
  <path d="M 250,370 L 250,400 L 250,420" class="arrow" />
  
  <!-- Conexiones de Variable con var -->
  <path d="M 550,370 L 550,400 L 550,420" class="arrow" />
  
  <!-- Conexiones de Variable con let -->
  <path d="M 800,370 L 800,400 L 650,400 L 650,450" class="dashed-arrow" />
  
  <!-- Conexiones de Comparación de Estados -->
  <path d="M 250,480 L 250,510 L 300,510" class="arrow" />
  <path d="M 550,480 L 550,510 L 500,510" class="arrow" />
  
  <!-- Conexiones a TDZ -->
  <path d="M 400,550 L 400,580 L 250,580 L 250,600" class="arrow" />
  <path d="M 400,550 L 400,580 L 550,580 L 550,600" class="arrow" />
  
  <!-- Conexiones de TDZ -->
  <path d="M 250,750 L 250,780 L 250,800" class="arrow" />
  <path d="M 550,730 L 550,780 L 550,800" class="arrow" />
  
  <!-- Conexiones de Verificación -->
  <path d="M 250,860 L 250,890 L 250,910" class="arrow" />
  <path d="M 550,860 L 550,890 L 550,910" class="arrow" />
  <path d="M 550,860 L 550,890 L 800,890 L 800,910" class="arrow" />
  
  <!-- Conexiones a Debugging -->
  <path d="M 250,970 L 250,1000 L 450,1000 L 450,1020" class="arrow" />
  <path d="M 550,970 L 550,1000 L 450,1000 L 450,1020" class="arrow" />
  <path d="M 800,970 L 800,1000 L 450,1000 L 450,1020" class="arrow" />
  
  <!-- Conexión final -->
  <path d="M 450,1170 L 450,1190" class="arrow" />
  
  <!-- Nota de copyright -->
  <text x="450" y="1190" text-anchor="middle" class="note">© JavaScript Avanzado - Variables y Declaraciones - Maestría Completa</text>
</svg>