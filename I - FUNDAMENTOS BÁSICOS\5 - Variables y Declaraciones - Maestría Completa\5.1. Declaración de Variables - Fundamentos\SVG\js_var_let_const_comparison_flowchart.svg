<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="varGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFB6C1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF69B4;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="letGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#98FB98;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#32CD32;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="constGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4682B4;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bestPracticeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFA500;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="2" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Title -->
  <text x="500" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2C3E50">var, let y const - Análisis Profundo</text>
  
  <!-- var Section -->
  <g>
    <rect x="50" y="60" width="280" height="200" rx="10" fill="url(#varGradient)" stroke="#FF1493" stroke-width="2" filter="url(#shadow)"/>
    <text x="190" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#8B0000">var (ES5)</text>
    
    <text x="70" y="110" font-family="Arial, sans-serif" font-size="14" fill="#8B0000">✓ Function Scoped</text>
    <text x="70" y="130" font-family="Arial, sans-serif" font-size="14" fill="#8B0000">✓ Hoisted (undefined)</text>
    <text x="70" y="150" font-family="Arial, sans-serif" font-size="14" fill="#8B0000">✓ Redeclarable</text>
    <text x="70" y="170" font-family="Arial, sans-serif" font-size="14" fill="#8B0000">✓ Global Object Binding</text>
    <text x="70" y="190" font-family="Arial, sans-serif" font-size="14" fill="#8B0000">✗ No Temporal Dead Zone</text>
    <text x="70" y="210" font-family="Arial, sans-serif" font-size="14" fill="#8B0000">✗ Closure Issues</text>
    <text x="70" y="230" font-family="Arial, sans-serif" font-size="14" fill="#8B0000">⚠ Legacy - Avoid</text>
  </g>
  
  <!-- let Section -->
  <g>
    <rect x="360" y="60" width="280" height="200" rx="10" fill="url(#letGradient)" stroke="#228B22" stroke-width="2" filter="url(#shadow)"/>
    <text x="500" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#006400">let (ES6)</text>
    
    <text x="380" y="110" font-family="Arial, sans-serif" font-size="14" fill="#006400">✓ Block Scoped</text>
    <text x="380" y="130" font-family="Arial, sans-serif" font-size="14" fill="#006400">✓ Temporal Dead Zone</text>
    <text x="380" y="150" font-family="Arial, sans-serif" font-size="14" fill="#006400">✓ Not Redeclarable</text>
    <text x="380" y="170" font-family="Arial, sans-serif" font-size="14" fill="#006400">✓ No Global Binding</text>
    <text x="380" y="190" font-family="Arial, sans-serif" font-size="14" fill="#006400">✓ Reassignable</text>
    <text x="380" y="210" font-family="Arial, sans-serif" font-size="14" fill="#006400">✓ Loop Closure Fix</text>
    <text x="380" y="230" font-family="Arial, sans-serif" font-size="14" fill="#006400">✓ Modern Standard</text>
  </g>
  
  <!-- const Section -->
  <g>
    <rect x="670" y="60" width="280" height="200" rx="10" fill="url(#constGradient)" stroke="#4169E1" stroke-width="2" filter="url(#shadow)"/>
    <text x="810" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#191970">const (ES6)</text>
    
    <text x="690" y="110" font-family="Arial, sans-serif" font-size="14" fill="#191970">✓ Block Scoped</text>
    <text x="690" y="130" font-family="Arial, sans-serif" font-size="14" fill="#191970">✓ Temporal Dead Zone</text>
    <text x="690" y="150" font-family="Arial, sans-serif" font-size="14" fill="#191970">✓ Not Redeclarable</text>
    <text x="690" y="170" font-family="Arial, sans-serif" font-size="14" fill="#191970">✓ No Global Binding</text>
    <text x="690" y="190" font-family="Arial, sans-serif" font-size="14" fill="#191970">✓ Immutable Binding</text>
    <text x="690" y="210" font-family="Arial, sans-serif" font-size="14" fill="#191970">✓ Object Mutation OK</text>
    <text x="690" y="230" font-family="Arial, sans-serif" font-size="14" fill="#191970">✓ Preferred Choice</text>
  </g>
  
  <!-- Comparison Flow -->
  <g>
    <rect x="50" y="300" width="900" height="120" rx="10" fill="#F8F9FA" stroke="#6C757D" stroke-width="2" filter="url(#shadow)"/>
    <text x="500" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#495057">Comparación de Comportamiento</text>
    
    <!-- Hoisting -->
    <text x="70" y="350" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#495057">Hoisting:</text>
    <text x="150" y="350" font-family="Arial, sans-serif" font-size="12" fill="#FF1493">var: undefined</text>
    <text x="250" y="350" font-family="Arial, sans-serif" font-size="12" fill="#228B22">let: TDZ</text>
    <text x="320" y="350" font-family="Arial, sans-serif" font-size="12" fill="#4169E1">const: TDZ</text>
    
    <!-- Scope -->
    <text x="70" y="370" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#495057">Scope:</text>
    <text x="150" y="370" font-family="Arial, sans-serif" font-size="12" fill="#FF1493">var: function</text>
    <text x="250" y="370" font-family="Arial, sans-serif" font-size="12" fill="#228B22">let: block</text>
    <text x="320" y="370" font-family="Arial, sans-serif" font-size="12" fill="#4169E1">const: block</text>
    
    <!-- Reassignment -->
    <text x="450" y="350" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#495057">Reasignación:</text>
    <text x="560" y="350" font-family="Arial, sans-serif" font-size="12" fill="#FF1493">var: ✓</text>
    <text x="620" y="350" font-family="Arial, sans-serif" font-size="12" fill="#228B22">let: ✓</text>
    <text x="680" y="350" font-family="Arial, sans-serif" font-size="12" fill="#4169E1">const: ✗</text>
    
    <!-- Redeclaration -->
    <text x="450" y="370" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#495057">Redeclaración:</text>
    <text x="560" y="370" font-family="Arial, sans-serif" font-size="12" fill="#FF1493">var: ✓</text>
    <text x="620" y="370" font-family="Arial, sans-serif" font-size="12" fill="#228B22">let: ✗</text>
    <text x="680" y="370" font-family="Arial, sans-serif" font-size="12" fill="#4169E1">const: ✗</text>
    
    <!-- Global Binding -->
    <text x="750" y="350" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#495057">Global:</text>
    <text x="810" y="350" font-family="Arial, sans-serif" font-size="12" fill="#FF1493">var: window</text>
    <text x="750" y="370" font-family="Arial, sans-serif" font-size="12" fill="#228B22">let: no binding</text>
    <text x="750" y="390" font-family="Arial, sans-serif" font-size="12" fill="#4169E1">const: no binding</text>
  </g>
  
  <!-- Best Practices -->
  <g>
    <rect x="200" y="450" width="600" height="150" rx="10" fill="url(#bestPracticeGradient)" stroke="#FF8C00" stroke-width="2" filter="url(#shadow)"/>
    <text x="500" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#8B4513">Mejores Prácticas</text>
    
    <text x="220" y="500" font-family="Arial, sans-serif" font-size="14" fill="#8B4513">1. Usa const por defecto</text>
    <text x="220" y="520" font-family="Arial, sans-serif" font-size="14" fill="#8B4513">2. let solo cuando necesites reasignación</text>
    <text x="220" y="540" font-family="Arial, sans-serif" font-size="14" fill="#8B4513">3. Evita var en código moderno</text>
    <text x="220" y="560" font-family="Arial, sans-serif" font-size="14" fill="#8B4513">4. Configura ESLint para enforcer reglas</text>
    <text x="220" y="580" font-family="Arial, sans-serif" font-size="14" fill="#8B4513">5. Migra var a let/const gradualmente</text>
  </g>
  
  <!-- Code Examples -->
  <g>
    <rect x="50" y="630" width="900" height="120" rx="10" fill="#2C3E50" stroke="#34495E" stroke-width="2" filter="url(#shadow)"/>
    <text x="500" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#ECF0F1">Ejemplos de Código</text>
    
    <text x="70" y="680" font-family="Courier New, monospace" font-size="12" fill="#E74C3C">var x = 1; var x = 2; // OK</text>
    <text x="70" y="700" font-family="Courier New, monospace" font-size="12" fill="#27AE60">let y = 1; y = 2; // OK</text>
    <text x="70" y="720" font-family="Courier New, monospace" font-size="12" fill="#3498DB">const z = 1; // z = 2; Error</text>
    
    <text x="400" y="680" font-family="Courier New, monospace" font-size="12" fill="#E74C3C">for(var i=0; i&lt;3; i++) // closure issue</text>
    <text x="400" y="700" font-family="Courier New, monospace" font-size="12" fill="#27AE60">for(let i=0; i&lt;3; i++) // block scoped</text>
    <text x="400" y="720" font-family="Courier New, monospace" font-size="12" fill="#3498DB">const obj = {}; obj.prop = 'OK';</text>
  </g>
  
  <!-- Arrows showing evolution -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#7F8C8D" />
    </marker>
  </defs>
  
  <line x1="330" y1="160" x2="360" y2="160" stroke="#7F8C8D" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="345" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#7F8C8D">ES6</text>
  
  <line x1="640" y1="160" x2="670" y2="160" stroke="#7F8C8D" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="655" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#7F8C8D">ES6</text>
</svg>