<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .code { font-family: 'Courier New', monospace; font-size: 11px; fill: #e74c3c; }
      .global-this { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .object-this { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .explicit-this { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .arrow-this { fill: #f39c12; stroke: #d68910; stroke-width: 2; }
      .constructor-this { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .this-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title">JavaScript 'this' Binding y Context - Análisis Completo</text>
  
  <!-- This Binding Rules -->
  <rect x="50" y="60" width="1300" height="120" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
  <text x="700" y="85" text-anchor="middle" class="subtitle">🎯 THIS BINDING RULES - Priority Order (High to Low)</text>
  
  <rect x="70" y="110" width="240" height="50" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="5"/>
  <text x="190" y="130" text-anchor="middle" class="text">1. new Binding</text>
  <text x="190" y="145" text-anchor="middle" class="small-text">Highest Priority</text>
  
  <rect x="330" y="110" width="240" height="50" fill="#f39c12" stroke="#d68910" stroke-width="2" rx="5"/>
  <text x="450" y="130" text-anchor="middle" class="text">2. Explicit Binding</text>
  <text x="450" y="145" text-anchor="middle" class="small-text">call, apply, bind</text>
  
  <rect x="590" y="110" width="240" height="50" fill="#27ae60" stroke="#229954" stroke-width="2" rx="5"/>
  <text x="710" y="130" text-anchor="middle" class="text">3. Implicit Binding</text>
  <text x="710" y="145" text-anchor="middle" class="small-text">Object method call</text>
  
  <rect x="850" y="110" width="240" height="50" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="5"/>
  <text x="970" y="130" text-anchor="middle" class="text">4. Default Binding</text>
  <text x="970" y="145" text-anchor="middle" class="small-text">Global/undefined</text>
  
  <rect x="1110" y="110" width="240" height="50" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="5"/>
  <text x="1230" y="130" text-anchor="middle" class="text">5. Arrow Functions</text>
  <text x="1230" y="145" text-anchor="middle" class="small-text">Lexical this</text>
  
  <!-- Global Context -->
  <rect x="50" y="200" width="650" height="200" class="global-this" rx="10" opacity="0.4"/>
  <text x="375" y="225" text-anchor="middle" class="subtitle">🌍 GLOBAL CONTEXT (Default Binding)</text>
  
  <rect x="70" y="250" width="300" height="130" fill="#ffffff" stroke="#2980b9" stroke-width="2" rx="5"/>
  <text x="220" y="275" text-anchor="middle" class="subtitle">Non-Strict Mode</text>
  <text x="80" y="295" class="code">function globalFunction() {</text>
  <text x="90" y="310" class="code">console.log(this); // window (browser)</text>
  <text x="90" y="325" class="code">console.log(this === window); // true</text>
  <text x="90" y="340" class="code">this.globalVar = "I'm global";</text>
  <text x="80" y="355" class="code">}</text>
  <text x="80" y="370" class="code">globalFunction();</text>
  
  <rect x="380" y="250" width="300" height="130" fill="#ffffff" stroke="#2980b9" stroke-width="2" rx="5"/>
  <text x="530" y="275" text-anchor="middle" class="subtitle">Strict Mode</text>
  <text x="390" y="295" class="code">"use strict";</text>
  <text x="390" y="310" class="code">function strictFunction() {</text>
  <text x="400" y="325" class="code">console.log(this); // undefined</text>
  <text x="400" y="340" class="code">// this.prop = "error"; // TypeError</text>
  <text x="390" y="355" class="code">}</text>
  <text x="390" y="370" class="code">strictFunction();</text>
  
  <!-- Object Method Context -->
  <rect x="750" y="200" width="600" height="200" class="object-this" rx="10" opacity="0.4"/>
  <text x="1050" y="225" text-anchor="middle" class="subtitle">📦 OBJECT METHOD CONTEXT (Implicit Binding)</text>
  
  <rect x="770" y="250" width="560" height="130" fill="#ffffff" stroke="#229954" stroke-width="2" rx="5"/>
  <text x="1050" y="275" text-anchor="middle" class="subtitle">Method Invocation</text>
  <text x="780" y="295" class="code">const person = {</text>
  <text x="790" y="310" class="code">name: "John",</text>
  <text x="790" y="325" class="code">greet: function() {</text>
  <text x="800" y="340" class="code">console.log(this.name); // "John" - this refers to person</text>
  <text x="800" y="355" class="code">console.log(this === person); // true</text>
  <text x="790" y="370" class="code">}</text>
  <text x="780" y="385" class="code">};</text>
  <text x="780" y="400" class="code">person.greet(); // Implicit binding</text>
  
  <!-- Explicit Binding -->
  <rect x="50" y="430" width="1300" height="200" class="explicit-this" rx="10" opacity="0.4"/>
  <text x="700" y="455" text-anchor="middle" class="subtitle">🎯 EXPLICIT BINDING (call, apply, bind)</text>
  
  <!-- call method -->
  <rect x="70" y="480" width="300" height="130" fill="#ffffff" stroke="#c0392b" stroke-width="2" rx="5"/>
  <text x="220" y="505" text-anchor="middle" class="subtitle">📞 call()</text>
  <text x="80" y="525" class="code">function introduce(age, city) {</text>
  <text x="90" y="540" class="code">console.log(`I'm ${this.name},`)</text>
  <text x="90" y="555" class="code">console.log(`${age} years old`)</text>
  <text x="90" y="570" class="code">console.log(`from ${city}`)</text>
  <text x="80" y="585" class="code">}</text>
  <text x="80" y="600" class="code">introduce.call(person, 30, "NYC")</text>
  
  <!-- apply method -->
  <rect x="390" y="480" width="300" height="130" fill="#ffffff" stroke="#c0392b" stroke-width="2" rx="5"/>
  <text x="540" y="505" text-anchor="middle" class="subtitle">📋 apply()</text>
  <text x="400" y="525" class="code">const args = [25, "LA"];</text>
  <text x="400" y="540" class="code">introduce.apply(person, args);</text>
  <text x="400" y="560" class="text">Key Difference:</text>
  <text x="410" y="575" class="code">call: individual arguments</text>
  <text x="410" y="590" class="code">apply: array of arguments</text>
  <text x="400" y="605" class="small-text">Mnemonic: Apply = Array</text>
  
  <!-- bind method -->
  <rect x="710" y="480" width="300" height="130" fill="#ffffff" stroke="#c0392b" stroke-width="2" rx="5"/>
  <text x="860" y="505" text-anchor="middle" class="subtitle">🔗 bind()</text>
  <text x="720" y="525" class="code">const boundIntroduce = </text>
  <text x="730" y="540" class="code">introduce.bind(person, 35);</text>
  <text x="720" y="560" class="code">boundIntroduce("Miami");</text>
  <text x="720" y="580" class="text">Creates new function with:</text>
  <text x="730" y="595" class="code">• Fixed 'this' context</text>
  <text x="730" y="610" class="code">• Partial application support</text>
  
  <!-- Hard Binding -->
  <rect x="1030" y="480" width="300" height="130" fill="#ffffff" stroke="#c0392b" stroke-width="2" rx="5"/>
  <text x="1180" y="505" text-anchor="middle" class="subtitle">🔒 Hard Binding</text>
  <text x="1040" y="525" class="code">function hardBind(fn, obj) {</text>
  <text x="1050" y="540" class="code">return function() {</text>
  <text x="1060" y="555" class="code">return fn.apply(obj, arguments);</text>
  <text x="1050" y="570" class="code">};</text>
  <text x="1040" y="585" class="code">}</text>
  <text x="1040" y="600" class="code">const hardBound = hardBind(fn, obj);</text>
  
  <!-- Constructor Binding -->
  <rect x="50" y="660" width="650" height="200" class="constructor-this" rx="10" opacity="0.4"/>
  <text x="375" y="685" text-anchor="middle" class="subtitle">🏗️ CONSTRUCTOR BINDING (new)</text>
  
  <rect x="70" y="710" width="280" height="130" fill="#ffffff" stroke="#8e44ad" stroke-width="2" rx="5"/>
  <text x="210" y="735" text-anchor="middle" class="subtitle">Constructor Function</text>
  <text x="80" y="755" class="code">function Person(name, age) {</text>
  <text x="90" y="770" class="code">// this = {} (new empty object)</text>
  <text x="90" y="785" class="code">this.name = name;</text>
  <text x="90" y="800" class="code">this.age = age;</text>
  <text x="90" y="815" class="code">// return this (implicit)</text>
  <text x="80" y="830" class="code">}</text>
  
  <rect x="370" y="710" width="310" height="130" fill="#ffffff" stroke="#8e44ad" stroke-width="2" rx="5"/>
  <text x="525" y="735" text-anchor="middle" class="subtitle">new Operator Steps</text>
  <text x="380" y="755" class="text">1. Create new empty object {}</text>
  <text x="380" y="770" class="text">2. Set object's [[Prototype]]</text>
  <text x="380" y="785" class="text">3. Bind 'this' to new object</text>
  <text x="380" y="800" class="text">4. Execute constructor function</text>
  <text x="380" y="815" class="text">5. Return object (or explicit return)</text>
  <text x="380" y="835" class="code">const john = new Person("John", 30);</text>
  
  <!-- Arrow Functions -->
  <rect x="750" y="660" width="600" height="200" class="arrow-this" rx="10" opacity="0.4"/>
  <text x="1050" y="685" text-anchor="middle" class="subtitle">➡️ ARROW FUNCTIONS (Lexical this)</text>
  
  <rect x="770" y="710" width="560" height="130" fill="#ffffff" stroke="#d68910" stroke-width="2" rx="5"/>
  <text x="1050" y="735" text-anchor="middle" class="subtitle">Lexical Binding</text>
  <text x="780" y="755" class="code">const obj = {</text>
  <text x="790" y="770" class="code">name: "Object",</text>
  <text x="790" y="785" class="code">regularMethod: function() {</text>
  <text x="800" y="800" class="code">console.log(this.name); // "Object"</text>
  <text x="800" y="815" class="code">const arrowFunc = () => {</text>
  <text x="810" y="830" class="code">console.log(this.name); // "Object" (inherited)</text>
  <text x="800" y="845" class="code">};</text>
  <text x="800" y="860" class="code">arrowFunc(); // 'this' from enclosing scope</text>
  <text x="790" y="875" class="code">}</text>
  <text x="780" y="890" class="code">};</text>
  
  <!-- Common Pitfalls -->
  <rect x="50" y="890" width="1300" height="250" fill="#f8f9fa" stroke="#e74c3c" stroke-width="2" rx="10"/>
  <text x="700" y="915" text-anchor="middle" class="subtitle">⚠️ COMMON PITFALLS AND SOLUTIONS</text>
  
  <!-- Lost Context -->
  <rect x="70" y="940" width="300" height="180" fill="#ffffff" stroke="#e74c3c" stroke-width="2" rx="5"/>
  <text x="220" y="965" text-anchor="middle" class="subtitle">😵 Lost Context</text>
  <text x="80" y="985" class="code">const obj = {</text>
  <text x="90" y="1000" class="code">name: "MyObject",</text>
  <text x="90" y="1015" class="code">greet() {</text>
  <text x="100" y="1030" class="code">console.log(this.name);</text>
  <text x="90" y="1045" class="code">}</text>
  <text x="80" y="1060" class="code">};</text>
  <text x="80" y="1080" class="code">const greet = obj.greet;</text>
  <text x="80" y="1095" class="code">greet(); // undefined (lost context)</text>
  <text x="80" y="1110" class="small-text">Problem: Assignment loses binding</text>
  
  <!-- Event Handler Context -->
  <rect x="390" y="940" width="300" height="180" fill="#ffffff" stroke="#e74c3c" stroke-width="2" rx="5"/>
  <text x="540" y="965" text-anchor="middle" class="subtitle">🖱️ Event Handler Context</text>
  <text x="400" y="985" class="code">class Button {</text>
  <text x="410" y="1000" class="code">constructor(element) {</text>
  <text x="420" y="1015" class="code">this.element = element;</text>
  <text x="420" y="1030" class="code">this.clickCount = 0;</text>
  <text x="420" y="1045" class="code">// Wrong way:</text>
  <text x="420" y="1060" class="code">element.onclick = this.handleClick;</text>
  <text x="420" y="1075" class="code">// Right way:</text>
  <text x="420" y="1090" class="code">element.onclick = this.handleClick.bind(this);</text>
  <text x="410" y="1105" class="code">}</text>
  <text x="400" y="1120" class="code">}</text>
  
  <!-- Callback Context -->
  <rect x="710" y="940" width="300" height="180" fill="#ffffff" stroke="#e74c3c" stroke-width="2" rx="5"/>
  <text x="860" y="965" text-anchor="middle" class="subtitle">📞 Callback Context</text>
  <text x="720" y="985" class="code">class Timer {</text>
  <text x="730" y="1000" class="code">constructor() {</text>
  <text x="740" y="1015" class="code">this.seconds = 0;</text>
  <text x="730" y="1030" class="code">}</text>
  <text x="730" y="1050" class="code">start() {</text>
  <text x="740" y="1065" class="code">// Problem:</text>
  <text x="740" y="1080" class="code">setInterval(this.tick, 1000);</text>
  <text x="740" y="1095" class="code">// Solution:</text>
  <text x="740" y="1110" class="code">setInterval(() => this.tick(), 1000);</text>
  <text x="730" y="1125" class="code">}</text>
  <text x="720" y="1140" class="code">}</text>
  
  <!-- Best Practices -->
  <rect x="1030" y="940" width="300" height="180" fill="#ffffff" stroke="#27ae60" stroke-width="2" rx="5"/>
  <text x="1180" y="965" text-anchor="middle" class="subtitle">✅ Best Practices</text>
  <text x="1040" y="985" class="text">1. Use arrow functions for callbacks</text>
  <text x="1040" y="1000" class="text">2. Bind in constructor for event handlers</text>
  <text x="1040" y="1015" class="text">3. Store reference: const self = this</text>
  <text x="1040" y="1030" class="text">4. Use strict mode to catch errors</text>
  <text x="1040" y="1045" class="text">5. Understand lexical vs dynamic binding</text>
  <text x="1040" y="1065" class="code">// Modern approach:</text>
  <text x="1040" y="1080" class="code">class MyClass {</text>
  <text x="1050" y="1095" class="code">method = () => {</text>
  <text x="1060" y="1110" class="code">// 'this' always bound to instance</text>
  <text x="1050" y="1125" class="code">}</text>
  <text x="1040" y="1140" class="code">}</text>
  
  <!-- Arrows showing this flow -->
  <path d="M 190 160 Q 190 200 220 250" class="this-arrow"/>
  <path d="M 450 160 Q 450 200 540 250" class="this-arrow"/>
  <path d="M 710 160 Q 710 200 1050 250" class="this-arrow"/>
  <path d="M 970 160 Q 970 200 220 250" class="this-arrow dashed"/>
  <path d="M 1230 160 Q 1230 200 1050 250" class="this-arrow"/>
</svg>
