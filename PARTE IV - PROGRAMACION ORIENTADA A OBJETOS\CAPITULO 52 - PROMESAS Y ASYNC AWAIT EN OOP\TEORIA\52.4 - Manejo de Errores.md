# **52.4 - MANEJO DE ERRORES ASÍNCRONOS**

## **📖 INTRODUCCIÓN**

El manejo de errores asíncronos representa uno de los aspectos más críticos y complejos de la programación moderna, donde la diferencia entre un sistema robusto y uno frágil radica en cómo se anticipan, capturan, procesan y recuperan de los fallos inevitables que ocurren en operaciones distribuidas y asíncronas. En el contexto de la programación orientada a objetos con JavaScript, el manejo de errores asíncronos va mucho más allá de simples bloques try/catch; requiere una arquitectura sofisticada que incluya estrategias de retry inteligentes, circuit breakers para prevenir cascadas de fallos, logging estructurado para debugging efectivo, y mecanismos de recovery que permitan al sistema continuar operando incluso cuando componentes individuales fallan. En aplicaciones empresariales modernas, donde los usuarios esperan disponibilidad 24/7 y los sistemas deben manejar cargas variables y servicios externos impredecibles, dominar el manejo de errores asíncronos es la diferencia entre una aplicación que inspira confianza y una que genera frustración. Esta maestría incluye entender cuándo fallar rápido versus cuándo persistir, cómo propagar errores de manera que preserven contexto útil para debugging, y cómo implementar estrategias de fallback que mantengan la funcionalidad core incluso cuando servicios auxiliares están indisponibles.

## **🔍 CONCEPTOS FUNDAMENTALES**

### **Clasificación de Errores Asíncronos**

![Clasificación de Errores](../VISUALIZACIONES/anatomia/error-classification.svg)

```javascript
class AsyncErrorClassifier {
    constructor() {
        // Clasificadores de errores por tipo y severidad
        this.errorClassifiers = new Map();
        this.setupDefaultClassifiers();
    }
    
    /**
     * Clasifica un error según múltiples criterios
     * @param {Error} error - Error a clasificar
     * @param {Object} context - Contexto del error
     * @returns {Object} Clasificación detallada del error
     */
    classifyError(error, context = {}) {
        const classification = {
            type: 'UNKNOWN',                    // Tipo de error
            severity: 'MEDIUM',                 // Severidad: LOW, MEDIUM, HIGH, CRITICAL
            retryable: false,                   // Si se puede reintentar
            temporary: false,                   // Si es temporal o permanente
            userFacing: false,                  // Si afecta directamente al usuario
            category: 'GENERAL',                // Categoría: NETWORK, VALIDATION, BUSINESS, etc.
            recoverable: false,                 // Si se puede recuperar automáticamente
            escalationLevel: 'NONE'             // Nivel de escalación requerido
        };
        
        // Aplicar clasificadores en orden de prioridad
        for (const [name, classifier] of this.errorClassifiers) {
            try {
                const result = classifier(error, context);
                if (result) {
                    Object.assign(classification, result);
                    break; // Usar el primer clasificador que coincida
                }
            } catch (classifierError) {
                console.warn(`Error en clasificador ${name}:`, classifierError.message);
            }
        }
        
        return classification;
    }
    
    /**
     * Configura clasificadores por defecto
     */
    setupDefaultClassifiers() {
        // Clasificador de errores de red
        this.errorClassifiers.set('network', (error, context) => {
            if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || 
                error.code === 'ETIMEDOUT' || error.name === 'NetworkError') {
                return {
                    type: 'NETWORK_ERROR',
                    severity: 'HIGH',
                    retryable: true,
                    temporary: true,
                    category: 'NETWORK',
                    recoverable: true,
                    escalationLevel: 'OPERATIONS'
                };
            }
        });
        
        // Clasificador de errores HTTP
        this.errorClassifiers.set('http', (error, context) => {
            if (error.status || error.statusCode) {
                const status = error.status || error.statusCode;
                
                if (status >= 500) {
                    return {
                        type: 'SERVER_ERROR',
                        severity: 'HIGH',
                        retryable: true,
                        temporary: true,
                        category: 'SERVER',
                        recoverable: true,
                        escalationLevel: 'DEVELOPMENT'
                    };
                } else if (status >= 400) {
                    return {
                        type: 'CLIENT_ERROR',
                        severity: 'MEDIUM',
                        retryable: false,
                        temporary: false,
                        category: 'CLIENT',
                        recoverable: false,
                        escalationLevel: 'NONE'
                    };
                }
            }
        });
        
        // Clasificador de errores de validación
        this.errorClassifiers.set('validation', (error, context) => {
            if (error.name === 'ValidationError' || 
                error.message.includes('validation') ||
                error.message.includes('invalid')) {
                return {
                    type: 'VALIDATION_ERROR',
                    severity: 'LOW',
                    retryable: false,
                    temporary: false,
                    userFacing: true,
                    category: 'VALIDATION',
                    recoverable: false,
                    escalationLevel: 'NONE'
                };
            }
        });
    }
}
```

### **🔍 EXPLICACIÓN EXHAUSTIVA DEL CÓDIGO**

**Líneas 8-11: Constructor y Estado**
- `errorClassifiers`: Map que almacena funciones clasificadoras por nombre
- `setupDefaultClassifiers()`: Inicializa clasificadores comunes para errores típicos

**Líneas 18-32: Objeto de Clasificación**
- **`type`**: Identificador específico del tipo de error para logging y métricas
- **`severity`**: Nivel de severidad para priorización y alertas
- **`retryable`**: Determina si vale la pena reintentar la operación
- **`temporary`**: Indica si el error es temporal (red) o permanente (validación)
- **`userFacing`**: Si el error debe mostrarse al usuario final
- **`category`**: Categoría para agrupación y análisis de patrones
- **`recoverable`**: Si el sistema puede recuperarse automáticamente
- **`escalationLevel`**: Nivel de escalación para alertas y notificaciones

**Líneas 35-45: Lógica de Clasificación**
- **Línea 36**: Itera sobre clasificadores en orden de prioridad
- **Líneas 38-42**: Aplica cada clasificador de forma segura con try/catch
- **Línea 43**: Usa el primer clasificador que retorne un resultado válido

### **💻 EMULACIÓN DE CONSOLA**

```bash
# Crear clasificador y probar diferentes tipos de errores
const classifier = new AsyncErrorClassifier();

# Error de red
const networkError = new Error('Connection refused');
networkError.code = 'ECONNREFUSED';
const networkClassification = classifier.classifyError(networkError);

console.log(networkClassification);
{
  type: 'NETWORK_ERROR',
  severity: 'HIGH',
  retryable: true,
  temporary: true,
  userFacing: false,
  category: 'NETWORK',
  recoverable: true,
  escalationLevel: 'OPERATIONS'
}

# Error HTTP 500
const serverError = new Error('Internal Server Error');
serverError.status = 500;
const serverClassification = classifier.classifyError(serverError);

console.log(serverClassification);
{
  type: 'SERVER_ERROR',
  severity: 'HIGH',
  retryable: true,
  temporary: true,
  userFacing: false,
  category: 'SERVER',
  recoverable: true,
  escalationLevel: 'DEVELOPMENT'
}

# Error de validación
const validationError = new Error('Email is invalid');
validationError.name = 'ValidationError';
const validationClassification = classifier.classifyError(validationError);

console.log(validationClassification);
{
  type: 'VALIDATION_ERROR',
  severity: 'LOW',
  retryable: false,
  temporary: false,
  userFacing: true,
  category: 'VALIDATION',
  recoverable: false,
  escalationLevel: 'NONE'
}
```

## **⚙️ ESTRATEGIAS AVANZADAS**

### **1. Sistema de Retry Inteligente**

```javascript
class IntelligentRetrySystem {
    constructor(options = {}) {
        this.strategies = new Map();
        this.setupDefaultStrategies();
    }
    
    /**
     * Ejecuta operación con retry inteligente
     * @param {Function} operation - Operación asíncrona
     * @param {Object} options - Opciones de retry
     */
    async executeWithRetry(operation, options = {}) {
        const strategy = this.strategies.get(options.strategy || 'exponential');
        let attempt = 0;
        let lastError;
        
        while (attempt < strategy.maxAttempts) {
            attempt++;
            
            try {
                return await operation();
            } catch (error) {
                lastError = error;
                
                // Verificar si es retryable
                if (!this.shouldRetry(error, attempt, strategy)) {
                    break;
                }
                
                // Calcular delay
                const delay = this.calculateDelay(attempt, strategy, error);
                await this.delay(delay);
            }
        }
        
        throw lastError;
    }
}
```

### **2. Circuit Breaker Pattern**

```javascript
class AsyncCircuitBreaker {
    constructor(options = {}) {
        this.failureThreshold = options.failureThreshold || 5;
        this.recoveryTimeout = options.recoveryTimeout || 60000;
        this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
        this.failureCount = 0;
        this.lastFailureTime = null;
        this.successCount = 0;
    }
    
    /**
     * Ejecuta operación a través del circuit breaker
     * @param {Function} operation - Operación a proteger
     */
    async execute(operation) {
        if (this.state === 'OPEN') {
            if (Date.now() - this.lastFailureTime < this.recoveryTimeout) {
                throw new Error('Circuit breaker is OPEN');
            } else {
                this.state = 'HALF_OPEN';
                this.successCount = 0;
            }
        }
        
        try {
            const result = await operation();
            this.onSuccess();
            return result;
        } catch (error) {
            this.onFailure();
            throw error;
        }
    }
}
```

## **🎯 CASOS DE USO PRÁCTICOS**

### **Caso 1: Sistema de Pagos Resiliente**
Manejo de errores en procesamiento de pagos con retry inteligente, circuit breakers para proveedores de pago, y fallbacks a métodos alternativos.

### **Caso 2: API Gateway con Tolerancia a Fallos**
Gateway que maneja fallos de microservicios con circuit breakers, timeouts adaptativos, y respuestas degradadas cuando servicios están caídos.

### **Caso 3: Sistema de Notificaciones Robusto**
Servicio que maneja fallos de canales de notificación (email, SMS, push) con retry exponencial y fallbacks a canales alternativos.

## **⚠️ ERRORES COMUNES**

### **Error 1: No Clasificar Errores Apropiadamente**
```javascript
// ❌ INCORRECTO - Tratar todos los errores igual
catch (error) {
    await retry(operation); // Retry incluso errores no retryables
}

// ✅ CORRECTO - Clasificar antes de decidir acción
catch (error) {
    const classification = classifier.classifyError(error);
    if (classification.retryable) {
        await retry(operation);
    } else {
        throw error;
    }
}
```

### **Error 2: Circuit Breaker sin Estado Persistente**
```javascript
// ❌ INCORRECTO - Estado se pierde al reiniciar
class BadCircuitBreaker {
    constructor() {
        this.state = 'CLOSED'; // Se pierde al reiniciar
    }
}

// ✅ CORRECTO - Estado persistente
class GoodCircuitBreaker {
    constructor(storage) {
        this.storage = storage;
        this.state = storage.get('circuitState') || 'CLOSED';
    }
}
```

### **Error 3: Retry sin Backoff**
```javascript
// ❌ INCORRECTO - Retry inmediato
for (let i = 0; i < 3; i++) {
    try {
        return await operation();
    } catch (error) {
        // Retry inmediato puede empeorar la situación
    }
}

// ✅ CORRECTO - Backoff exponencial
for (let i = 0; i < 3; i++) {
    try {
        return await operation();
    } catch (error) {
        const delay = 1000 * Math.pow(2, i);
        await new Promise(resolve => setTimeout(resolve, delay));
    }
}
```

## **💡 MEJORES PRÁCTICAS**

### **1. Implementar Clasificación de Errores**
Clasifica errores por tipo, severidad y capacidad de retry para tomar decisiones inteligentes.

### **2. Usar Circuit Breakers para Servicios Externos**
Protege tu sistema de cascadas de fallos implementando circuit breakers para servicios externos.

### **3. Implementar Observabilidad Completa**
Registra métricas detalladas de errores para identificar patrones y optimizar estrategias.

### **4. Diseñar Fallbacks Apropiados**
Implementa estrategias de fallback que mantengan funcionalidad core cuando servicios auxiliares fallan.

### **5. Testear Escenarios de Fallo**
Usa chaos engineering para validar que tu manejo de errores funciona bajo condiciones adversas.

## **🤔 PREGUNTAS PARA REFLEXIÓN**

- ¿Cómo diseñarías un sistema de escalación automática de errores basado en severidad y frecuencia?
- ¿Qué estrategias implementarías para manejar errores en sistemas distribuidos con múltiples puntos de fallo?
- ¿Cómo balancearías entre resilencia y performance en un sistema de alto throughput?
- ¿De qué manera integrarías machine learning para predecir y prevenir errores antes de que ocurran?

## **📚 RECURSOS ADICIONALES**

- **Implementación Completa:** Ver `CODIGO/errores/ErrorHandler.js`
- **Ejemplos Prácticos:** Ver `EJEMPLOS/avanzados/error-handling.js`
- **Tests:** Ver `TESTS/unit/error-handling.test.js`
- **Visualizaciones:** Ver `VISUALIZACIONES/anatomia/error-classification.svg`

---

**Fin del Capítulo 52: Promesas y Async/Await en OOP**
