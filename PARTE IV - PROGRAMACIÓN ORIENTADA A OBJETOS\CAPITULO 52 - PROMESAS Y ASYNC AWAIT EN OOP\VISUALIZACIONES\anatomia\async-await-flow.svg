<svg width="1100" height="800" xmlns="http://www.w3.org/2000/svg">
  <!-- Definiciones -->
  <defs>
    <!-- Gradientes -->
    <linearGradient id="asyncGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="awaitGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="promiseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="errorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
    
    <!-- Sombra -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="3" dy="3" stdDeviation="4" flood-color="#000" flood-opacity="0.3"/>
    </filter>
    
    <!-- Flechas -->
    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
    
    <marker id="arrowGreen" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#10b981"/>
    </marker>
    
    <marker id="arrowRed" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ef4444"/>
    </marker>
  </defs>

  <!-- Título -->
  <text x="550" y="30" text-anchor="middle" font-size="22" font-weight="bold" fill="#1f2937">
    Flujo de Ejecución Async/Await en Métodos de Clase
  </text>
  
  <!-- Clase Container -->
  <rect x="50" y="60" width="1000" height="700" fill="#f8fafc" stroke="#64748b" stroke-width="3" rx="15" filter="url(#shadow)"/>
  <text x="70" y="90" font-size="18" font-weight="bold" fill="#374151">class AdvancedDataManager</text>
  
  <!-- Método Async Principal -->
  <rect x="80" y="110" width="940" height="200" fill="#dcfce7" stroke="#16a34a" stroke-width="3" rx="10" filter="url(#shadow)"/>
  <text x="550" y="135" text-anchor="middle" font-size="16" font-weight="bold" fill="#16a34a">async getData(entityType, id, options = {})</text>
  
  <!-- Flujo de Ejecución -->
  <rect x="100" y="150" width="900" height="150" fill="#f0fdf4" stroke="#16a34a" stroke-width="1" rx="5"/>
  
  <!-- Paso 1: Inicio -->
  <rect x="120" y="170" width="150" height="40" fill="url(#asyncGradient)" stroke="#10b981" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="195" y="185" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">1. INICIO</text>
  <text x="195" y="200" text-anchor="middle" font-size="10" fill="#fff">Función marcada async</text>
  
  <!-- Paso 2: Verificar Cache -->
  <rect x="290" y="170" width="150" height="40" fill="#e0f2fe" stroke="#0891b2" stroke-width="2" rx="5"/>
  <text x="365" y="185" text-anchor="middle" font-size="12" font-weight="bold" fill="#0c4a6e">2. VERIFICAR CACHE</text>
  <text x="365" y="200" text-anchor="middle" font-size="10" fill="#0c4a6e">Operación síncrona</text>
  
  <!-- Paso 3: Await Request -->
  <rect x="460" y="170" width="150" height="40" fill="url(#awaitGradient)" stroke="#f59e0b" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="535" y="185" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">3. AWAIT REQUEST</text>
  <text x="535" y="200" text-anchor="middle" font-size="10" fill="#fff">Pausa ejecución</text>
  
  <!-- Paso 4: Procesar Respuesta -->
  <rect x="630" y="170" width="150" height="40" fill="#e0f2fe" stroke="#0891b2" stroke-width="2" rx="5"/>
  <text x="705" y="185" text-anchor="middle" font-size="12" font-weight="bold" fill="#0c4a6e">4. PROCESAR</text>
  <text x="705" y="200" text-anchor="middle" font-size="10" fill="#0c4a6e">Operación síncrona</text>
  
  <!-- Paso 5: Return -->
  <rect x="800" y="170" width="150" height="40" fill="url(#asyncGradient)" stroke="#10b981" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="875" y="185" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">5. RETURN</text>
  <text x="875" y="200" text-anchor="middle" font-size="10" fill="#fff">Promise.resolve()</text>
  
  <!-- Flechas de flujo -->
  <line x1="270" y1="190" x2="290" y2="190" stroke="#374151" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="440" y1="190" x2="460" y2="190" stroke="#374151" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="610" y1="190" x2="630" y2="190" stroke="#374151" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="780" y1="190" x2="800" y2="190" stroke="#374151" stroke-width="3" marker-end="url(#arrow)"/>
  
  <!-- Detalles del Await -->
  <rect x="80" y="330" width="940" height="180" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" rx="10"/>
  <text x="90" y="355" font-size="16" font-weight="bold" fill="#92400e">Detalle del Await: Pausa y Reanudación</text>
  
  <!-- Event Loop -->
  <rect x="100" y="370" width="200" height="120" fill="#fffbeb" stroke="#f59e0b" stroke-width="2" rx="5"/>
  <text x="200" y="390" text-anchor="middle" font-size="14" font-weight="bold" fill="#92400e">Event Loop</text>
  <text x="110" y="410" font-size="11" fill="#92400e">1. await pausa función</text>
  <text x="110" y="425" font-size="11" fill="#92400e">2. Control vuelve al caller</text>
  <text x="110" y="440" font-size="11" fill="#92400e">3. Event loop continúa</text>
  <text x="110" y="455" font-size="11" fill="#92400e">4. Promise se resuelve</text>
  <text x="110" y="470" font-size="11" fill="#92400e">5. Función se reanuda</text>
  
  <!-- Microtask Queue -->
  <rect x="320" y="370" width="200" height="120" fill="#e0f2fe" stroke="#0891b2" stroke-width="2" rx="5"/>
  <text x="420" y="390" text-anchor="middle" font-size="14" font-weight="bold" fill="#0c4a6e">Microtask Queue</text>
  <text x="330" y="410" font-size="11" fill="#0c4a6e">• Promise callbacks</text>
  <text x="330" y="425" font-size="11" fill="#0c4a6e">• async/await continuations</text>
  <text x="330" y="440" font-size="11" fill="#0c4a6e">• queueMicrotask()</text>
  <text x="330" y="455" font-size="11" fill="#0c4a6e">• MutationObserver</text>
  <text x="330" y="470" font-size="11" fill="#0c4a6e">Prioridad: ALTA</text>
  
  <!-- Call Stack -->
  <rect x="540" y="370" width="200" height="120" fill="#dcfce7" stroke="#16a34a" stroke-width="2" rx="5"/>
  <text x="640" y="390" text-anchor="middle" font-size="14" font-weight="bold" fill="#16a34a">Call Stack</text>
  <text x="550" y="410" font-size="11" fill="#16a34a">getData() [PAUSED]</text>
  <text x="550" y="425" font-size="11" fill="#16a34a">↓ await executeRequest()</text>
  <text x="550" y="440" font-size="11" fill="#16a34a">↓ fetch(url)</text>
  <text x="550" y="455" font-size="11" fill="#16a34a">↓ [WAITING...]</text>
  <text x="550" y="470" font-size="11" fill="#16a34a">Estado: SUSPENDED</text>
  
  <!-- Web APIs -->
  <rect x="760" y="370" width="200" height="120" fill="#fce7f3" stroke="#ec4899" stroke-width="2" rx="5"/>
  <text x="860" y="390" text-anchor="middle" font-size="14" font-weight="bold" fill="#be185d">Web APIs</text>
  <text x="770" y="410" font-size="11" fill="#be185d">• fetch() API</text>
  <text x="770" y="425" font-size="11" fill="#be185d">• setTimeout()</text>
  <text x="770" y="440" font-size="11" fill="#be185d">• DOM Events</text>
  <text x="770" y="455" font-size="11" fill="#be185d">• File System</text>
  <text x="770" y="470" font-size="11" fill="#be185d">Ejecución: ASYNC</text>
  
  <!-- Manejo de Errores -->
  <rect x="80" y="530" width="940" height="120" fill="#fee2e2" stroke="#dc2626" stroke-width="2" rx="10"/>
  <text x="90" y="555" font-size="16" font-weight="bold" fill="#dc2626">Manejo de Errores con Try/Catch</text>
  
  <!-- Try Block -->
  <rect x="100" y="570" width="280" height="60" fill="#fef2f2" stroke="#dc2626" stroke-width="1" rx="5"/>
  <text x="240" y="590" text-anchor="middle" font-size="12" font-weight="bold" fill="#dc2626">try { await operation() }</text>
  <text x="110" y="610" font-size="10" fill="#dc2626">• Ejecuta código asíncrono</text>
  <text x="110" y="625" font-size="10" fill="#dc2626">• Captura errores de Promises</text>
  
  <!-- Catch Block -->
  <rect x="400" y="570" width="280" height="60" fill="#fef2f2" stroke="#dc2626" stroke-width="1" rx="5"/>
  <text x="540" y="590" text-anchor="middle" font-size="12" font-weight="bold" fill="#dc2626">catch (error) { handle }</text>
  <text x="410" y="610" font-size="10" fill="#dc2626">• Maneja Promise rejections</text>
  <text x="410" y="625" font-size="10" fill="#dc2626">• Convierte async errors en sync</text>
  
  <!-- Finally Block -->
  <rect x="700" y="570" width="280" height="60" fill="#fef2f2" stroke="#dc2626" stroke-width="1" rx="5"/>
  <text x="840" y="590" text-anchor="middle" font-size="12" font-weight="bold" fill="#dc2626">finally { cleanup }</text>
  <text x="710" y="610" font-size="10" fill="#dc2626">• Siempre ejecuta</text>
  <text x="710" y="625" font-size="10" fill="#dc2626">• Ideal para cleanup</text>
  
  <!-- Timeline de Ejecución -->
  <rect x="80" y="670" width="940" height="80" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="2" rx="8"/>
  <text x="90" y="690" font-size="14" font-weight="bold" fill="#0c4a6e">Timeline de Ejecución Async/Await</text>
  
  <text x="90" y="710" font-size="11" fill="#0c4a6e">T0: Función async llamada → T1: await encontrado → T2: Función pausada → T3: Event loop continúa</text>
  <text x="90" y="725" font-size="11" fill="#0c4a6e">T4: Promise resuelve → T5: Microtask encolado → T6: Función reanuda → T7: Resultado retornado</text>
  <text x="90" y="740" font-size="11" fill="#0c4a6e">Ventaja: Código que se lee como síncrono pero ejecuta asíncronamente</text>
  
  <!-- Conexiones entre componentes -->
  <line x1="200" y1="490" x2="200" y2="530" stroke="#f59e0b" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="420" y1="490" x2="420" y2="530" stroke="#0891b2" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="640" y1="490" x2="640" y2="530" stroke="#16a34a" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="860" y1="490" x2="860" y2="530" stroke="#ec4899" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Indicadores de flujo -->
  <circle cx="535" cy="230" r="15" fill="url(#awaitGradient)" stroke="#f59e0b" stroke-width="3"/>
  <text x="535" y="237" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">⏸</text>
  <text x="535" y="255" text-anchor="middle" font-size="10" fill="#92400e">PAUSA</text>
  
  <!-- Flecha de reanudación -->
  <path d="M 535 260 Q 535 290 535 320" stroke="#f59e0b" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
  <text x="545" y="290" font-size="10" fill="#92400e">Reanuda cuando</text>
  <text x="545" y="305" font-size="10" fill="#92400e">Promise resuelve</text>
  
  <!-- Leyenda -->
  <rect x="900" y="100" width="140" height="120" fill="#f8fafc" stroke="#64748b" stroke-width="1" rx="5" opacity="0.95"/>
  <text x="910" y="120" font-size="12" font-weight="bold" fill="#374151">LEYENDA:</text>
  <rect x="910" y="130" width="15" height="8" fill="url(#asyncGradient)" rx="2"/>
  <text x="935" y="138" font-size="10" fill="#374151">Async function</text>
  <rect x="910" y="145" width="15" height="8" fill="url(#awaitGradient)" rx="2"/>
  <text x="935" y="153" font-size="10" fill="#374151">Await operation</text>
  <rect x="910" y="160" width="15" height="8" fill="#e0f2fe" rx="2"/>
  <text x="935" y="168" font-size="10" fill="#374151">Sync operation</text>
  <circle cx="920" cy="180" r="4" fill="#f59e0b"/>
  <text x="935" y="185" font-size="10" fill="#374151">Pause point</text>
  <line x1="910" y1="195" x2="925" y2="195" stroke="#374151" stroke-width="2" stroke-dasharray="2,2"/>
  <text x="935" y="200" font-size="10" fill="#374151">Async flow</text>
</svg>
