# **Quiz - Configuración del Entorno de Desarrollo**

## **📋 Información del Quiz**

- **Tema:** 1.2. Configuración del Entorno de Desarrollo
- **Subtema:** 1.2.1 - Instalación de Node.js
- **Tiempo límite:** 15 minutos
- **Preguntas:** 15
- **Puntuación mínima:** 80% (12/15 correctas)
- **Intentos permitidos:** 3

## **🎯 Objetivos Evaluados**

Este quiz evalúa tu comprensión de:
- [ ] Conceptos básicos de Node.js
- [ ] Proceso de instalación
- [ ] Verificación de la instalación
- [ ] Comandos básicos
- [ ] Solución de problemas comunes

---

## **📝 PREGUNTAS**

### **Pregunta 1** (Conceptual)
**¿Qué es Node.js?**

a) Un navegador web especializado para desarrolladores  
b) Un entorno de ejecución de JavaScript construido sobre el motor V8 de Chrome  
c) Un lenguaje de programación derivado de JavaScript  
d) Una base de datos optimizada para aplicaciones web  

<details>
<summary>Ver respuesta</summary>

**Respuesta correcta: b) Un entorno de ejecución de JavaScript construido sobre el motor V8 de Chrome**

**Explicación:** Node.js es un runtime environment que permite ejecutar JavaScript fuera del navegador, utilizando el motor V8 de Google Chrome.
</details>

---

### **Pregunta 2** (Práctica)
**¿Cuál es el comando correcto para verificar la versión de Node.js instalada?**

a) `node -v`  
b) `node --version`  
c) `nodejs --version`  
d) Tanto a) como b) son correctos  

<details>
<summary>Ver respuesta</summary>

**Respuesta correcta: d) Tanto a) como b) son correctos**

**Explicación:** Ambos comandos `node -v` y `node --version` muestran la versión instalada de Node.js.
</details>

---

### **Pregunta 3** (Conceptual)
**¿Qué herramienta viene incluida automáticamente con la instalación de Node.js?**

a) Yarn  
b) npm  
c) Git  
d) Visual Studio Code  

<details>
<summary>Ver respuesta</summary>

**Respuesta correcta: b) npm**

**Explicación:** npm (Node Package Manager) se instala automáticamente junto con Node.js y es el gestor de paquetes por defecto.
</details>

---

### **Pregunta 4** (Práctica)
**Si ejecutas `node ejemplo.js` y obtienes el error "'node' is not recognized", ¿cuál es la causa más probable?**

a) El archivo ejemplo.js no existe  
b) Node.js no está instalado  
c) Node.js no está en el PATH del sistema  
d) El archivo tiene errores de sintaxis  

<details>
<summary>Ver respuesta</summary>

**Respuesta correcta: c) Node.js no está en el PATH del sistema**

**Explicación:** Este error indica que el sistema no puede encontrar el comando `node`, lo que significa que Node.js no está en el PATH o no está instalado correctamente.
</details>

---

### **Pregunta 5** (Conceptual)
**¿Qué significa LTS en el contexto de las versiones de Node.js?**

a) Latest Technology Stack  
b) Long Term Support  
c) Linux Terminal Support  
d) Local Testing Suite  

<details>
<summary>Ver respuesta</summary>

**Respuesta correcta: b) Long Term Support**

**Explicación:** LTS significa Long Term Support, indicando versiones que reciben soporte extendido y son recomendadas para producción.
</details>

---

### **Pregunta 6** (Código)
**¿Cuál de estos códigos muestra correctamente la versión de Node.js desde dentro de un script?**

a) `console.log(node.version);`  
b) `console.log(process.version);`  
c) `console.log(system.nodeVersion);`  
d) `console.log(require('version'));`  

<details>
<summary>Ver respuesta</summary>

**Respuesta correcta: b) `console.log(process.version);`**

**Explicación:** `process.version` es la propiedad correcta para obtener la versión de Node.js desde dentro de un script.
</details>

---

### **Pregunta 7** (Práctica)
**¿Cuál es la extensión de archivo estándar para scripts de Node.js?**

a) `.node`  
b) `.nodejs`  
c) `.js`  
d) `.njs`  

<details>
<summary>Ver respuesta</summary>

**Respuesta correcta: c) `.js`**

**Explicación:** Los scripts de Node.js utilizan la extensión `.js` estándar de JavaScript.
</details>

---

### **Pregunta 8** (Conceptual)
**¿Qué motor de JavaScript utiliza Node.js?**

a) SpiderMonkey  
b) Chakra  
c) V8  
d) JavaScriptCore  

<details>
<summary>Ver respuesta</summary>

**Respuesta correcta: c) V8**

**Explicación:** Node.js está construido sobre el motor V8 de Google Chrome, que compila JavaScript a código máquina nativo.
</details>

---

### **Pregunta 9** (Práctica)
**¿Cómo ejecutarías un archivo JavaScript llamado "app.js" con Node.js?**

a) `nodejs app.js`  
b) `node app.js`  
c) `js app.js`  
d) `run app.js`  

<details>
<summary>Ver respuesta</summary>

**Respuesta correcta: b) `node app.js`**

**Explicación:** El comando `node` seguido del nombre del archivo es la forma estándar de ejecutar scripts JavaScript con Node.js.
</details>

---

### **Pregunta 10** (Código)
**¿Cuál de estos códigos obtiene información sobre el sistema operativo?**

a) `const os = require('os'); console.log(os.platform());`  
b) `const system = require('system'); console.log(system.os());`  
c) `console.log(process.os);`  
d) `console.log(node.system);`  

<details>
<summary>Ver respuesta</summary>

**Respuesta correcta: a) `const os = require('os'); console.log(os.platform());`**

**Explicación:** El módulo `os` es el módulo core de Node.js para obtener información del sistema operativo.
</details>

---

### **Pregunta 11** (Conceptual)
**¿Cuál es una ventaja principal de usar Node.js para desarrollo web?**

a) Solo funciona en Windows  
b) Permite usar JavaScript tanto en frontend como backend  
c) Es más lento que otros entornos  
d) Solo sirve para aplicaciones móviles  

<details>
<summary>Ver respuesta</summary>

**Respuesta correcta: b) Permite usar JavaScript tanto en frontend como backend**

**Explicación:** Una de las principales ventajas de Node.js es permitir el desarrollo full-stack con un solo lenguaje: JavaScript.
</details>

---

### **Pregunta 12** (Práctica)
**¿Qué comando verificaría tanto Node.js como npm están instalados?**

a) `node --version && npm --version`  
b) `check node npm`  
c) `verify nodejs`  
d) `node npm --check`  

<details>
<summary>Ver respuesta</summary>

**Respuesta correcta: a) `node --version && npm --version`**

**Explicación:** El operador `&&` permite ejecutar ambos comandos secuencialmente para verificar ambas instalaciones.
</details>

---

### **Pregunta 13** (Código)
**¿Cuál es la forma correcta de obtener argumentos de línea de comandos en Node.js?**

a) `process.arguments`  
b) `process.argv`  
c) `node.args`  
d) `system.arguments`  

<details>
<summary>Ver respuesta</summary>

**Respuesta correcta: b) `process.argv`**

**Explicación:** `process.argv` es un array que contiene los argumentos de línea de comandos pasados al script.
</details>

---

### **Pregunta 14** (Práctica)
**¿Dónde deberías descargar Node.js oficialmente?**

a) github.com/nodejs  
b) nodejs.com  
c) nodejs.org  
d) node.js.com  

<details>
<summary>Ver respuesta</summary>

**Respuesta correcta: c) nodejs.org**

**Explicación:** El sitio oficial de Node.js es nodejs.org, donde se pueden descargar las versiones oficiales y seguras.
</details>

---

### **Pregunta 15** (Conceptual)
**¿Qué tipo de arquitectura utiliza Node.js para manejar operaciones I/O?**

a) Multi-threading bloqueante  
b) Single-threading bloqueante  
c) Multi-threading no bloqueante  
d) Single-threading no bloqueante (event-driven)  

<details>
<summary>Ver respuesta</summary>

**Respuesta correcta: d) Single-threading no bloqueante (event-driven)**

**Explicación:** Node.js utiliza un modelo de un solo hilo con un bucle de eventos (event loop) para manejar operaciones I/O de manera no bloqueante.
</details>

---

## **📊 Evaluación de Resultados**

### **Puntuación:**
- **15/15 (100%):** 🏆 ¡Excelente! Dominio completo del tema
- **13-14/15 (87-93%):** 🥇 Muy bien, conocimiento sólido
- **12/15 (80%):** 🥈 Bien, cumples el mínimo requerido
- **10-11/15 (67-73%):** 🥉 Regular, necesitas repasar algunos conceptos
- **<10/15 (<67%):** ❌ Insuficiente, revisa el material y vuelve a intentar

### **Áreas de Mejora según Errores:**

**Si fallaste preguntas 1, 5, 8, 11, 15:** Repasa conceptos fundamentales de Node.js  
**Si fallaste preguntas 2, 4, 9, 12, 14:** Practica más con comandos y instalación  
**Si fallaste preguntas 6, 10, 13:** Estudia más el código y APIs de Node.js  
**Si fallaste preguntas 3, 7:** Revisa herramientas y convenciones básicas  

---

## **🎯 Acciones Recomendadas**

### **Si obtuviste 80% o más:**
✅ ¡Felicitaciones! Puedes continuar con el siguiente tema  
✅ Considera hacer el proyecto práctico para reforzar  
✅ Ayuda a otros estudiantes en el foro  

### **Si obtuviste menos de 80%:**
📚 Revisa el material del tema 1.2.1  
🔄 Practica con los ejemplos de código  
💻 Ejecuta el ejemplo-basico.js  
🔁 Vuelve a tomar el quiz cuando te sientas preparado  

---

## **📝 Notas Adicionales**

- **Tiempo promedio:** La mayoría de estudiantes completan este quiz en 8-12 minutos
- **Dificultad:** Este quiz cubre conceptos fundamentales que son base para todo el curso
- **Recursos:** Si necesitas ayuda, revisa la documentación oficial de Node.js
- **Siguiente paso:** Una vez aprobado, continúa con "1.2.2 - Configuración de Visual Studio Code"

---

## **🔗 Recursos de Apoyo**

- [Documentación oficial de Node.js](https://nodejs.org/docs/)
- [Guía de instalación paso a paso](../contenido/1.2.1%20-%20Instalación%20de%20Node.js.md)
- [Ejemplo práctico](../ejemplos/ejemplo-basico.js)
- [Foro de estudiantes](https://discord.gg/curso-javascript)

**¡Buena suerte con tu evaluación!** 🚀
