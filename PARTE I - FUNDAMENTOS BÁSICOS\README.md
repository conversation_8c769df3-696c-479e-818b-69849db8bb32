# **PARTE I - FUNDAMENTOS BÁSICOS**

## **📚 Descripción de la Parte**

Esta parte del curso establece las bases sólidas de JavaScript, cubriendo desde la configuración del entorno hasta los conceptos fundamentales que todo desarrollador debe dominar. Cada capítulo está diseñado para construir conocimiento de manera progresiva, con ejemplos prácticos, ejercicios interactivos y evaluaciones que refuerzan el aprendizaje.

## **🎯 Objetivos de Aprendizaje**

Al completar esta parte, serás capaz de:

- Configurar un entorno de desarrollo profesional
- Dominar la sintaxis fundamental de JavaScript
- Trabajar con variables, tipos de datos y operadores
- Implementar estructuras de control eficientes
- Manejar errores de forma profesional

## **📋 Índice de Capítulos**

### **[Capítulo 1 - Introducción y Configuración](1%20-%20Introducción%20y%20Configuración/README.md)** ⭐⭐
**Tiempo estimado:** 8-12 horas | **Temas:** 7

Establece las bases para el desarrollo en JavaScript, desde la historia del lenguaje hasta la configuración de un entorno profesional.

- [1.1. Historia y Evolución de JavaScript](1%20-%20Introducción%20y%20Configuración/1.1.%20Historia%20y%20Evolución%20de%20JavaScript/README.md)
- [1.2. Configuración del Entorno](1%20-%20Introducción%20y%20Configuración/1.2.%20Configuración%20del%20Entorno/README.md)
- [1.3. Herramientas de Desarrollo](1%20-%20Introducción%20y%20Configuración/1.3.%20Herramientas%20de%20Desarrollo/README.md)
- [1.4. Primer Programa](1%20-%20Introducción%20y%20Configuración/1.4.%20Primer%20Programa/README.md)
- [1.5. Debugging Básico](1%20-%20Introducción%20y%20Configuración/1.5.%20Debugging%20Básico/README.md)
- [1.6. Configuración de Proyecto](1%20-%20Introducción%20y%20Configuración/1.6.%20Configuración%20de%20Proyecto/README.md)
- [1.7. Herramientas de Build](1%20-%20Introducción%20y%20Configuración/1.7.%20Herramientas%20de%20Build/README.md)

---

### **[Capítulo 2 - Sintaxis Fundamental](2%20-%20Sintaxis%20Fundamental/README.md)** ⭐⭐⭐
**Tiempo estimado:** 10-15 horas | **Temas:** 8

Domina la sintaxis básica de JavaScript, desde comentarios hasta estructuras de código.

- [2.1. Comentarios y Documentación](2%20-%20Sintaxis%20Fundamental/2.1.%20Comentarios%20y%20Documentación/README.md)
- [2.2. Palabras Reservadas](2%20-%20Sintaxis%20Fundamental/2.2.%20Palabras%20Reservadas/README.md)
- [2.3. Identificadores y Convenciones](2%20-%20Sintaxis%20Fundamental/2.3.%20Identificadores%20y%20Convenciones/README.md)
- [2.4. Literales](2%20-%20Sintaxis%20Fundamental/2.4.%20Literales/README.md)
- [2.5. Separadores y Delimitadores](2%20-%20Sintaxis%20Fundamental/2.5.%20Separadores%20y%20Delimitadores/README.md)
- [2.6. Estructura de Código](2%20-%20Sintaxis%20Fundamental/2.6.%20Estructura%20de%20Código/README.md)
- [2.7. Encoding y Unicode](2%20-%20Sintaxis%20Fundamental/2.7.%20Encoding%20y%20Unicode/README.md)
- [2.8. Strict Mode](2%20-%20Sintaxis%20Fundamental/2.8.%20Strict%20Mode/README.md)

---

### **[Capítulo 3 - Variables y Tipos de Datos](3%20-%20Variables%20y%20Tipos%20de%20Datos/README.md)** ⭐⭐⭐
**Tiempo estimado:** 12-18 horas | **Temas:** 8

Aprende a trabajar con variables y todos los tipos de datos de JavaScript.

- [3.1. Declaración de Variables](3%20-%20Variables%20y%20Tipos%20de%20Datos/3.1.%20Declaración%20de%20Variables/README.md)
- [3.2. Tipos Primitivos](3%20-%20Variables%20y%20Tipos%20de%20Datos/3.2.%20Tipos%20Primitivos/README.md)
- [3.3. Objetos y Referencias](3%20-%20Variables%20y%20Tipos%20de%20Datos/3.3.%20Objetos%20y%20Referencias/README.md)
- [3.4. Conversión de Tipos](3%20-%20Variables%20y%20Tipos%20de%20Datos/3.4.%20Conversión%20de%20Tipos/README.md)
- [3.5. Scope y Hoisting](3%20-%20Variables%20y%20Tipos%20de%20Datos/3.5.%20Scope%20y%20Hoisting/README.md)
- [3.6. Temporal Dead Zone](3%20-%20Variables%20y%20Tipos%20de%20Datos/3.6.%20Temporal%20Dead%20Zone/README.md)
- [3.7. Constantes y Inmutabilidad](3%20-%20Variables%20y%20Tipos%20de%20Datos/3.7.%20Constantes%20e%20Inmutabilidad/README.md)
- [3.8. Destructuring Básico](3%20-%20Variables%20y%20Tipos%20de%20Datos/3.8.%20Destructuring%20Básico/README.md)

---

### **[Capítulo 4 - Operadores](4%20-%20Operadores/README.md)** ⭐⭐⭐
**Tiempo estimado:** 10-14 horas | **Temas:** 8

Domina todos los operadores de JavaScript y sus casos de uso.

- [4.1. Operadores Aritméticos](4%20-%20Operadores/4.1.%20Operadores%20Aritméticos/README.md)
- [4.2. Operadores de Asignación](4%20-%20Operadores/4.2.%20Operadores%20de%20Asignación/README.md)
- [4.3. Operadores de Comparación](4%20-%20Operadores/4.3.%20Operadores%20de%20Comparación/README.md)
- [4.4. Operadores Lógicos](4%20-%20Operadores/4.4.%20Operadores%20Lógicos/README.md)
- [4.5. Operadores Unarios](4%20-%20Operadores/4.5.%20Operadores%20Unarios/README.md)
- [4.6. Operador Ternario](4%20-%20Operadores/4.6.%20Operador%20Ternario/README.md)
- [4.7. Operadores de Cadena](4%20-%20Operadores/4.7.%20Operadores%20de%20Cadena/README.md)
- [4.8. Precedencia y Asociatividad](4%20-%20Operadores/4.8.%20Precedencia%20y%20Asociatividad/README.md)

---

### **[Capítulo 5 - Estructuras de Control](5%20-%20Estructuras%20de%20Control/README.md)** ⭐⭐⭐⭐
**Tiempo estimado:** 12-16 horas | **Temas:** 7

Aprende a controlar el flujo de ejecución con estructuras condicionales y bucles.

- [5.1. Estructuras Condicionales](5%20-%20Estructuras%20de%20Control/5.1.%20Estructuras%20Condicionales/README.md)
- [5.2. Bucles Básicos](5%20-%20Estructuras%20de%20Control/5.2.%20Bucles%20Básicos/README.md)
- [5.3. Bucles Avanzados](5%20-%20Estructuras%20de%20Control/5.3.%20Bucles%20Avanzados/README.md)
- [5.4. Control de Flujo](5%20-%20Estructuras%20de%20Control/5.4.%20Control%20de%20Flujo/README.md)
- [5.5. Switch Statement](5%20-%20Estructuras%20de%20Control/5.5.%20Switch%20Statement/README.md)
- [5.6. Etiquetas y Saltos](5%20-%20Estructuras%20de%20Control/5.6.%20Etiquetas%20y%20Saltos/README.md)
- [5.7. Patrones de Control](5%20-%20Estructuras%20de%20Control/5.7.%20Patrones%20de%20Control/README.md)

---

### **[Capítulo 6 - Manejo de Errores Básico](6%20-%20Manejo%20de%20Errores%20Básico/README.md)** ⭐⭐⭐
**Tiempo estimado:** 8-12 horas | **Temas:** 6

Aprende try/catch/finally y técnicas básicas de manejo de errores.

- [6.1. Try-Catch-Finally](6%20-%20Manejo%20de%20Errores%20Básico/6.1.%20Try-Catch-Finally/README.md)
- [6.2. Tipos de Errores](6%20-%20Manejo%20de%20Errores%20Básico/6.2.%20Tipos%20de%20Errores/README.md)
- [6.3. Throw Statement](6%20-%20Manejo%20de%20Errores%20Básico/6.3.%20Throw%20Statement/README.md)
- [6.4. Error Objects](6%20-%20Manejo%20de%20Errores%20Básico/6.4.%20Error%20Objects/README.md)
- [6.5. Debugging Strategies](6%20-%20Manejo%20de%20Errores%20Básico/6.5.%20Debugging%20Strategies/README.md)
- [6.6. Best Practices](6%20-%20Manejo%20de%20Errores%20Básico/6.6.%20Best%20Practices/README.md)

---

## **🎯 Rutas de Aprendizaje de la Parte**

### **🚀 Ruta Rápida (30-45 horas)**
Enfoque en conceptos esenciales para comenzar a programar rápidamente.

### **📚 Ruta Completa (60-90 horas)**
Cobertura completa de todos los fundamentos con práctica extensiva.

### **🔬 Ruta Experto (90-120 horas)**
Para dominio completo y comprensión profunda.

---

## **📊 Sistema de Progreso**

```
Capítulo 1: [░░░░░░░░░░] 0% completado
Capítulo 2: [░░░░░░░░░░] 0% completado  
Capítulo 3: [░░░░░░░░░░] 0% completado
Capítulo 4: [░░░░░░░░░░] 0% completado
Capítulo 5: [░░░░░░░░░░] 0% completado
Capítulo 6: [░░░░░░░░░░] 0% completado

Progreso Total: [░░░░░░░░░░] 0% completado
```

## **🏆 Logros de la Parte**

- 🎯 **Configuración Expert**: Completar Capítulo 1
- 🎯 **Sintaxis Expert**: Completar Capítulo 2
- 🎯 **Variables Expert**: Completar Capítulo 3
- 🎯 **Operadores Expert**: Completar Capítulo 4
- 🎯 **Control Expert**: Completar Capítulo 5
- 🎯 **Errores Expert**: Completar Capítulo 6
- 👑 **Fundamentos Master**: Completar todos los capítulos

---

## **➡️ Navegación**

⬅️ **Anterior:** [Curso Principal](../README.md)  
➡️ **Siguiente:** [Parte II - Estructuras de Datos](../PARTE%20II%20-%20ESTRUCTURAS%20DE%20DATOS/README.md)  
🏠 **Curso:** [Curso Completo de JavaScript](../README.md)

---

**¡Domina esta parte y avanza en tu maestría de JavaScript!** 🚀

