# **GUÍAS DE TUTORIALES INTERACTIVOS JAVASCRIPT**

## **🎥 SERIE DE TUTORIALES: "JAVASCRIPT DESDE CERO"**

### **Tutorial 1: Configuración del Entorno de Desarrollo**
**Duración estimada: 15 minutos**

#### **🎯 Objetivos del Tutorial**
- Configurar VS Code para desarrollo JavaScript
- Instalar extensiones esenciales
- Configurar Node.js y NPM
- Crear el primer proyecto

#### **📋 Guión del Tutorial**

**[00:00 - 02:00] Introducción**
```
¡Hola! Bienvenidos al primer tutorial de nuestra serie "JavaScript desde Cero".
En este video aprenderemos a configurar nuestro entorno de desarrollo.

Al final de este tutorial tendrás:
✅ VS Code configurado y optimizado
✅ Node.js y NPM instalados
✅ Tu primer proyecto JavaScript funcionando
✅ Las mejores extensiones para productividad
```

**[02:00 - 05:00] Instalación de VS Code**
```
Paso 1: Descargar VS Code
- Ir a code.visualstudio.com
- Descargar la versión para tu sistema operativo
- Ejecutar el instalador

Paso 2: Primera configuración
- Abrir VS Code
- Configurar tema (recomiendo Dark+ o Material Theme)
- Configurar fuente (recomiendo Fira Code con ligaduras)
```

**[05:00 - 08:00] Extensiones Esenciales**
```
Extensiones que instalaremos:

1. ES7+ React/Redux/React-Native snippets
   - Snippets útiles para JavaScript moderno

2. Prettier - Code formatter
   - Formateo automático de código

3. ESLint
   - Detección de errores y mejores prácticas

4. Live Server
   - Servidor local para desarrollo

5. Bracket Pair Colorizer
   - Colores para identificar llaves y paréntesis

6. Auto Rename Tag
   - Renombrado automático de etiquetas HTML

7. Path Intellisense
   - Autocompletado de rutas de archivos

8. GitLens
   - Información avanzada de Git
```

**[08:00 - 12:00] Instalación de Node.js**
```
Paso 1: Descargar Node.js
- Ir a nodejs.org
- Descargar la versión LTS (Long Term Support)
- Ejecutar el instalador

Paso 2: Verificar instalación
- Abrir terminal/cmd
- Ejecutar: node --version
- Ejecutar: npm --version

Paso 3: Configurar NPM (opcional)
- npm config set init-author-name "Tu Nombre"
- npm config set init-license "MIT"
```

**[12:00 - 15:00] Primer Proyecto**
```
Crear estructura del proyecto:

mi-primer-proyecto/
├── index.html
├── css/
│   └── styles.css
├── js/
│   └── script.js
└── package.json

Código de ejemplo para mostrar:

// index.html
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mi Primer Proyecto JavaScript</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <h1 id="titulo">¡Hola JavaScript!</h1>
    <button id="boton">Hacer clic</button>
    <script src="js/script.js"></script>
</body>
</html>

// js/script.js
document.addEventListener('DOMContentLoaded', function() {
    const titulo = document.getElementById('titulo');
    const boton = document.getElementById('boton');
    
    boton.addEventListener('click', function() {
        titulo.textContent = '¡JavaScript funciona!';
        titulo.style.color = 'blue';
    });
});
```

#### **🎯 Ejercicios Prácticos**
1. Instalar todas las extensiones mencionadas
2. Crear el proyecto de ejemplo
3. Modificar el código para cambiar el color de fondo al hacer clic
4. Agregar un contador de clics

---

### **Tutorial 2: Variables y Tipos de Datos**
**Duración estimada: 20 minutos**

#### **🎯 Objetivos del Tutorial**
- Entender los tipos de datos primitivos
- Aprender a declarar variables correctamente
- Conocer las diferencias entre var, let y const
- Practicar con ejemplos interactivos

#### **📋 Guión del Tutorial**

**[00:00 - 03:00] Introducción a Variables**
```
Las variables son contenedores para almacenar datos.
En JavaScript tenemos 3 formas de declararlas:

1. var (antigua, evitar usar)
2. let (para variables que cambian)
3. const (para constantes)

Ejemplo en vivo:
```

**[03:00 - 08:00] Tipos de Datos Primitivos**
```javascript
// Demostración interactiva en consola

// String (texto)
let nombre = "Juan";
let apellido = 'Pérez';
let mensaje = `Hola ${nombre} ${apellido}`;

console.log(typeof nombre); // "string"

// Number (números)
let edad = 25;
let precio = 19.99;
let infinito = Infinity;

console.log(typeof edad); // "number"

// Boolean (verdadero/falso)
let esEstudiante = true;
let esMayor = false;

console.log(typeof esEstudiante); // "boolean"

// Undefined (sin valor asignado)
let sinValor;
console.log(sinValor); // undefined
console.log(typeof sinValor); // "undefined"

// Null (valor nulo intencionalmente)
let valorNulo = null;
console.log(typeof valorNulo); // "object" (peculiaridad de JS)

// Symbol (identificador único)
let simbolo = Symbol('id');
console.log(typeof simbolo); // "symbol"

// BigInt (números muy grandes)
let numeroGrande = 123456789012345678901234567890n;
console.log(typeof numeroGrande); // "bigint"
```

**[08:00 - 12:00] Scope y Hoisting**
```javascript
// Demostrar diferencias entre var, let, const

// VAR - Problemas de scope
function ejemploVar() {
    if (true) {
        var x = 1;
    }
    console.log(x); // 1 - accesible fuera del bloque
}

// LET - Block scope
function ejemploLet() {
    if (true) {
        let y = 1;
    }
    // console.log(y); // Error: y no está definida
}

// CONST - Inmutable
const PI = 3.14159;
// PI = 3.14; // Error: no se puede reasignar

// Pero los objetos const sí se pueden modificar
const persona = { nombre: "Ana" };
persona.edad = 25; // ✅ Válido
persona.nombre = "María"; // ✅ Válido
// persona = {}; // ❌ Error
```

**[12:00 - 17:00] Conversión de Tipos**
```javascript
// Conversión implícita (coerción)
console.log("5" + 3); // "53" (string)
console.log("5" - 3); // 2 (number)
console.log("5" * 3); // 15 (number)
console.log(true + 1); // 2 (number)

// Conversión explícita
let numero = "123";
console.log(Number(numero)); // 123
console.log(parseInt(numero)); // 123
console.log(parseFloat("123.45")); // 123.45

let valor = 123;
console.log(String(valor)); // "123"
console.log(valor.toString()); // "123"

console.log(Boolean(1)); // true
console.log(Boolean(0)); // false
console.log(Boolean("")); // false
console.log(Boolean("hola")); // true
```

**[17:00 - 20:00] Ejercicio Práctico**
```javascript
// Crear una calculadora simple
function calculadora() {
    // Pedir datos al usuario
    const num1 = parseFloat(prompt("Ingresa el primer número:"));
    const operador = prompt("Ingresa el operador (+, -, *, /):");
    const num2 = parseFloat(prompt("Ingresa el segundo número:"));
    
    let resultado;
    
    switch (operador) {
        case '+':
            resultado = num1 + num2;
            break;
        case '-':
            resultado = num1 - num2;
            break;
        case '*':
            resultado = num1 * num2;
            break;
        case '/':
            if (num2 !== 0) {
                resultado = num1 / num2;
            } else {
                alert("Error: División por cero");
                return;
            }
            break;
        default:
            alert("Operador no válido");
            return;
    }
    
    alert(`Resultado: ${num1} ${operador} ${num2} = ${resultado}`);
}

// Llamar la función
calculadora();
```

#### **🎯 Ejercicios para Practicar**
1. Crear variables de todos los tipos primitivos
2. Experimentar con conversiones de tipos
3. Crear un programa que calcule el área de un círculo
4. Hacer un conversor de temperaturas (Celsius a Fahrenheit)

---

### **Tutorial 3: Funciones Avanzadas**
**Duración estimada: 25 minutos**

#### **🎯 Objetivos del Tutorial**
- Dominar diferentes formas de declarar funciones
- Entender closures y scope
- Aprender sobre funciones de alto orden
- Implementar callbacks y arrow functions

#### **📋 Estructura del Tutorial**

**[00:00 - 05:00] Tipos de Funciones**
```javascript
// 1. Declaración de función (function declaration)
function saludar(nombre) {
    return `Hola ${nombre}!`;
}

// 2. Expresión de función (function expression)
const despedir = function(nombre) {
    return `Adiós ${nombre}!`;
};

// 3. Arrow function (función flecha)
const multiplicar = (a, b) => a * b;

// 4. Arrow function con cuerpo
const calcularArea = (radio) => {
    const pi = 3.14159;
    return pi * radio * radio;
};

// 5. IIFE (Immediately Invoked Function Expression)
(function() {
    console.log("Esta función se ejecuta inmediatamente");
})();
```

**[05:00 - 10:00] Parámetros y Argumentos**
```javascript
// Parámetros por defecto
function crearUsuario(nombre, edad = 18, activo = true) {
    return {
        nombre,
        edad,
        activo,
        fechaCreacion: new Date()
    };
}

// Rest parameters
function sumar(...numeros) {
    return numeros.reduce((total, num) => total + num, 0);
}

console.log(sumar(1, 2, 3, 4, 5)); // 15

// Destructuring en parámetros
function mostrarPersona({ nombre, edad, ciudad = "No especificada" }) {
    console.log(`${nombre}, ${edad} años, de ${ciudad}`);
}

const persona = { nombre: "Ana", edad: 25, ciudad: "Madrid" };
mostrarPersona(persona);
```

**[10:00 - 15:00] Closures**
```javascript
// Ejemplo básico de closure
function crearContador() {
    let contador = 0;
    
    return function() {
        contador++;
        return contador;
    };
}

const contador1 = crearContador();
const contador2 = crearContador();

console.log(contador1()); // 1
console.log(contador1()); // 2
console.log(contador2()); // 1 (independiente)

// Closure práctico: módulo privado
const calculadora = (function() {
    let historial = [];
    
    return {
        sumar: function(a, b) {
            const resultado = a + b;
            historial.push(`${a} + ${b} = ${resultado}`);
            return resultado;
        },
        
        restar: function(a, b) {
            const resultado = a - b;
            historial.push(`${a} - ${b} = ${resultado}`);
            return resultado;
        },
        
        obtenerHistorial: function() {
            return [...historial]; // Copia del array
        },
        
        limpiarHistorial: function() {
            historial = [];
        }
    };
})();
```

**[15:00 - 20:00] Funciones de Alto Orden**
```javascript
// Función que recibe otra función como parámetro
function procesarArray(array, callback) {
    const resultado = [];
    for (let i = 0; i < array.length; i++) {
        resultado.push(callback(array[i], i, array));
    }
    return resultado;
}

const numeros = [1, 2, 3, 4, 5];

// Usar con diferentes callbacks
const cuadrados = procesarArray(numeros, x => x * x);
const dobles = procesarArray(numeros, x => x * 2);
const indices = procesarArray(numeros, (valor, indice) => indice);

// Función que retorna otra función
function crearMultiplicador(factor) {
    return function(numero) {
        return numero * factor;
    };
}

const multiplicarPor3 = crearMultiplicador(3);
const multiplicarPor10 = crearMultiplicador(10);

console.log(multiplicarPor3(5)); // 15
console.log(multiplicarPor10(5)); // 50
```

**[20:00 - 25:00] Proyecto Práctico**
```javascript
// Sistema de validación con funciones
const validadores = {
    email: (email) => {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    },
    
    password: (password) => {
        return password.length >= 8 && 
               /[A-Z]/.test(password) && 
               /[a-z]/.test(password) && 
               /\d/.test(password);
    },
    
    telefono: (telefono) => {
        const regex = /^\+?[\d\s-()]{10,}$/;
        return regex.test(telefono);
    }
};

function validarFormulario(datos, reglas) {
    const errores = [];
    
    Object.keys(reglas).forEach(campo => {
        const valor = datos[campo];
        const validador = reglas[campo];
        
        if (!validador(valor)) {
            errores.push(`${campo} no es válido`);
        }
    });
    
    return {
        esValido: errores.length === 0,
        errores
    };
}

// Uso del sistema
const datosUsuario = {
    email: "<EMAIL>",
    password: "MiPassword123",
    telefono: "+34-123-456-789"
};

const resultado = validarFormulario(datosUsuario, validadores);
console.log(resultado);
```

#### **🎯 Ejercicios Interactivos**
1. Crear un sistema de descuentos usando closures
2. Implementar un debounce function
3. Crear un sistema de plugins usando funciones de alto orden
4. Desarrollar un mini-framework de validación

---

## **🎮 EJERCICIOS INTERACTIVOS ONLINE**

### **Plataforma de Práctica: "JS Playground"**

#### **Ejercicio 1: Debugging Challenge**
```javascript
// Código con errores para que el estudiante corrija
function calcularPromedio(numeros) {
    let suma = 0;
    for (let i = 0; i <= numeros.length; i++) { // Error: <= debería ser <
        suma += numeros[i];
    }
    return suma / numeros.lenght; // Error: lenght debería ser length
}

// Instrucciones:
// 1. Encuentra y corrige los 2 errores en el código
// 2. Prueba la función con [10, 20, 30, 40, 50]
// 3. El resultado debería ser 30
```

#### **Ejercicio 2: Code Golf**
```javascript
// Desafío: Escribir la función más corta posible que:
// - Tome un array de números
// - Retorne solo los números pares
// - Los multiplique por 2
// - Los ordene de mayor a menor

// Solución larga (para principiantes):
function procesarNumeros(numeros) {
    const pares = numeros.filter(num => num % 2 === 0);
    const dobles = pares.map(num => num * 2);
    const ordenados = dobles.sort((a, b) => b - a);
    return ordenados;
}

// Solución corta (para avanzados):
const procesarNumeros = nums => nums.filter(n => n % 2 === 0).map(n => n * 2).sort((a, b) => b - a);

// Prueba con: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
// Resultado esperado: [20, 16, 12, 8, 4]
```

#### **Ejercicio 3: Algoritmo del Día**
```javascript
// Implementar el algoritmo FizzBuzz
// Reglas:
// - Números divisibles por 3: "Fizz"
// - Números divisibles por 5: "Buzz"  
// - Números divisibles por ambos: "FizzBuzz"
// - Otros números: el número mismo

function fizzBuzz(n) {
    // Tu código aquí
    // Pista: usa el operador módulo (%)
}

// Prueba: fizzBuzz(15) debería retornar:
// [1, 2, "Fizz", 4, "Buzz", "Fizz", 7, 8, "Fizz", "Buzz", 11, "Fizz", 13, 14, "FizzBuzz"]
```

Estas guías de tutoriales proporcionan una estructura completa para crear contenido multimedia educativo, con objetivos claros, ejemplos prácticos y ejercicios interactivos que refuerzan el aprendizaje.
