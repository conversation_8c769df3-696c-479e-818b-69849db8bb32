/**
 * EJEMPLO BÁSICO - Historia y Evolución de JavaScript
 * ===================================================
 * 
 * Este ejemplo muestra la evolución de JavaScript comparando
 * sintaxis de diferentes versiones del lenguaje.
 */

console.log('=== EVOLUCIÓN DE JAVASCRIPT: COMPARACIÓN DE SINTAXIS ===\n');

// ============================================================================
// 1. VARIABLES: var vs let vs const
// ============================================================================

console.log('1. EVOLUCIÓN DE VARIABLES:');
console.log('------------------------');

// ES3/ES5: Solo var
console.log('ES3/ES5 - Solo var:');
var oldVariable = 'Soy una variable var';
console.log(oldVariable);

// ES6+: let y const
console.log('\nES6+ - let y const:');
let mutableVariable = 'Puedo cambiar';
const immutableVariable = 'No puedo cambiar';
console.log(mutableVariable);
console.log(immutableVariable);

// ============================================================================
// 2. FUNCIONES: function vs arrow functions
// ============================================================================

console.log('\n2. EVOLUCIÓN DE FUNCIONES:');
console.log('-------------------------');

// ES3/ES5: Function declarations y expressions
console.log('ES3/ES5 - Funciones tradicionales:');
function oldFunction(name) {
    return 'Hola, ' + name + '!';
}

var oldFunctionExpression = function(a, b) {
    return a + b;
};

console.log(oldFunction('JavaScript'));
console.log('Suma:', oldFunctionExpression(5, 3));

// ES6+: Arrow functions
console.log('\nES6+ - Arrow functions:');
const newFunction = (name) => `Hola, ${name}!`;
const add = (a, b) => a + b;
const multiply = (a, b) => {
    const result = a * b;
    return result;
};

console.log(newFunction('JavaScript Moderno'));
console.log('Suma:', add(5, 3));
console.log('Multiplicación:', multiply(4, 7));

// ============================================================================
// 3. STRINGS: concatenación vs template literals
// ============================================================================

console.log('\n3. EVOLUCIÓN DE STRINGS:');
console.log('-----------------------');

const name = 'JavaScript';
const year = 2024;

// ES3/ES5: Concatenación
console.log('ES3/ES5 - Concatenación:');
var oldMessage = 'Hola, soy ' + name + ' y estoy en el año ' + year;
console.log(oldMessage);

// ES6+: Template literals
console.log('\nES6+ - Template literals:');
const newMessage = `Hola, soy ${name} y estoy en el año ${year}`;
console.log(newMessage);

// Template literals multilínea
const multilineMessage = `
    Este es un mensaje
    que abarca múltiples
    líneas de forma natural
`;
console.log('Multilínea:', multilineMessage.trim());

// ============================================================================
// 4. OBJETOS: ES5 vs ES6+ syntax
// ============================================================================

console.log('\n4. EVOLUCIÓN DE OBJETOS:');
console.log('-----------------------');

// ES5: Sintaxis tradicional
console.log('ES5 - Objetos tradicionales:');
var oldPerson = {
    name: 'Juan',
    age: 30,
    greet: function() {
        return 'Hola, soy ' + this.name;
    }
};
console.log(oldPerson.greet());

// ES6+: Sintaxis moderna
console.log('\nES6+ - Objetos modernos:');
const modernPerson = {
    name: 'María',
    age: 25,
    // Method shorthand
    greet() {
        return `Hola, soy ${this.name}`;
    },
    // Computed property names
    [`year${year}`]: 'Propiedad dinámica'
};
console.log(modernPerson.greet());
console.log('Propiedad dinámica:', modernPerson[`year${year}`]);

// ============================================================================
// 5. ARRAYS: métodos tradicionales vs modernos
// ============================================================================

console.log('\n5. EVOLUCIÓN DE ARRAYS:');
console.log('----------------------');

const numbers = [1, 2, 3, 4, 5];

// ES3: Bucles tradicionales
console.log('ES3 - Bucle for tradicional:');
var doubled1 = [];
for (var i = 0; i < numbers.length; i++) {
    doubled1.push(numbers[i] * 2);
}
console.log('Duplicados:', doubled1);

// ES5: Métodos funcionales
console.log('\nES5 - Métodos funcionales:');
var doubled2 = numbers.map(function(num) {
    return num * 2;
});
console.log('Duplicados:', doubled2);

// ES6+: Arrow functions con métodos
console.log('\nES6+ - Arrow functions:');
const doubled3 = numbers.map(num => num * 2);
const evens = numbers.filter(num => num % 2 === 0);
const sum = numbers.reduce((acc, num) => acc + num, 0);

console.log('Duplicados:', doubled3);
console.log('Pares:', evens);
console.log('Suma:', sum);

// ============================================================================
// 6. DESTRUCTURING: ES6+ feature
// ============================================================================

console.log('\n6. DESTRUCTURING (ES6+):');
console.log('------------------------');

// Array destructuring
const colors = ['rojo', 'verde', 'azul'];
const [primary, secondary, tertiary] = colors;
console.log('Colores:', { primary, secondary, tertiary });

// Object destructuring
const user = { 
    username: 'developer', 
    email: '<EMAIL>', 
    age: 28 
};
const { username, email } = user;
console.log('Usuario:', { username, email });

// ============================================================================
// 7. PROMISES vs CALLBACKS: Evolución del código asíncrono
// ============================================================================

console.log('\n7. EVOLUCIÓN DEL CÓDIGO ASÍNCRONO:');
console.log('----------------------------------');

// Simulación de operación asíncrona
function simulateAsyncOperation(callback) {
    setTimeout(() => {
        callback(null, 'Operación completada');
    }, 100);
}

// ES5: Callbacks
console.log('ES5 - Callbacks:');
simulateAsyncOperation(function(error, result) {
    if (error) {
        console.log('Error:', error);
    } else {
        console.log('Resultado:', result);
    }
});

// ES6: Promises
console.log('\nES6 - Promises:');
const promiseOperation = () => {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            resolve('Operación con Promise completada');
        }, 100);
    });
};

promiseOperation()
    .then(result => console.log('Resultado:', result))
    .catch(error => console.log('Error:', error));

// ES2017: async/await
console.log('\nES2017 - async/await:');
async function modernAsyncOperation() {
    try {
        const result = await promiseOperation();
        console.log('Resultado:', result);
    } catch (error) {
        console.log('Error:', error);
    }
}

modernAsyncOperation();

// ============================================================================
// 8. CLASES: ES6+ vs function constructors
// ============================================================================

console.log('\n8. EVOLUCIÓN DE CLASES:');
console.log('----------------------');

// ES5: Function constructors
console.log('ES5 - Function constructors:');
function OldAnimal(name) {
    this.name = name;
}

OldAnimal.prototype.speak = function() {
    return this.name + ' hace un sonido';
};

var oldDog = new OldAnimal('Rex');
console.log(oldDog.speak());

// ES6+: Classes
console.log('\nES6+ - Classes:');
class ModernAnimal {
    constructor(name) {
        this.name = name;
    }
    
    speak() {
        return `${this.name} hace un sonido`;
    }
}

class Dog extends ModernAnimal {
    speak() {
        return `${this.name} ladra`;
    }
}

const modernDog = new Dog('Max');
console.log(modernDog.speak());

// ============================================================================
// RESUMEN
// ============================================================================

console.log('\n=== RESUMEN DE LA EVOLUCIÓN ===');
console.log('JavaScript ha evolucionado enormemente:');
console.log('• Sintaxis más limpia y expresiva');
console.log('• Mejor manejo de asincronía');
console.log('• Características de programación moderna');
console.log('• Mayor legibilidad y mantenibilidad');
console.log('• Ecosistema más robusto');

console.log('\n¡JavaScript sigue evolucionando cada año! 🚀');
