/**
 * MODELO DE BADGE
 * ===============
 * 
 * Sistema de gamificación con badges y achievements para CodeCraft Academy
 * Incluye criterios automáticos, rareza y sistema de progreso
 */

const mongoose = require('mongoose');

const badgeSchema = new mongoose.Schema({
    // Información básica del badge
    name: {
        type: String,
        required: [true, 'El nombre del badge es requerido'],
        unique: true,
        trim: true
    },
    
    slug: {
        type: String,
        required: true,
        unique: true,
        lowercase: true
    },
    
    description: {
        type: String,
        required: [true, 'La descripción es requerida'],
        maxlength: [500, 'La descripción no puede exceder 500 caracteres']
    },
    
    shortDescription: {
        type: String,
        required: true,
        maxlength: [100, 'La descripción corta no puede exceder 100 caracteres']
    },
    
    // Categorización
    category: {
        type: String,
        required: true,
        enum: [
            'learning',      // Badges de aprendizaje
            'achievement',   // Logros específicos
            'milestone',     // Hitos importantes
            'skill',         // Habilidades específicas
            'social',        // Interacción social
            'time',          // Basados en tiempo
            'quality',       // Calidad del trabajo
            'special',       // Eventos especiales
            'instructor',    // Para instructores
            'community'      // Contribución a la comunidad
        ]
    },
    
    subcategory: {
        type: String,
        enum: [
            // Learning
            'first_steps', 'course_completion', 'language_mastery', 'concept_understanding',
            // Achievement
            'perfect_score', 'speed_completion', 'problem_solving', 'creativity',
            // Milestone
            'study_streak', 'total_hours', 'courses_completed', 'projects_finished',
            // Skill
            'javascript', 'python', 'react', 'nodejs', 'algorithms', 'databases',
            // Social
            'helping_others', 'forum_participation', 'peer_review', 'mentoring',
            // Time
            'early_bird', 'night_owl', 'consistent_learner', 'marathon_session',
            // Quality
            'clean_code', 'best_practices', 'documentation', 'testing',
            // Special
            'beta_tester', 'launch_day', 'anniversary', 'holiday_special',
            // Instructor
            'course_creator', 'student_favorite', 'expert_reviewer',
            // Community
            'contributor', 'bug_reporter', 'feature_suggester'
        ]
    },
    
    // Rareza y valor
    rarity: {
        type: String,
        enum: ['common', 'uncommon', 'rare', 'epic', 'legendary'],
        default: 'common'
    },
    
    points: {
        type: Number,
        required: true,
        min: 1,
        max: 10000
    },
    
    // Diseño visual
    design: {
        icon: {
            type: String,
            required: true // URL del icono o nombre del icono
        },
        color: {
            primary: {
                type: String,
                default: '#f7df1e'
            },
            secondary: {
                type: String,
                default: '#333333'
            },
            background: {
                type: String,
                default: '#ffffff'
            }
        },
        shape: {
            type: String,
            enum: ['circle', 'square', 'hexagon', 'star', 'shield', 'diamond'],
            default: 'circle'
        },
        animation: {
            type: String,
            enum: ['none', 'glow', 'pulse', 'bounce', 'rotate', 'sparkle'],
            default: 'none'
        },
        imageUrl: String, // URL de imagen personalizada
        thumbnailUrl: String
    },
    
    // Criterios para obtener el badge
    criteria: {
        type: {
            type: String,
            enum: [
                'automatic',     // Se otorga automáticamente
                'manual',        // Requiere revisión manual
                'nomination',    // Requiere nominación
                'application'    // Requiere aplicación
            ],
            default: 'automatic'
        },
        
        // Condiciones automáticas
        conditions: {
            // Progreso en cursos
            coursesCompleted: {
                min: Number,
                max: Number,
                specific: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Course' }]
            },
            
            // Puntuaciones
            averageScore: {
                min: Number,
                max: Number
            },
            
            perfectScores: {
                min: Number
            },
            
            // Tiempo de estudio
            totalStudyHours: {
                min: Number,
                max: Number
            },
            
            studyStreak: {
                min: Number // días consecutivos
            },
            
            // Ejercicios y proyectos
            exercisesCompleted: {
                min: Number
            },
            
            projectsCompleted: {
                min: Number
            },
            
            // Habilidades específicas
            skillsRequired: [{
                name: String,
                level: {
                    type: String,
                    enum: ['basic', 'intermediate', 'advanced', 'expert']
                }
            }],
            
            // Lenguajes de programación
            languagesLearned: {
                min: Number,
                specific: [String] // ['javascript', 'python', etc.]
            },
            
            // Actividad social
            forumPosts: {
                min: Number
            },
            
            helpfulAnswers: {
                min: Number
            },
            
            peerReviews: {
                min: Number
            },
            
            // Fechas especiales
            dateRange: {
                start: Date,
                end: Date
            },
            
            // Condiciones especiales
            specialConditions: [{
                type: String,
                value: mongoose.Schema.Types.Mixed
            }]
        },
        
        // Descripción legible de los criterios
        humanReadable: {
            type: String,
            required: true
        }
    },
    
    // Configuración de disponibilidad
    availability: {
        isActive: {
            type: Boolean,
            default: true
        },
        
        startDate: Date,
        endDate: Date,
        
        // Límite de otorgamientos
        maxAwards: {
            type: Number,
            default: null // null = sin límite
        },
        
        currentAwards: {
            type: Number,
            default: 0
        },
        
        // Restricciones
        restrictions: {
            userRoles: [String], // Solo para ciertos roles
            subscriptionPlans: [String], // Solo para ciertos planes
            regions: [String], // Solo para ciertas regiones
            firstTimeOnly: Boolean // Solo se puede obtener una vez por usuario
        }
    },
    
    // Prerrequisitos
    prerequisites: [{
        badgeId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Badge'
        },
        required: {
            type: Boolean,
            default: true
        }
    }],
    
    // Recompensas adicionales
    rewards: {
        experienceBonus: {
            type: Number,
            default: 0
        },
        
        unlocks: [{
            type: {
                type: String,
                enum: ['course', 'feature', 'content', 'discount', 'access']
            },
            resourceId: String,
            description: String
        }],
        
        discounts: [{
            type: {
                type: String,
                enum: ['course', 'subscription', 'merchandise']
            },
            percentage: Number,
            amount: Number,
            validUntil: Date
        }]
    },
    
    // Estadísticas
    stats: {
        totalAwarded: {
            type: Number,
            default: 0
        },
        
        uniqueRecipients: {
            type: Number,
            default: 0
        },
        
        averageTimeToEarn: {
            type: Number, // en días
            default: 0
        },
        
        popularityScore: {
            type: Number,
            default: 0
        },
        
        difficultyRating: {
            type: Number,
            min: 1,
            max: 10,
            default: 5
        }
    },
    
    // Metadatos
    metadata: {
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        
        version: {
            type: String,
            default: '1.0.0'
        },
        
        tags: [String],
        
        isHidden: {
            type: Boolean,
            default: false
        },
        
        isFeatured: {
            type: Boolean,
            default: false
        },
        
        sortOrder: {
            type: Number,
            default: 0
        }
    }

}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Índices
badgeSchema.index({ slug: 1 }, { unique: true });
badgeSchema.index({ category: 1, subcategory: 1 });
badgeSchema.index({ rarity: 1 });
badgeSchema.index({ 'availability.isActive': 1 });
badgeSchema.index({ 'stats.totalAwarded': -1 });
badgeSchema.index({ 'metadata.isFeatured': 1 });
badgeSchema.index({ points: -1 });

// Virtuals
badgeSchema.virtual('isAvailable').get(function() {
    if (!this.availability.isActive) return false;
    
    const now = new Date();
    if (this.availability.startDate && now < this.availability.startDate) return false;
    if (this.availability.endDate && now > this.availability.endDate) return false;
    
    if (this.availability.maxAwards && this.availability.currentAwards >= this.availability.maxAwards) {
        return false;
    }
    
    return true;
});

badgeSchema.virtual('rarityLevel').get(function() {
    const levels = {
        'common': 1,
        'uncommon': 2,
        'rare': 3,
        'epic': 4,
        'legendary': 5
    };
    return levels[this.rarity] || 1;
});

badgeSchema.virtual('completionRate').get(function() {
    if (!this.availability.maxAwards) return null;
    return Math.round((this.availability.currentAwards / this.availability.maxAwards) * 100);
});

// Middleware pre-save
badgeSchema.pre('save', function(next) {
    // Generar slug si no existe
    if (!this.slug && this.name) {
        this.slug = this.name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '');
    }
    
    // Calcular popularityScore
    this.stats.popularityScore = this.calculatePopularityScore();
    
    next();
});

// Métodos de instancia
badgeSchema.methods.calculatePopularityScore = function() {
    let score = 0;
    
    // Basado en número de otorgamientos
    score += Math.min(this.stats.totalAwarded * 0.1, 50);
    
    // Basado en rareza (badges más raros son más populares cuando se obtienen)
    const rarityMultiplier = {
        'common': 1,
        'uncommon': 1.2,
        'rare': 1.5,
        'epic': 2,
        'legendary': 3
    };
    score *= rarityMultiplier[this.rarity] || 1;
    
    // Penalizar si es muy fácil de obtener
    if (this.stats.averageTimeToEarn < 1) {
        score *= 0.8;
    }
    
    return Math.round(score);
};

badgeSchema.methods.checkEligibility = function(user, userProgress) {
    if (!this.isAvailable) return false;
    
    const conditions = this.criteria.conditions;
    
    // Verificar prerrequisitos
    if (this.prerequisites.length > 0) {
        const userBadges = user.gamification.badges.map(b => b.badgeId.toString());
        const requiredBadges = this.prerequisites
            .filter(p => p.required)
            .map(p => p.badgeId.toString());
        
        const hasAllRequired = requiredBadges.every(badgeId => userBadges.includes(badgeId));
        if (!hasAllRequired) return false;
    }
    
    // Verificar restricciones
    if (this.availability.restrictions.userRoles.length > 0) {
        if (!this.availability.restrictions.userRoles.includes(user.role)) return false;
    }
    
    if (this.availability.restrictions.subscriptionPlans.length > 0) {
        if (!this.availability.restrictions.subscriptionPlans.includes(user.subscription.plan)) return false;
    }
    
    if (this.availability.restrictions.firstTimeOnly) {
        const alreadyHas = user.gamification.badges.some(b => b.badgeId.toString() === this._id.toString());
        if (alreadyHas) return false;
    }
    
    // Verificar condiciones específicas
    if (conditions.coursesCompleted) {
        if (conditions.coursesCompleted.min && user.academicProgress.totalCoursesCompleted < conditions.coursesCompleted.min) {
            return false;
        }
        if (conditions.coursesCompleted.max && user.academicProgress.totalCoursesCompleted > conditions.coursesCompleted.max) {
            return false;
        }
    }
    
    if (conditions.totalStudyHours) {
        const totalHours = user.academicProgress.totalStudyTimeMinutes / 60;
        if (conditions.totalStudyHours.min && totalHours < conditions.totalStudyHours.min) {
            return false;
        }
    }
    
    if (conditions.studyStreak) {
        if (conditions.studyStreak.min && user.academicProgress.currentStreak < conditions.studyStreak.min) {
            return false;
        }
    }
    
    // Verificar fechas especiales
    if (conditions.dateRange) {
        const now = new Date();
        if (conditions.dateRange.start && now < conditions.dateRange.start) return false;
        if (conditions.dateRange.end && now > conditions.dateRange.end) return false;
    }
    
    return true;
};

badgeSchema.methods.award = function(userId) {
    this.stats.totalAwarded += 1;
    this.availability.currentAwards += 1;
    
    // Actualizar estadísticas si es necesario
    return this.save();
};

badgeSchema.methods.revoke = function() {
    if (this.stats.totalAwarded > 0) {
        this.stats.totalAwarded -= 1;
    }
    if (this.availability.currentAwards > 0) {
        this.availability.currentAwards -= 1;
    }
    
    return this.save();
};

// Métodos estáticos
badgeSchema.statics.findAvailable = function() {
    return this.find({
        'availability.isActive': true,
        'metadata.isHidden': false
    });
};

badgeSchema.statics.findByCategory = function(category) {
    return this.find({
        category,
        'availability.isActive': true,
        'metadata.isHidden': false
    });
};

badgeSchema.statics.findByRarity = function(rarity) {
    return this.find({
        rarity,
        'availability.isActive': true,
        'metadata.isHidden': false
    });
};

badgeSchema.statics.findFeatured = function() {
    return this.find({
        'metadata.isFeatured': true,
        'availability.isActive': true
    }).sort({ 'metadata.sortOrder': 1 });
};

badgeSchema.statics.findPopular = function(limit = 10) {
    return this.find({
        'availability.isActive': true,
        'metadata.isHidden': false
    })
    .sort({ 'stats.popularityScore': -1 })
    .limit(limit);
};

badgeSchema.statics.findRare = function() {
    return this.find({
        rarity: { $in: ['rare', 'epic', 'legendary'] },
        'availability.isActive': true,
        'metadata.isHidden': false
    }).sort({ 'stats.totalAwarded': 1 });
};

badgeSchema.statics.checkUserEligibility = function(userId) {
    // Esta función sería llamada periódicamente para verificar
    // si un usuario es elegible para nuevos badges
    return this.find({
        'availability.isActive': true,
        'criteria.type': 'automatic'
    });
};

module.exports = mongoose.model('Badge', badgeSchema);
