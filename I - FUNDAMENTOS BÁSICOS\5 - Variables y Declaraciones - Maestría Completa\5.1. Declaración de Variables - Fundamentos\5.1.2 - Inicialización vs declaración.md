# 5.1.2 - Inicialización vs declaración

### Introducción

La declaración y la inicialización son dos conceptos fundamentales pero distintos en JavaScript.
La declaración crea la variable y la registra en el scope actual.
La inicialización asigna un valor inicial a la variable declarada.
Pueden ocurrir simultáneamente o en momentos diferentes.
Entender esta diferencia es crucial para evitar errores comunes.
¿Sabes cuándo una variable está declarada pero no inicializada?

### Código de Ejemplo
```javascript
// Inicialización vs Declaración en JavaScript

// 1. DECLARACIÓN SIN INICIALIZACIÓN
console.log("=== DECLARACIÓN SIN INICIALIZACIÓN ===");

// Con let - variable declarada pero no inicializada
let nombre;
console.log("nombre después de declaración:", nombre); // undefined
console.log("typeof nombre:", typeof nombre); // undefined

// Con var - variable declarada pero no inicializada
var edad;
console.log("edad después de declaración:", edad); // undefined
console.log("typeof edad:", typeof edad); // undefined

// Con const - ERROR: debe inicializarse en la declaración
// const PI; // SyntaxError: Missing initializer in const declaration

// 2. DECLARACIÓN CON INICIALIZACIÓN
console.log("\n=== DECLARACIÓN CON INICIALIZACIÓN ===");

let apellido = "García"; // Declaración + inicialización
var salario = 50000; // Declaración + inicialización
const GRAVEDAD = 9.81; // Declaración + inicialización (obligatoria)

console.log("apellido:", apellido);
console.log("salario:", salario);
console.log("GRAVEDAD:", GRAVEDAD);

// 3. INICIALIZACIÓN POSTERIOR
console.log("\n=== INICIALIZACIÓN POSTERIOR ===");

let ciudad; // Solo declaración
console.log("ciudad antes de inicialización:", ciudad);

ciudad = "Madrid"; // Inicialización posterior
console.log("ciudad después de inicialización:", ciudad);

// 4. MÚLTIPLES DECLARACIONES
console.log("\n=== MÚLTIPLES DECLARACIONES ===");

// Declaración múltiple sin inicialización
let a, b, c;
console.log("a, b, c:", a, b, c); // undefined, undefined, undefined

// Declaración múltiple con inicialización mixta
let x = 10, y, z = 30;
console.log("x, y, z:", x, y, z); // 10, undefined, 30

// Inicialización posterior de las no inicializadas
y = 20;
console.log("x, y, z después:", x, y, z); // 10, 20, 30

// 5. DIFERENCIAS EN EL COMPORTAMIENTO
console.log("\n=== DIFERENCIAS EN EL COMPORTAMIENTO ===");

// Verificar si una variable está declarada pero no inicializada
let estado;
if (estado === undefined) {
    console.log("Variable 'estado' está declarada pero no inicializada");
}

// Diferencia entre undefined y not declared
try {
    console.log(variableNoDeclarada); // ReferenceError
} catch (error) {
    console.log("Error al acceder a variable no declarada:", error.message);
}

// 6. HOISTING Y DECLARACIÓN/INICIALIZACIÓN
console.log("\n=== HOISTING Y DECLARACIÓN/INICIALIZACIÓN ===");

// Con var - la declaración se eleva, la inicialización no
console.log("varHoisted antes de declaración:", varHoisted); // undefined
var varHoisted = "Valor hoisted";
console.log("varHoisted después de inicialización:", varHoisted);

// Con let/const - Temporal Dead Zone
try {
    console.log(letVariable); // ReferenceError
} catch (error) {
    console.log("Error con let en TDZ:", error.message);
}
let letVariable = "Valor let";

// 7. INICIALIZACIÓN CON DIFERENTES TIPOS
console.log("\n=== INICIALIZACIÓN CON DIFERENTES TIPOS ===");

let numero = 42; // Inicialización con number
let texto = "Hola"; // Inicialización con string
let booleano = true; // Inicialización con boolean
let objeto = { id: 1 }; // Inicialización con object
let array = [1, 2, 3]; // Inicialización con array
let funcion = function() { return "función"; }; // Inicialización con function

console.log("Tipos inicializados:");
console.log("numero:", numero, "- tipo:", typeof numero);
console.log("texto:", texto, "- tipo:", typeof texto);
console.log("booleano:", booleano, "- tipo:", typeof booleano);
console.log("objeto:", objeto, "- tipo:", typeof objeto);
console.log("array:", array, "- tipo:", typeof array);
console.log("funcion:", funcion, "- tipo:", typeof funcion);

// 8. REINICIALIZACIÓN
console.log("\n=== REINICIALIZACIÓN ===");

let variable = "Valor inicial";
console.log("Valor inicial:", variable);

variable = "Nuevo valor"; // Reinicialización
console.log("Después de reinicialización:", variable);

variable = 123; // Cambio de tipo
console.log("Después de cambio de tipo:", variable, "- tipo:", typeof variable);

// Con const no se puede reinicializar
const CONSTANTE = "Valor constante";
console.log("CONSTANTE:", CONSTANTE);

try {
    CONSTANTE = "Nuevo valor"; // TypeError
} catch (error) {
    console.log("Error al reinicializar const:", error.message);
}

// 9. PATRONES DE INICIALIZACIÓN
console.log("\n=== PATRONES DE INICIALIZACIÓN ===");

// Inicialización condicional
let configuracion = process?.env?.NODE_ENV || "development";
console.log("configuracion:", configuracion);

// Inicialización con valores por defecto
let opciones = {
    debug: true,
    timeout: 5000,
    retries: 3
};
console.log("opciones:", opciones);

// Inicialización lazy (perezosa)
let valorCostoso;
function getValorCostoso() {
    if (valorCostoso === undefined) {
        valorCostoso = "Cálculo costoso realizado";
        console.log("Inicialización lazy ejecutada");
    }
    return valorCostoso;
}

console.log("Primera llamada:", getValorCostoso());
console.log("Segunda llamada:", getValorCostoso());
```

### Salida en Consola
```
=== DECLARACIÓN SIN INICIALIZACIÓN ===
nombre después de declaración: undefined
typeof nombre: undefined
edad después de declaración: undefined
typeof edad: undefined

=== DECLARACIÓN CON INICIALIZACIÓN ===
apellido: García
salario: 50000
GRAVEDAD: 9.81

=== INICIALIZACIÓN POSTERIOR ===
ciudad antes de inicialización: undefined
ciudad después de inicialización: Madrid

=== MÚLTIPLES DECLARACIONES ===
a, b, c: undefined undefined undefined
x, y, z: 10 undefined 30
x, y, z después: 10 20 30

=== DIFERENCIAS EN EL COMPORTAMIENTO ===
Variable 'estado' está declarada pero no inicializada
Error al acceder a variable no declarada: variableNoDeclarada is not defined

=== HOISTING Y DECLARACIÓN/INICIALIZACIÓN ===
varHoisted antes de declaración: undefined
varHoisted después de inicialización: Valor hoisted
Error con let en TDZ: Cannot access 'letVariable' before initialization

=== INICIALIZACIÓN CON DIFERENTES TIPOS ===
Tipos inicializados:
numero: 42 - tipo: number
texto: Hola - tipo: string
booleano: true - tipo: boolean
objeto: { id: 1 } - tipo: object
array: [ 1, 2, 3 ] - tipo: object
funcion: [Function: funcion] - tipo: function

=== REINICIALIZACIÓN ===
Valor inicial: Valor inicial
Después de reinicialización: Nuevo valor
Después de cambio de tipo: 123 - tipo: number
CONSTANTE: Valor constante
Error al reinicializar const: Assignment to constant variable.

=== PATRONES DE INICIALIZACIÓN ===
configuracion: development
opciones: { debug: true, timeout: 5000, retries: 3 }
Inicialización lazy ejecutada
Primera llamada: Cálculo costoso realizado
Segunda llamada: Cálculo costoso realizado
```

### Diagrama de Flujo del Código
```mermaid
flowchart TD
    A[Inicio del Programa] --> B[Declaración sin Inicialización]
    B --> C[let nombre - undefined]
    B --> D[var edad - undefined]
    B --> E[const PI - ERROR sin inicialización]
    
    C --> F[Declaración con Inicialización]
    D --> F
    E --> F
    
    F --> G[let apellido = 'García']
    F --> H[var salario = 50000]
    F --> I[const GRAVEDAD = 9.81]
    
    G --> J[Inicialización Posterior]
    H --> J
    I --> J
    
    J --> K[let ciudad - declarada]
    K --> L[ciudad = 'Madrid' - inicializada]
    
    L --> M[Múltiples Declaraciones]
    M --> N[let a, b, c - todas undefined]
    M --> O[let x=10, y, z=30 - mixta]
    
    N --> P[Verificación de Estados]
    O --> P
    
    P --> Q[estado === undefined?]
    Q -->|Sí| R[Variable declarada no inicializada]
    Q -->|No| S[Variable inicializada]
    
    R --> T[Hoisting y TDZ]
    S --> T
    
    T --> U[var: declaración elevada]
    T --> V[let/const: Temporal Dead Zone]
    
    U --> W[Inicialización con Tipos]
    V --> W
    
    W --> X[number, string, boolean]
    W --> Y[object, array, function]
    
    X --> Z[Reinicialización]
    Y --> Z
    
    Z --> AA[let: permitida]
    Z --> BB[const: ERROR]
    
    AA --> CC[Patrones Avanzados]
    BB --> CC
    
    CC --> DD[Inicialización condicional]
    CC --> EE[Valores por defecto]
    CC --> FF[Lazy initialization]
    
    DD --> GG[Fin del Programa]
    EE --> GG
    FF --> GG
    
    style A fill:#4CAF50,stroke:#000,stroke-width:3px,color:#fff
    style E fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff
    style BB fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff
    style V fill:#FFD700,stroke:#000,stroke-width:3px,color:#000
    style GG fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph "Declaración"
        A1[Crear Variable]
        A2[Registrar en Scope]
        A3[Asignar undefined]
        A4[Reservar Memoria]
    end
    
    subgraph "Inicialización"
        B1[Asignar Valor]
        B2[Definir Tipo]
        B3[Establecer Estado]
        B4[Valor !== undefined]
    end
    
    subgraph "Estados Posibles"
        C1[Declarada + No Inicializada]
        C2[Declarada + Inicializada]
        C3[No Declarada]
        C4[undefined vs ReferenceError]
    end
    
    subgraph "Comportamientos"
        D1[var: Hoisting]
        D2[let/const: TDZ]
        D3[const: Inicialización Obligatoria]
        D4[Reinicialización]
    end
    
    A1 --> A2
    A2 --> A3
    A3 --> A4
    
    B1 --> B2
    B2 --> B3
    B3 --> B4
    
    A4 --> C1
    B4 --> C2
    C3 --> C4
    
    C1 --> D1
    C2 --> D2
    D2 --> D3
    D3 --> D4
    
    style A1 fill:#98FB98,stroke:#000,stroke-width:2px
    style B1 fill:#FFB6C1,stroke:#000,stroke-width:2px
    style C3 fill:#FF6347,stroke:#000,stroke-width:2px
    style D3 fill:#FFD700,stroke:#000,stroke-width:2px
```

### Casos de uso
1. **Declaración temprana**: Declarar variables al inicio del scope para claridad.
2. **Inicialización condicional**: Asignar valores basados en condiciones.
3. **Lazy loading**: Inicializar solo cuando se necesita el valor.
4. **Configuración por defecto**: Inicializar con valores predeterminados.
5. **Validación de entrada**: Declarar antes de validar y asignar.
6. **Bucles y contadores**: Declarar contadores antes de bucles.
7. **Estado de aplicación**: Declarar variables de estado sin valor inicial.
8. **Parámetros opcionales**: Manejar parámetros que pueden no existir.
9. **Cacheo de resultados**: Declarar cache sin inicializar hasta necesitarlo.
10. **Manejo de errores**: Declarar variables para capturar resultados de try/catch.

### Errores comunes
1. **Usar const sin inicializar**: Olvidar que const requiere inicialización.
2. **Confundir undefined con not declared**: No distinguir entre estados.
3. **Acceder antes de inicializar**: Usar variables en Temporal Dead Zone.
4. **Reinicializar const**: Intentar cambiar valor de constantes.
5. **No inicializar en bucles**: Usar contadores sin valor inicial.
6. **Declaraciones múltiples confusas**: Mezclar inicializadas y no inicializadas.
7. **Hoisting mal entendido**: Confundir declaración con inicialización en hoisting.
8. **Tipos inconsistentes**: Cambiar tipos sin control en reinicialización.
9. **Variables globales no inicializadas**: Crear variables globales sin valor.
10. **Inicialización tardía innecesaria**: No inicializar cuando se puede hacer inmediatamente.

### Recomendaciones
1. **Inicializa en la declaración cuando sea posible**: Evita estados undefined.
2. **Usa const por defecto**: Solo usa let cuando necesites reasignar.
3. **Declara variables cerca de su uso**: Minimiza el tiempo sin inicializar.
4. **Usa nombres descriptivos**: Indica si una variable puede estar sin inicializar.
5. **Valida antes de usar**: Verifica que las variables estén inicializadas.
6. **Evita var completamente**: Usa let/const para mejor control.
7. **Documenta inicialización lazy**: Explica por qué se inicializa tarde.
8. **Usa valores por defecto**: Proporciona valores sensatos en la inicialización.
9. **Agrupa declaraciones relacionadas**: Organiza declaraciones lógicamente.
10. **Considera el rendimiento**: Inicializa solo cuando sea necesario para valores costosos.

---

**Siguiente**: [5.1.3 - Undefined vs uninitialized](./5.1.3%20-%20Undefined%20vs%20uninitialized.md)  
**Anterior**: [5.1.1 - Conceptos básicos de variables](./5.1.1%20-%20Conceptos%20básicos%20de%20variables.md)  
**Índice**: [Variables y Declaraciones - Maestría Completa](../README.md)