/**
 * NAVEGACIÓN PRINCIPAL DE LA APP
 * ==============================
 * 
 * Sistema de navegación completo para la aplicación móvil
 * Incluye tabs, stack navigation y drawer
 */

import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {createDrawerNavigator} from '@react-navigation/drawer';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {useTheme} from 'react-native-paper';

// Screens
import HomeScreen from '../screens/HomeScreen';
import CoursesScreen from '../screens/CoursesScreen';
import CourseDetailScreen from '../screens/CourseDetailScreen';
import LessonScreen from '../screens/LessonScreen';
import ExerciseScreen from '../screens/ExerciseScreen';
import ProjectScreen from '../screens/ProjectScreen';
import ProgressScreen from '../screens/ProgressScreen';
import CommunityScreen from '../screens/CommunityScreen';
import ForumScreen from '../screens/ForumScreen';
import ForumPostScreen from '../screens/ForumPostScreen';
import ProfileScreen from '../screens/ProfileScreen';
import SettingsScreen from '../screens/SettingsScreen';
import CertificatesScreen from '../screens/CertificatesScreen';
import CertificateDetailScreen from '../screens/CertificateDetailScreen';
import SearchScreen from '../screens/SearchScreen';
import NotificationsScreen from '../screens/NotificationsScreen';
import DownloadsScreen from '../screens/DownloadsScreen';
import HelpScreen from '../screens/HelpScreen';
import AboutScreen from '../screens/AboutScreen';
import CodeEditorScreen from '../screens/CodeEditorScreen';
import VideoPlayerScreen from '../screens/VideoPlayerScreen';
import QuizScreen from '../screens/QuizScreen';
import AchievementsScreen from '../screens/AchievementsScreen';
import LeaderboardScreen from '../screens/LeaderboardScreen';
import StudyPlanScreen from '../screens/StudyPlanScreen';
import OfflineContentScreen from '../screens/OfflineContentScreen';

// Components
import CustomDrawerContent from '../components/navigation/CustomDrawerContent';
import TabBarIcon from '../components/navigation/TabBarIcon';
import HeaderRight from '../components/navigation/HeaderRight';

// Hooks
import {useAppSelector} from '../hooks/redux';

// Types
export type RootStackParamList = {
  MainTabs: undefined;
  CourseDetail: {courseId: string};
  Lesson: {courseId: string; lessonId: string};
  Exercise: {courseId: string; exerciseId: string};
  Project: {courseId: string; projectId: string};
  ForumPost: {postId: string};
  CertificateDetail: {certificateId: string};
  Search: {query?: string};
  Notifications: undefined;
  Settings: undefined;
  Help: undefined;
  About: undefined;
  CodeEditor: {code?: string; language?: string};
  VideoPlayer: {videoUrl: string; title?: string};
  Quiz: {courseId: string; quizId: string};
  Achievements: undefined;
  Leaderboard: {courseId?: string};
  StudyPlan: undefined;
  OfflineContent: undefined;
};

export type TabParamList = {
  Home: undefined;
  Courses: undefined;
  Progress: undefined;
  Community: undefined;
  Profile: undefined;
};

export type DrawerParamList = {
  MainStack: undefined;
  Downloads: undefined;
  Certificates: undefined;
  Achievements: undefined;
  StudyPlan: undefined;
  Settings: undefined;
  Help: undefined;
  About: undefined;
};

const Tab = createBottomTabNavigator<TabParamList>();
const Stack = createNativeStackNavigator<RootStackParamList>();
const Drawer = createDrawerNavigator<DrawerParamList>();

// Tab Navigator
const TabNavigator: React.FC = () => {
  const theme = useTheme();
  const {unreadNotifications} = useAppSelector(state => state.notifications);

  return (
    <Tab.Navigator
      screenOptions={({route}) => ({
        tabBarIcon: ({focused, color, size}) => (
          <TabBarIcon
            route={route}
            focused={focused}
            color={color}
            size={size}
          />
        ),
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.onSurfaceVariant,
        tabBarStyle: {
          backgroundColor: theme.colors.surface,
          borderTopColor: theme.colors.outline,
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
        },
        headerShown: false,
      })}>
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarLabel: 'Inicio',
          tabBarBadge: unreadNotifications > 0 ? unreadNotifications : undefined,
        }}
      />
      <Tab.Screen
        name="Courses"
        component={CoursesScreen}
        options={{
          tabBarLabel: 'Cursos',
        }}
      />
      <Tab.Screen
        name="Progress"
        component={ProgressScreen}
        options={{
          tabBarLabel: 'Progreso',
        }}
      />
      <Tab.Screen
        name="Community"
        component={CommunityScreen}
        options={{
          tabBarLabel: 'Comunidad',
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          tabBarLabel: 'Perfil',
        }}
      />
    </Tab.Navigator>
  );
};

// Main Stack Navigator
const MainStackNavigator: React.FC = () => {
  const theme = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.surface,
        },
        headerTintColor: theme.colors.onSurface,
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerShadowVisible: false,
        animation: 'slide_from_right',
      }}>
      <Stack.Screen
        name="MainTabs"
        component={TabNavigator}
        options={{
          headerShown: false,
        }}
      />
      
      {/* Course Related Screens */}
      <Stack.Screen
        name="CourseDetail"
        component={CourseDetailScreen}
        options={({route}) => ({
          title: 'Detalles del Curso',
          headerRight: () => <HeaderRight type="course" data={route.params} />,
        })}
      />
      
      <Stack.Screen
        name="Lesson"
        component={LessonScreen}
        options={({route}) => ({
          title: 'Lección',
          headerRight: () => <HeaderRight type="lesson" data={route.params} />,
          gestureEnabled: false, // Prevent swipe back during lesson
        })}
      />
      
      <Stack.Screen
        name="Exercise"
        component={ExerciseScreen}
        options={{
          title: 'Ejercicio',
          gestureEnabled: false,
        }}
      />
      
      <Stack.Screen
        name="Project"
        component={ProjectScreen}
        options={{
          title: 'Proyecto',
        }}
      />
      
      <Stack.Screen
        name="Quiz"
        component={QuizScreen}
        options={{
          title: 'Quiz',
          gestureEnabled: false,
        }}
      />
      
      {/* Community Screens */}
      <Stack.Screen
        name="ForumPost"
        component={ForumPostScreen}
        options={{
          title: 'Discusión',
        }}
      />
      
      {/* Utility Screens */}
      <Stack.Screen
        name="Search"
        component={SearchScreen}
        options={{
          title: 'Buscar',
          presentation: 'modal',
        }}
      />
      
      <Stack.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{
          title: 'Notificaciones',
        }}
      />
      
      <Stack.Screen
        name="CodeEditor"
        component={CodeEditorScreen}
        options={{
          title: 'Editor de Código',
          presentation: 'fullScreenModal',
          gestureEnabled: false,
        }}
      />
      
      <Stack.Screen
        name="VideoPlayer"
        component={VideoPlayerScreen}
        options={{
          title: 'Video',
          presentation: 'fullScreenModal',
          headerShown: false,
        }}
      />
      
      <Stack.Screen
        name="CertificateDetail"
        component={CertificateDetailScreen}
        options={{
          title: 'Certificado',
        }}
      />
      
      {/* Settings and Info */}
      <Stack.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          title: 'Configuración',
        }}
      />
      
      <Stack.Screen
        name="Help"
        component={HelpScreen}
        options={{
          title: 'Ayuda',
        }}
      />
      
      <Stack.Screen
        name="About"
        component={AboutScreen}
        options={{
          title: 'Acerca de',
        }}
      />
      
      <Stack.Screen
        name="Achievements"
        component={AchievementsScreen}
        options={{
          title: 'Logros',
        }}
      />
      
      <Stack.Screen
        name="Leaderboard"
        component={LeaderboardScreen}
        options={{
          title: 'Clasificación',
        }}
      />
      
      <Stack.Screen
        name="StudyPlan"
        component={StudyPlanScreen}
        options={{
          title: 'Plan de Estudio',
        }}
      />
      
      <Stack.Screen
        name="OfflineContent"
        component={OfflineContentScreen}
        options={{
          title: 'Contenido Offline',
        }}
      />
    </Stack.Navigator>
  );
};

// Drawer Navigator (Main App Navigator)
const AppNavigator: React.FC = () => {
  const theme = useTheme();
  const {user} = useAppSelector(state => state.auth);

  return (
    <Drawer.Navigator
      drawerContent={props => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          backgroundColor: theme.colors.surface,
          width: 280,
        },
        drawerActiveTintColor: theme.colors.primary,
        drawerInactiveTintColor: theme.colors.onSurfaceVariant,
        drawerLabelStyle: {
          fontSize: 16,
          fontWeight: '500',
        },
        swipeEnabled: true,
        swipeEdgeWidth: 50,
      }}>
      <Drawer.Screen
        name="MainStack"
        component={MainStackNavigator}
        options={{
          drawerLabel: 'Inicio',
          drawerIcon: ({color, size}) => (
            <Icon name="home" color={color} size={size} />
          ),
        }}
      />
      
      <Drawer.Screen
        name="Downloads"
        component={DownloadsScreen}
        options={{
          drawerLabel: 'Descargas',
          drawerIcon: ({color, size}) => (
            <Icon name="download" color={color} size={size} />
          ),
        }}
      />
      
      <Drawer.Screen
        name="Certificates"
        component={CertificatesScreen}
        options={{
          drawerLabel: 'Certificados',
          drawerIcon: ({color, size}) => (
            <Icon name="certificate" color={color} size={size} />
          ),
        }}
      />
      
      <Drawer.Screen
        name="Achievements"
        component={AchievementsScreen}
        options={{
          drawerLabel: 'Logros',
          drawerIcon: ({color, size}) => (
            <Icon name="trophy" color={color} size={size} />
          ),
        }}
      />
      
      <Drawer.Screen
        name="StudyPlan"
        component={StudyPlanScreen}
        options={{
          drawerLabel: 'Plan de Estudio',
          drawerIcon: ({color, size}) => (
            <Icon name="calendar-check" color={color} size={size} />
          ),
        }}
      />
      
      <Drawer.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          drawerLabel: 'Configuración',
          drawerIcon: ({color, size}) => (
            <Icon name="cog" color={color} size={size} />
          ),
        }}
      />
      
      <Drawer.Screen
        name="Help"
        component={HelpScreen}
        options={{
          drawerLabel: 'Ayuda',
          drawerIcon: ({color, size}) => (
            <Icon name="help-circle" color={color} size={size} />
          ),
        }}
      />
      
      <Drawer.Screen
        name="About"
        component={AboutScreen}
        options={{
          drawerLabel: 'Acerca de',
          drawerIcon: ({color, size}) => (
            <Icon name="information" color={color} size={size} />
          ),
        }}
      />
    </Drawer.Navigator>
  );
};

export default AppNavigator;
