# **JAVASCRIPT CHEAT SHEET - REFERENCIA RÁPIDA**

## **🔤 VARIABLES Y TIPOS DE DATOS**

```javascript
// Declaración de variables
let variable = "valor";           // Variable mutable
const constante = "valor";        // Variable inmutable
var antigua = "valor";            // Evitar usar (scope issues)

// Tipos primitivos
let texto = "string";             // String
let numero = 42;                  // Number
let booleano = true;              // Boolean
let indefinido = undefined;       // Undefined
let nulo = null;                  // Null
let simbolo = Symbol("id");       // Symbol
let bigInt = 123n;               // BigInt

// Verificar tipos
typeof variable;                  // Retorna el tipo
instanceof Array;                 // Verifica instancia
```

## **📊 ARRAYS**

```javascript
// Creación
let arr = [1, 2, 3];
let arr2 = new Array(1, 2, 3);

// Métodos principales
arr.push(4);                      // Agregar al final
arr.pop();                        // Remover del final
arr.unshift(0);                   // Agregar al inicio
arr.shift();                      // Remover del inicio
arr.splice(1, 2, 'a', 'b');      // Modificar elementos
arr.slice(1, 3);                  // Copiar porción

// Métodos funcionales
arr.map(x => x * 2);              // Transformar elementos
arr.filter(x => x > 1);           // Filtrar elementos
arr.reduce((acc, x) => acc + x);  // Reducir a un valor
arr.forEach(x => console.log(x)); // Iterar elementos
arr.find(x => x > 1);             // Encontrar elemento
arr.some(x => x > 1);             // Alguno cumple condición
arr.every(x => x > 0);            // Todos cumplen condición
```

## **🏗️ OBJETOS**

```javascript
// Creación
let obj = { nombre: "Juan", edad: 30 };
let obj2 = new Object();

// Acceso a propiedades
obj.nombre;                       // Notación punto
obj["nombre"];                    // Notación corchetes
obj?.nombre;                      // Optional chaining

// Métodos de Object
Object.keys(obj);                 // Obtener claves
Object.values(obj);               // Obtener valores
Object.entries(obj);              // Obtener pares clave-valor
Object.assign(target, source);    // Copiar propiedades
Object.freeze(obj);               // Hacer inmutable
Object.seal(obj);                 // Sellar objeto

// Destructuring
let { nombre, edad } = obj;       // Extraer propiedades
let { nombre: n } = obj;          // Renombrar al extraer
```

## **⚡ FUNCIONES**

```javascript
// Declaración de función
function miFuncion(param1, param2) {
    return param1 + param2;
}

// Expresión de función
const miFuncion = function(param1, param2) {
    return param1 + param2;
};

// Arrow function
const miFuncion = (param1, param2) => param1 + param2;
const miFuncion = param => param * 2;        // Un parámetro
const miFuncion = () => "sin parámetros";    // Sin parámetros

// Parámetros por defecto
function saludar(nombre = "Mundo") {
    return `Hola ${nombre}`;
}

// Rest parameters
function sumar(...numeros) {
    return numeros.reduce((a, b) => a + b);
}

// Spread operator
const arr1 = [1, 2, 3];
const arr2 = [...arr1, 4, 5];    // Expandir array
const obj1 = { a: 1, b: 2 };
const obj2 = { ...obj1, c: 3 };  // Expandir objeto
```

## **🔄 CONTROL DE FLUJO**

```javascript
// Condicionales
if (condicion) {
    // código
} else if (otraCondicion) {
    // código
} else {
    // código
}

// Operador ternario
const resultado = condicion ? valorSiTrue : valorSiFalse;

// Switch
switch (valor) {
    case 1:
        // código
        break;
    case 2:
        // código
        break;
    default:
        // código por defecto
}

// Bucles
for (let i = 0; i < 10; i++) {
    // código
}

for (let elemento of array) {
    // iterar valores
}

for (let clave in objeto) {
    // iterar claves
}

while (condicion) {
    // código
}

do {
    // código
} while (condicion);
```

## **🎯 CLASES Y OOP**

```javascript
// Definición de clase
class MiClase {
    constructor(param) {
        this.propiedad = param;
    }
    
    metodo() {
        return this.propiedad;
    }
    
    static metodoEstatico() {
        return "método estático";
    }
    
    get getter() {
        return this.propiedad;
    }
    
    set setter(valor) {
        this.propiedad = valor;
    }
}

// Herencia
class ClaseHija extends MiClase {
    constructor(param, param2) {
        super(param);
        this.otraPropiedad = param2;
    }
    
    metodo() {
        return super.metodo() + " modificado";
    }
}

// Instanciación
const instancia = new MiClase("valor");
```

## **⏰ ASÍNCRONO**

```javascript
// Promises
const promesa = new Promise((resolve, reject) => {
    if (exito) {
        resolve(resultado);
    } else {
        reject(error);
    }
});

promesa
    .then(resultado => console.log(resultado))
    .catch(error => console.error(error))
    .finally(() => console.log("Finalizado"));

// Async/Await
async function funcionAsincrona() {
    try {
        const resultado = await promesa;
        return resultado;
    } catch (error) {
        console.error(error);
    }
}

// Fetch API
fetch('https://api.ejemplo.com/datos')
    .then(response => response.json())
    .then(data => console.log(data))
    .catch(error => console.error(error));

// Con async/await
async function obtenerDatos() {
    try {
        const response = await fetch('https://api.ejemplo.com/datos');
        const data = await response.json();
        return data;
    } catch (error) {
        console.error(error);
    }
}
```

## **🌐 DOM MANIPULATION**

```javascript
// Selección de elementos
document.getElementById('id');
document.querySelector('.clase');
document.querySelectorAll('div');
document.getElementsByClassName('clase');
document.getElementsByTagName('div');

// Manipulación de contenido
elemento.innerHTML = '<p>HTML</p>';
elemento.textContent = 'Texto';
elemento.innerText = 'Texto visible';

// Manipulación de atributos
elemento.getAttribute('atributo');
elemento.setAttribute('atributo', 'valor');
elemento.removeAttribute('atributo');
elemento.hasAttribute('atributo');

// Manipulación de clases
elemento.classList.add('clase');
elemento.classList.remove('clase');
elemento.classList.toggle('clase');
elemento.classList.contains('clase');

// Eventos
elemento.addEventListener('click', function(event) {
    event.preventDefault();
    console.log('Clicked!');
});

// Crear elementos
const nuevoElemento = document.createElement('div');
nuevoElemento.textContent = 'Nuevo elemento';
document.body.appendChild(nuevoElemento);
```

## **🔧 UTILIDADES COMUNES**

```javascript
// Conversión de tipos
String(123);                      // "123"
Number("123");                    // 123
Boolean(1);                       // true
parseInt("123px");                // 123
parseFloat("123.45");             // 123.45

// Fechas
const ahora = new Date();
const fecha = new Date('2023-12-25');
fecha.getFullYear();              // Año
fecha.getMonth();                 // Mes (0-11)
fecha.getDate();                  // Día del mes

// Math
Math.random();                    // Número aleatorio 0-1
Math.floor(4.7);                  // 4
Math.ceil(4.1);                   // 5
Math.round(4.5);                  // 5
Math.max(1, 2, 3);               // 3
Math.min(1, 2, 3);               // 1

// JSON
JSON.stringify(objeto);           // Objeto a string
JSON.parse(jsonString);           // String a objeto

// Timers
setTimeout(() => console.log('Después de 1s'), 1000);
setInterval(() => console.log('Cada 1s'), 1000);
```

## **🚨 MANEJO DE ERRORES**

```javascript
// Try-catch
try {
    // código que puede fallar
    throw new Error('Error personalizado');
} catch (error) {
    console.error(error.message);
} finally {
    // código que siempre se ejecuta
}

// Tipos de errores
new Error('Mensaje');
new TypeError('Tipo incorrecto');
new ReferenceError('Referencia no encontrada');
new SyntaxError('Error de sintaxis');
```

## **📦 MÓDULOS ES6**

```javascript
// Exportar
export const variable = 'valor';
export function miFuncion() {}
export default class MiClase {}

// Importar
import { variable, miFuncion } from './modulo.js';
import MiClase from './modulo.js';
import * as modulo from './modulo.js';

// Importación dinámica
const modulo = await import('./modulo.js');
```

## **🎨 TEMPLATE LITERALS**

```javascript
const nombre = 'Juan';
const edad = 30;

// Template literal
const mensaje = `Hola ${nombre}, tienes ${edad} años`;

// Multilínea
const html = `
    <div>
        <h1>${nombre}</h1>
        <p>Edad: ${edad}</p>
    </div>
`;

// Tagged templates
function tag(strings, ...values) {
    return strings.reduce((result, string, i) => {
        return result + string + (values[i] || '');
    }, '');
}

const resultado = tag`Hola ${nombre}`;
```
