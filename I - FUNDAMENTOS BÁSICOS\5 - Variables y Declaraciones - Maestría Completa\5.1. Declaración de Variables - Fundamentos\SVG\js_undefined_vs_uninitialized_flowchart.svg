<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600">
  <!-- Definición de estilos -->
  <defs>
    <style>
      .title { font-family: Arial; font-size: 20px; font-weight: bold; }
      .subtitle { font-family: Arial; font-size: 16px; font-weight: bold; }
      .code { font-family: 'Courier New'; font-size: 12px; }
      .note { font-family: Arial; font-size: 12px; font-style: italic; }
      .box { stroke-width: 2px; rx: 5px; ry: 5px; }
      .arrow { stroke: #333; stroke-width: 2px; fill: none; marker-end: url(#arrowhead); }
      .connector { stroke: #666; stroke-width: 1.5px; stroke-dasharray: 5,3; fill: none; }
      .highlight { font-weight: bold; fill: #d32f2f; }
    </style>
    
    <!-- Definición de marcadores de flecha -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- Título principal -->
  <text x="400" y="30" text-anchor="middle" class="title">Estados de Variables en JavaScript: Undefined vs. No Inicializada</text>
  
  <!-- Sección 1: Estados de Variables -->
  <rect x="50" y="60" width="700" height="120" fill="#f5f5f5" class="box" stroke="#ccc" />
  <text x="400" y="80" text-anchor="middle" class="subtitle">Estados de Variables</text>
  
  <!-- Cajas de estados -->
  <rect x="70" y="100" width="150" height="60" fill="#ffcdd2" class="box" stroke="#e57373" />
  <text x="145" y="130" text-anchor="middle">No Declarada</text>
  <text x="145" y="150" text-anchor="middle" class="code">console.log(x); // Error</text>
  
  <rect x="240" y="100" width="150" height="60" fill="#fff9c4" class="box" stroke="#fff176" />
  <text x="315" y="130" text-anchor="middle">Declarada No Inicializada</text>
  <text x="315" y="150" text-anchor="middle" class="code">let x; // undefined</text>
  
  <rect x="410" y="100" width="150" height="60" fill="#e1f5fe" class="box" stroke="#81d4fa" />
  <text x="485" y="130" text-anchor="middle">Inicializada con undefined</text>
  <text x="485" y="150" text-anchor="middle" class="code">let x = undefined;</text>
  
  <rect x="580" y="100" width="150" height="60" fill="#e8f5e9" class="box" stroke="#a5d6a7" />
  <text x="655" y="130" text-anchor="middle">Inicializada con Valor</text>
  <text x="655" y="150" text-anchor="middle" class="code">let x = 42;</text>
  
  <!-- Sección 2: Comportamiento con var, let y const -->
  <rect x="50" y="200" width="700" height="180" fill="#f5f5f5" class="box" stroke="#ccc" />
  <text x="400" y="220" text-anchor="middle" class="subtitle">Comportamiento por Tipo de Declaración</text>
  
  <!-- Tabla de comportamientos -->
  <!-- Encabezados -->
  <rect x="70" y="240" width="120" height="30" fill="#e0e0e0" class="box" stroke="#9e9e9e" />
  <text x="130" y="260" text-anchor="middle" class="subtitle">Declaración</text>
  
  <rect x="190" y="240" width="170" height="30" fill="#e0e0e0" class="box" stroke="#9e9e9e" />
  <text x="275" y="260" text-anchor="middle" class="subtitle">Antes de Declaración</text>
  
  <rect x="360" y="240" width="170" height="30" fill="#e0e0e0" class="box" stroke="#9e9e9e" />
  <text x="445" y="260" text-anchor="middle" class="subtitle">Sin Inicialización</text>
  
  <rect x="530" y="240" width="200" height="30" fill="#e0e0e0" class="box" stroke="#9e9e9e" />
  <text x="630" y="260" text-anchor="middle" class="subtitle">Comportamiento</text>
  
  <!-- Fila var -->
  <rect x="70" y="270" width="120" height="30" fill="#fff" class="box" stroke="#9e9e9e" />
  <text x="130" y="290" text-anchor="middle" class="code">var x;</text>
  
  <rect x="190" y="270" width="170" height="30" fill="#fff" class="box" stroke="#9e9e9e" />
  <text x="275" y="290" text-anchor="middle" class="code">undefined (hoisting)</text>
  
  <rect x="360" y="270" width="170" height="30" fill="#fff" class="box" stroke="#9e9e9e" />
  <text x="445" y="290" text-anchor="middle" class="code">undefined</text>
  
  <rect x="530" y="270" width="200" height="30" fill="#fff" class="box" stroke="#9e9e9e" />
  <text x="630" y="290" text-anchor="middle" class="code">Hoisted, scope de función</text>
  
  <!-- Fila let -->
  <rect x="70" y="300" width="120" height="30" fill="#fff" class="box" stroke="#9e9e9e" />
  <text x="130" y="320" text-anchor="middle" class="code">let x;</text>
  
  <rect x="190" y="300" width="170" height="30" fill="#fff" class="box" stroke="#9e9e9e" />
  <text x="275" y="320" text-anchor="middle" class="code">ReferenceError (TDZ)</text>
  
  <rect x="360" y="300" width="170" height="30" fill="#fff" class="box" stroke="#9e9e9e" />
  <text x="445" y="320" text-anchor="middle" class="code">undefined</text>
  
  <rect x="530" y="300" width="200" height="30" fill="#fff" class="box" stroke="#9e9e9e" />
  <text x="630" y="320" text-anchor="middle" class="code">Block scope, TDZ</text>
  
  <!-- Fila const -->
  <rect x="70" y="330" width="120" height="30" fill="#fff" class="box" stroke="#9e9e9e" />
  <text x="130" y="350" text-anchor="middle" class="code">const x;</text>
  
  <rect x="190" y="330" width="170" height="30" fill="#fff" class="box" stroke="#9e9e9e" />
  <text x="275" y="350" text-anchor="middle" class="code">ReferenceError (TDZ)</text>
  
  <rect x="360" y="330" width="170" height="30" fill="#fff" class="box" stroke="#9e9e9e" />
  <text x="445" y="350" text-anchor="middle" class="code">SyntaxError</text>
  
  <rect x="530" y="330" width="200" height="30" fill="#fff" class="box" stroke="#9e9e9e" />
  <text x="630" y="350" text-anchor="middle" class="code">Requiere inicialización</text>
  
  <!-- Sección 3: Verificación y Detección -->
  <rect x="50" y="400" width="700" height="180" fill="#f5f5f5" class="box" stroke="#ccc" />
  <text x="400" y="420" text-anchor="middle" class="subtitle">Verificación y Detección</text>
  
  <!-- Métodos de verificación -->
  <rect x="70" y="440" width="320" height="120" fill="#fff" class="box" stroke="#9e9e9e" />
  <text x="230" y="460" text-anchor="middle" class="subtitle">Métodos de Verificación</text>
  
  <text x="90" y="485" class="code">// Verificar si es undefined</text>
  <text x="90" y="505" class="code">if (variable === undefined) { ... }</text>
  
  <text x="90" y="530" class="code">// Verificar si es undefined o null</text>
  <text x="90" y="550" class="code">if (variable == null) { ... }</text>
  
  <!-- Mejores prácticas -->
  <rect x="410" y="440" width="320" height="120" fill="#fff" class="box" stroke="#9e9e9e" />
  <text x="570" y="460" text-anchor="middle" class="subtitle">Mejores Prácticas</text>
  
  <text x="430" y="485" class="code">// Valores por defecto (ES6+)</text>
  <text x="430" y="505" class="code">const value = param ?? defaultValue;</text>
  
  <text x="430" y="530" class="code">// Verificación de parámetros</text>
  <text x="430" y="550" class="code">if (typeof param === 'undefined') { ... }</text>
  
  <!-- Notas y referencias -->
  <text x="400" y="590" text-anchor="middle" class="note">© JavaScript Avanzado - Variables y Declaraciones - Maestría Completa</text>
</svg>