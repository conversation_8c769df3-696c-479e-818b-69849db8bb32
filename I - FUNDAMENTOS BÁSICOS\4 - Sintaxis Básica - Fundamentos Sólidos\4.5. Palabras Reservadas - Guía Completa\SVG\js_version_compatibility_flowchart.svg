<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="processGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#388E3C;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="decisionGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F57C00;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="errorGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#F44336;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D32F2F;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>

  <!-- Título -->
  <rect x="10" y="10" width="980" height="50" fill="url(#headerGrad)" rx="5" filter="url(#shadow)"/>
  <text x="500" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">
    Flujo de Compatibilidad de Versiones JavaScript
  </text>

  <!-- Inicio: Código JavaScript -->
  <rect x="400" y="80" width="200" height="60" fill="url(#processGrad)" rx="10" filter="url(#shadow)"/>
  <text x="500" y="105" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Código JavaScript
  </text>
  <text x="500" y="125" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">
    checkES6Support()
  </text>

  <!-- Detección de Características -->
  <polygon points="450,170 550,170 570,200 550,230 450,230 430,200" fill="url(#decisionGrad)" filter="url(#shadow)"/>
  <text x="500" y="195" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Detección de
  </text>
  <text x="500" y="210" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Características
  </text>

  <!-- Características Detectadas -->
  <rect x="150" y="280" width="150" height="80" fill="url(#processGrad)" rx="8" filter="url(#shadow)"/>
  <text x="225" y="300" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">
    FeatureDetection
  </text>
  <text x="225" y="315" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="9">
    • arrowFunctions
  </text>
  <text x="225" y="330" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="9">
    • asyncAwait
  </text>
  <text x="225" y="345" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="9">
    • optionalChaining
  </text>

  <!-- Decisión: Soporte Nativo -->
  <polygon points="450,280 550,280 570,320 550,360 450,360 430,320" fill="url(#decisionGrad)" filter="url(#shadow)"/>
  <text x="500" y="315" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    ¿Soporte
  </text>
  <text x="500" y="330" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Nativo?
  </text>

  <!-- Ejecución Nativa -->
  <rect x="700" y="280" width="150" height="80" fill="url(#processGrad)" rx="8" filter="url(#shadow)"/>
  <text x="775" y="305" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Ejecución Nativa
  </text>
  <text x="775" y="325" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="9">
    Usar características
  </text>
  <text x="775" y="340" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="9">
    modernas directamente
  </text>

  <!-- Polyfill/Fallback -->
  <rect x="350" y="420" width="150" height="80" fill="url(#processGrad)" rx="8" filter="url(#shadow)"/>
  <text x="425" y="445" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Aplicar Polyfill
  </text>
  <text x="425" y="465" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="9">
    Array.includes
  </text>
  <text x="425" y="480" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="9">
    Fallback functions
  </text>

  <!-- Configuración de Transpilación -->
  <rect x="150" y="420" width="150" height="80" fill="url(#processGrad)" rx="8" filter="url(#shadow)"/>
  <text x="225" y="445" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Transpilación
  </text>
  <text x="225" y="465" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="9">
    TranspilationConfig
  </text>
  <text x="225" y="480" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="9">
    shouldTranspile()
  </text>

  <!-- Operaciones Compatibles -->
  <rect x="550" y="420" width="150" height="80" fill="url(#processGrad)" rx="8" filter="url(#shadow)"/>
  <text x="625" y="445" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Operaciones
  </text>
  <text x="625" y="460" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Compatibles
  </text>
  <text x="625" y="480" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="9">
    modernArrayOperations()
  </text>

  <!-- Resultado Final -->
  <rect x="400" y="560" width="200" height="60" fill="url(#processGrad)" rx="10" filter="url(#shadow)"/>
  <text x="500" y="585" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Código Compatible
  </text>
  <text x="500" y="605" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10">
    Funciona en todos los navegadores
  </text>

  <!-- Información de Versiones -->
  <rect x="750" y="420" width="200" height="120" fill="#E3F2FD" rx="8" stroke="#1976D2" stroke-width="2" filter="url(#shadow)"/>
  <text x="850" y="440" text-anchor="middle" fill="#1976D2" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Versiones ECMAScript
  </text>
  <text x="760" y="460" fill="#333" font-family="Arial, sans-serif" font-size="9">
    • ES5 (2009): Soporte universal
  </text>
  <text x="760" y="475" fill="#333" font-family="Arial, sans-serif" font-size="9">
    • ES6/2015: Arrow functions, let/const
  </text>
  <text x="760" y="490" fill="#333" font-family="Arial, sans-serif" font-size="9">
    • ES2017: async/await
  </text>
  <text x="760" y="505" fill="#333" font-family="Arial, sans-serif" font-size="9">
    • ES2020: Optional chaining
  </text>
  <text x="760" y="520" fill="#333" font-family="Arial, sans-serif" font-size="9">
    • Herramientas: Babel, TypeScript
  </text>

  <!-- Flechas de conexión -->
  <line x1="500" y1="140" x2="500" y2="170" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="500" y1="230" x2="225" y2="280" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="500" y1="230" x2="500" y2="280" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Sí - Soporte nativo -->
  <line x1="570" y1="320" x2="700" y2="320" stroke="#4CAF50" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="635" y="315" fill="#4CAF50" font-family="Arial, sans-serif" font-size="10" font-weight="bold">SÍ</text>
  
  <!-- No - Polyfill/Fallback -->
  <line x1="500" y1="360" x2="500" y2="420" stroke="#F44336" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="510" y="390" fill="#F44336" font-family="Arial, sans-serif" font-size="10" font-weight="bold">NO</text>
  
  <!-- Conexiones a transpilación y operaciones -->
  <line x1="450" y1="460" x2="300" y2="460" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="500" y1="460" x2="550" y2="460" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Convergencia al resultado final -->
  <line x1="225" y1="500" x2="450" y2="560" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="425" y1="500" x2="475" y2="560" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="625" y1="500" x2="525" y2="560" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="775" y1="360" x2="550" y2="560" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- Ejemplos de entrada y salida -->
  <rect x="50" y="650" width="300" height="100" fill="#FFF3E0" rx="8" stroke="#FF9800" stroke-width="2" filter="url(#shadow)"/>
  <text x="200" y="670" text-anchor="middle" fill="#E65100" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Ejemplo de Entrada
  </text>
  <text x="60" y="690" fill="#333" font-family="Courier, monospace" font-size="9">
    const data = [{name: 'Item1', active: true}];
  </text>
  <text x="60" y="705" fill="#333" font-family="Courier, monospace" font-size="9">
    modernArrayOperations(data);
  </text>
  <text x="60" y="720" fill="#333" font-family="Courier, monospace" font-size="9">
    // Detecta características y aplica fallback
  </text>
  <text x="60" y="735" fill="#333" font-family="Courier, monospace" font-size="9">
    // Resultado: ['Item1']
  </text>

  <rect x="650" y="650" width="300" height="100" fill="#E8F5E8" rx="8" stroke="#4CAF50" stroke-width="2" filter="url(#shadow)"/>
  <text x="800" y="670" text-anchor="middle" fill="#2E7D32" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Resultado Compatible
  </text>
  <text x="660" y="690" fill="#333" font-family="Courier, monospace" font-size="9">
    // Navegador moderno: usa optional chaining
  </text>
  <text x="660" y="705" fill="#333" font-family="Courier, monospace" font-size="9">
    // Navegador antiguo: usa fallback
  </text>
  <text x="660" y="720" fill="#333" font-family="Courier, monospace" font-size="9">
    // Ambos obtienen: ['Item1']
  </text>
  <text x="660" y="735" fill="#333" font-family="Courier, monospace" font-size="9">
    // Compatibilidad garantizada
  </text>
</svg>