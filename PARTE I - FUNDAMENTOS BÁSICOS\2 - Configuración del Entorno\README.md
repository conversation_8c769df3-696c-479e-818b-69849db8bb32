# **Capítulo 2 - Configuración del Entorno**

## **📖 Descripción del Capítulo**

Este capítulo te guiará paso a paso para configurar un entorno de desarrollo profesional para JavaScript. Aprenderás a instalar y configurar todas las herramientas necesarias para convertirte en un desarrollador JavaScript eficiente.

## **🎯 Objetivos de Aprendizaje**

Al completar este capítulo, serás capaz de:

- [ ] Instalar y configurar Node.js correctamente
- [ ] Configurar VS Code con las mejores extensiones
- [ ] Dominar las extensiones esenciales para JavaScript
- [ ] Usar la terminal y CLI eficientemente
- [ ] Configurar herramientas de debugging profesionales

## **📊 Información del Capítulo**

- **Dificultad:** ⭐ (Principiante)
- **Tiempo estimado:** 4-6 horas
- **Temas:** 5
- **Subtemas:** 50
- **Ejercicios prácticos:** 25

## **📋 Índice de Temas**

### **[2.1. Instalación de Node.js](2.1.%20Instalación%20de%20Node.js/README.md)** ⭐
**Tiempo:** 1 hora | **Subtemas:** 10

Instala Node.js y npm, configura versiones y aprende los comandos básicos.

---

### **[2.2. Configuración de VS Code](2.2.%20Configuración%20de%20VS Code/README.md)** ⭐
**Tiempo:** 1-2 horas | **Subtemas:** 10

Configura el editor más popular para JavaScript con todas las optimizaciones.

---

### **[2.3. Extensiones Esenciales](2.3.%20Extensiones%20Esenciales/README.md)** ⭐⭐
**Tiempo:** 1 hora | **Subtemas:** 10

Instala y configura las extensiones que todo desarrollador JavaScript necesita.

---

### **[2.4. Terminal y CLI](2.4.%20Terminal%20y%20CLI/README.md)** ⭐⭐
**Tiempo:** 1-2 horas | **Subtemas:** 10

Domina la línea de comandos y las herramientas CLI esenciales.

---

### **[2.5. Debugging Setup](2.5.%20Debugging%20Setup/README.md)** ⭐⭐
**Tiempo:** 1 hora | **Subtemas:** 10

Configura herramientas de debugging para navegador y Node.js.

---

## **🛠️ Herramientas que Instalaremos**

### **Esenciales**
- **Node.js** - Runtime de JavaScript
- **npm/yarn** - Gestores de paquetes
- **VS Code** - Editor de código
- **Git** - Control de versiones

### **Extensiones VS Code**
- **ES7+ React/Redux/React-Native snippets**
- **Prettier** - Formateador de código
- **ESLint** - Linter de JavaScript
- **Live Server** - Servidor de desarrollo
- **GitLens** - Git integrado
- **Bracket Pair Colorizer** - Colores para brackets

### **Herramientas CLI**
- **nodemon** - Auto-restart para Node.js
- **http-server** - Servidor HTTP simple
- **create-react-app** - Scaffolding para React
- **@vue/cli** - CLI de Vue.js

## **💻 Requisitos del Sistema**

### **Windows**
- Windows 10 o superior
- 4GB RAM mínimo (8GB recomendado)
- 2GB espacio libre

### **macOS**
- macOS 10.14 o superior
- 4GB RAM mínimo (8GB recomendado)
- 2GB espacio libre

### **Linux**
- Ubuntu 18.04+ / Debian 10+ / CentOS 7+
- 4GB RAM mínimo (8GB recomendado)
- 2GB espacio libre

## **🎯 Rutas de Configuración**

### **🚀 Configuración Rápida (2-3 horas)**
Solo lo esencial para empezar:
- Node.js + npm
- VS Code básico
- Extensiones mínimas

### **📚 Configuración Completa (4-6 horas)**
Entorno profesional completo:
- Todas las herramientas
- Configuración optimizada
- Debugging avanzado

### **🔬 Configuración Experto (6-8 horas)**
Para desarrolladores avanzados:
- Herramientas adicionales
- Configuraciones personalizadas
- Automatización

---

## **📊 Progreso del Capítulo**

```
Tema 2.1: [░░░░░░░░░░] 0% completado
Tema 2.2: [░░░░░░░░░░] 0% completado
Tema 2.3: [░░░░░░░░░░] 0% completado
Tema 2.4: [░░░░░░░░░░] 0% completado
Tema 2.5: [░░░░░░░░░░] 0% completado

Progreso Total: [░░░░░░░░░░] 0% completado
```

## **🏆 Logros del Capítulo**

- 🎯 **Node Master**: Instalar y configurar Node.js
- 🎯 **VS Code Pro**: Configurar VS Code profesionalmente
- 🎯 **Extension Expert**: Instalar extensiones esenciales
- 🎯 **Terminal Ninja**: Dominar la línea de comandos
- 🎯 **Debug Master**: Configurar debugging
- 🚀 **Environment Master**: Completar todos los temas

---

## **📝 Proyecto del Capítulo**

**"Mi Primer Proyecto JavaScript"**
- Crear estructura de proyecto
- Configurar todas las herramientas
- Escribir código de prueba
- Hacer debugging
- Documentar el proceso

---

## **➡️ Navegación**

⬅️ **Anterior:** [Capítulo 1 - Introducción a JavaScript](../1%20-%20Introducción%20a%20JavaScript/README.md)  
➡️ **Siguiente:** [Capítulo 3 - Primeros Pasos](../3%20-%20Primeros%20Pasos/README.md)  
🏠 **Parte:** [Volver a Fundamentos Básicos](../README.md)

---

**¡Configura tu entorno como un profesional!** 🚀
