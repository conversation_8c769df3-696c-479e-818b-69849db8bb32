## 4.3.3. <PERSON><PERSON><PERSON> donde ASI falla

### Introducción
ASI no siempre inserta semicolons correctamente.  
Falla en restricted productions.  
Causa syntax errors inesperados.  
Afecta return, break, continue.  
Postfix ++/-- al inicio de línea.  
¿Has debuggeado errores por ASI?

### Código de Ejemplo
```javascript
// Falla en return
let func = () => {
  return
  42
}
console.log(func())

// Falla con array al inicio
let a = 1
[2, 3].forEach(x => console.log(x))

// Falla con regex
let b = 2
/3/.test('3')

// Falla con postfix
let c = 1
c
++  // Interpreta como ++c
console.log(c)

// Falla en continue
while (true) {
  continue
  {label: true}
}
```
### Resultados en Consola
- `undefined`
- SyntaxError para array
- SyntaxError para regex
- `2` (pero inesperado)
- SyntaxError para continue

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> ReturnFail["Paso 1: return 42 <br> - ASI inserta ;"]
    ReturnFail --> LogFunc["Paso 2: console.log(func()) <br> - undefined"]
    LogFunc --> ArrayFail["Paso 3: [2,3].forEach <br> - SyntaxError"]
    ArrayFail --> RegexFail["Paso 4: /3/.test <br> - SyntaxError"]
    RegexFail --> PostfixFail["Paso 5: c ++ <br> - Interpreta mal"]
    PostfixFail --> LogC["Paso 6: console.log(c) <br> - 2"]
    LogC --> ContinueFail["Paso 7: continue {label} <br> - SyntaxError"]
    ContinueFail --> Fin["Paso 8: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> DebugASI["Debuggear ASI fails"]
    ReturnFail --> NotaReturn["Nota: Return vacío"]
    ArrayFail --> NotaArray["Nota: Interpreta como access"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ReturnFail fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogFunc fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ArrayFail fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style RegexFail fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style PostfixFail fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogC fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ContinueFail fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style DebugASI fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaReturn fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaArray fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> ASIAttempt["Intenta ASI <br> - Reglas"]
        ASIAttempt --> Restricted["Restricted production? <br> - No inserta"]
        Restricted -->|Sí| SyntaxError["Syntax Error <br> - Falla"]
        Restricted -->|No| WrongInsert["Inserta mal <br> - Comportamiento inesperado"]
        WrongInsert --> Bug["Bug en runtime"]
        SyntaxError --> Bug
        Bug --> Debug["Debug <br> - Difícil de encontrar"]
        Inicio --> Desarrollador(("Desarrollador")) --> Prevent["Prevenir con ; explícitos"]
        Restricted --> NotaRestricted["Nota: return, break, etc."]
        WrongInsert --> NotaWrong["Nota: ++, arrays, regex"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ASIAttempt fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Restricted fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style SyntaxError fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style WrongInsert fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Bug fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Debug fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Prevent fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaRestricted fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaWrong fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Return con line break.
2. Arrays/objects al inicio de línea.
3. Regex literals después de variable.
4. Postfix operators separados.
5. Break/continue con labels.

### Errores comunes
1. Line breaks después de return.
2. Empezar líneas con [ o (.
3. Usar / para regex sin cuidado.
4. Separar variable y ++.
5. Ignorar restricted productions.

### Recomendaciones
1. Siempre usa semicolons.
2. Evita line breaks riesgosos.
3. Usa linters para detectar.
4. Testea código sin ;.
5. Lee spec para casos edge.