# **CONFIGURACIÓN DE HERRAMIENTAS DE DESARROLLO JAVASCRIPT**

## **📦 PACKAGE.JSON - CONFIGURACIÓN BÁSICA**

```json
{
  "name": "mi-proyecto-javascript",
  "version": "1.0.0",
  "description": "Descripción del proyecto",
  "main": "index.js",
  "type": "module",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "build": "webpack --mode production",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/**/*.js",
    "lint:fix": "eslint src/**/*.js --fix",
    "format": "prettier --write src/**/*.js",
    "prepare": "husky install"
  },
  "keywords": ["javascript", "node", "web"],
  "author": "Tu Nombre",
  "license": "MIT",
  "devDependencies": {
    "@babel/core": "^7.22.0",
    "@babel/preset-env": "^7.22.0",
    "eslint": "^8.42.0",
    "prettier": "^2.8.8",
    "jest": "^29.5.0",
    "husky": "^8.0.3",
    "lint-staged": "^13.2.2",
    "nodemon": "^2.0.22",
    "webpack": "^5.88.0",
    "webpack-cli": "^5.1.4"
  },
  "dependencies": {
    "express": "^4.18.2",
    "lodash": "^4.17.21"
  },
  "engines": {
    "node": ">=16.0.0",
    "npm": ">=8.0.0"
  }
}
```

## **🔧 ESLINT - CONFIGURACIÓN**

### **.eslintrc.js**
```javascript
module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
    jest: true
  },
  extends: [
    'eslint:recommended',
    '@eslint/js/recommended'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  rules: {
    // Errores
    'no-console': 'warn',
    'no-debugger': 'error',
    'no-unused-vars': 'error',
    'no-undef': 'error',
    
    // Mejores prácticas
    'prefer-const': 'error',
    'no-var': 'error',
    'eqeqeq': 'error',
    'curly': 'error',
    
    // Estilo
    'indent': ['error', 2],
    'quotes': ['error', 'single'],
    'semi': ['error', 'always'],
    'comma-dangle': ['error', 'never'],
    
    // ES6+
    'arrow-spacing': 'error',
    'prefer-arrow-callback': 'error',
    'prefer-template': 'error',
    'object-shorthand': 'error'
  },
  overrides: [
    {
      files: ['**/*.test.js', '**/*.spec.js'],
      rules: {
        'no-console': 'off'
      }
    }
  ]
};
```

### **.eslintignore**
```
node_modules/
dist/
build/
coverage/
*.min.js
```

## **🎨 PRETTIER - CONFIGURACIÓN**

### **.prettierrc.js**
```javascript
module.exports = {
  // Básico
  semi: true,
  singleQuote: true,
  quoteProps: 'as-needed',
  
  // Indentación
  tabWidth: 2,
  useTabs: false,
  
  // Líneas
  printWidth: 80,
  endOfLine: 'lf',
  
  // Trailing commas
  trailingComma: 'es5',
  
  // Espacios
  bracketSpacing: true,
  bracketSameLine: false,
  
  // Arrow functions
  arrowParens: 'avoid',
  
  // HTML/JSX
  htmlWhitespaceSensitivity: 'css',
  
  // Otros
  insertPragma: false,
  requirePragma: false,
  proseWrap: 'preserve'
};
```

### **.prettierignore**
```
node_modules/
dist/
build/
coverage/
package-lock.json
yarn.lock
*.min.js
*.min.css
```

## **🧪 JEST - CONFIGURACIÓN DE TESTING**

### **jest.config.js**
```javascript
module.exports = {
  // Entorno de testing
  testEnvironment: 'node', // o 'jsdom' para testing del DOM
  
  // Archivos de test
  testMatch: [
    '**/__tests__/**/*.js',
    '**/?(*.)+(spec|test).js'
  ],
  
  // Coverage
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/**/*.test.js',
    '!src/**/*.spec.js',
    '!src/index.js'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  
  // Setup
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  
  // Transformaciones
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  
  // Módulos
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  
  // Timeouts
  testTimeout: 10000,
  
  // Verbose output
  verbose: true,
  
  // Clear mocks
  clearMocks: true,
  restoreMocks: true
};
```

### **jest.setup.js**
```javascript
// Configuración global para tests
global.console = {
  ...console,
  // Silenciar logs en tests
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// Timeout global para tests async
jest.setTimeout(10000);

// Mocks globales
global.fetch = jest.fn();

// Cleanup después de cada test
afterEach(() => {
  jest.clearAllMocks();
});
```

## **📦 BABEL - CONFIGURACIÓN**

### **babel.config.js**
```javascript
module.exports = {
  presets: [
    [
      '@babel/preset-env',
      {
        targets: {
          node: '16',
          browsers: ['> 1%', 'last 2 versions', 'not dead']
        },
        useBuiltIns: 'usage',
        corejs: 3
      }
    ]
  ],
  plugins: [
    '@babel/plugin-proposal-class-properties',
    '@babel/plugin-proposal-private-methods',
    '@babel/plugin-proposal-optional-chaining',
    '@babel/plugin-proposal-nullish-coalescing-operator'
  ],
  env: {
    test: {
      presets: [
        [
          '@babel/preset-env',
          {
            targets: { node: 'current' }
          }
        ]
      ]
    }
  }
};
```

## **📦 WEBPACK - CONFIGURACIÓN**

### **webpack.config.js**
```javascript
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';
  
  return {
    entry: './src/index.js',
    
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: isProduction 
        ? '[name].[contenthash].js' 
        : '[name].js',
      clean: true
    },
    
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader'
          }
        },
        {
          test: /\.css$/,
          use: [
            isProduction 
              ? MiniCssExtractPlugin.loader 
              : 'style-loader',
            'css-loader'
          ]
        },
        {
          test: /\.(png|svg|jpg|jpeg|gif)$/i,
          type: 'asset/resource'
        }
      ]
    },
    
    plugins: [
      new HtmlWebpackPlugin({
        template: './src/index.html'
      }),
      ...(isProduction 
        ? [new MiniCssExtractPlugin({
            filename: '[name].[contenthash].css'
          })] 
        : [])
    ],
    
    devServer: {
      static: './dist',
      hot: true,
      open: true,
      port: 3000
    },
    
    optimization: {
      splitChunks: {
        chunks: 'all'
      }
    },
    
    devtool: isProduction 
      ? 'source-map' 
      : 'eval-source-map'
  };
};
```

## **🐕 HUSKY - GIT HOOKS**

### **.husky/pre-commit**
```bash
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx lint-staged
```

### **.husky/commit-msg**
```bash
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx commitlint --edit $1
```

### **lint-staged configuration en package.json**
```json
{
  "lint-staged": {
    "*.js": [
      "eslint --fix",
      "prettier --write",
      "git add"
    ],
    "*.{json,md}": [
      "prettier --write",
      "git add"
    ]
  }
}
```

## **📝 COMMITLINT - CONFIGURACIÓN**

### **commitlint.config.js**
```javascript
module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'feat',     // Nueva característica
        'fix',      // Corrección de bug
        'docs',     // Documentación
        'style',    // Formato, punto y coma, etc.
        'refactor', // Refactoring
        'test',     // Agregar tests
        'chore',    // Mantenimiento
        'perf',     // Mejora de performance
        'ci',       // Cambios en CI
        'build',    // Cambios en build
        'revert'    // Revertir commit
      ]
    ],
    'type-case': [2, 'always', 'lower-case'],
    'type-empty': [2, 'never'],
    'scope-case': [2, 'always', 'lower-case'],
    'subject-case': [2, 'never', ['sentence-case', 'start-case', 'pascal-case', 'upper-case']],
    'subject-empty': [2, 'never'],
    'subject-full-stop': [2, 'never', '.'],
    'header-max-length': [2, 'always', 72]
  }
};
```

## **🔧 VSCODE - CONFIGURACIÓN**

### **.vscode/settings.json**
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "eslint.validate": ["javascript"],
  "files.associations": {
    "*.js": "javascript"
  },
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  },
  "javascript.preferences.quoteStyle": "single",
  "typescript.preferences.quoteStyle": "single"
}
```

### **.vscode/extensions.json**
```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",
    "christian-kohler.path-intellisense",
    "formulahendry.auto-rename-tag",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

## **🚀 SCRIPTS DE AUTOMATIZACIÓN**

### **scripts/setup.js**
```javascript
#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🚀 Configurando proyecto...');

// Instalar dependencias
console.log('📦 Instalando dependencias...');
execSync('npm install', { stdio: 'inherit' });

// Configurar Husky
console.log('🐕 Configurando Husky...');
execSync('npx husky install', { stdio: 'inherit' });

// Crear directorios necesarios
const dirs = ['src', 'tests', 'docs'];
dirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir);
    console.log(`📁 Creado directorio: ${dir}`);
  }
});

console.log('✅ Proyecto configurado correctamente!');
```

Esta configuración proporciona un entorno de desarrollo robusto con linting, formatting, testing y automatización de tareas para proyectos JavaScript modernos.
