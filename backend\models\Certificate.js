/**
 * MODELO DE CERTIFICADO
 * =====================
 * 
 * Sistema completo de certificaciones para CodeCraft Academy
 * Incluye verificación blockchain, templates personalizables y analytics
 */

const mongoose = require('mongoose');
const crypto = require('crypto');

const certificateSchema = new mongoose.Schema({
    // Información básica del certificado
    certificateId: {
        type: String,
        unique: true,
        required: true
    },
    
    // Referencias
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    
    courseId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course',
        required: true
    },
    
    // Tipo de certificado
    type: {
        type: String,
        enum: [
            'course_completion',
            'specialization',
            'professional_certificate',
            'expert_certification',
            'instructor_certification',
            'achievement_badge'
        ],
        required: true
    },
    
    // Información del certificado
    title: {
        type: String,
        required: true
    },
    
    description: {
        type: String,
        required: true
    },
    
    // Información del estudiante
    recipient: {
        name: {
            type: String,
            required: true
        },
        email: {
            type: String,
            required: true
        },
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        }
    },
    
    // Información del curso/programa
    program: {
        title: {
            type: String,
            required: true
        },
        language: String,
        category: String,
        level: String,
        duration: String,
        instructor: {
            name: String,
            title: String,
            signature: String // URL de la imagen de la firma
        }
    },
    
    // Métricas de rendimiento
    performance: {
        finalScore: {
            type: Number,
            required: true,
            min: 0,
            max: 100
        },
        completionTime: {
            type: Number, // en días
            required: true
        },
        totalStudyHours: {
            type: Number,
            required: true
        },
        exercisesCompleted: Number,
        projectsCompleted: Number,
        averageScore: Number,
        rank: String, // 'excellent', 'good', 'satisfactory'
        percentile: Number // Percentil en comparación con otros estudiantes
    },
    
    // Habilidades certificadas
    skills: [{
        name: {
            type: String,
            required: true
        },
        level: {
            type: String,
            enum: ['basic', 'intermediate', 'advanced', 'expert'],
            required: true
        },
        verified: {
            type: Boolean,
            default: true
        }
    }],
    
    // Competencias demostradas
    competencies: [{
        name: String,
        description: String,
        evidenceType: {
            type: String,
            enum: ['project', 'exercise', 'quiz', 'peer_review', 'instructor_review']
        },
        evidenceUrl: String,
        score: Number
    }],
    
    // Fechas importantes
    issuedAt: {
        type: Date,
        required: true,
        default: Date.now
    },
    
    validFrom: {
        type: Date,
        required: true,
        default: Date.now
    },
    
    expiresAt: {
        type: Date,
        // Algunos certificados pueden tener fecha de expiración
    },
    
    // Verificación y autenticidad
    verification: {
        hash: {
            type: String,
            required: true,
            unique: true
        },
        blockchainTxId: String, // ID de transacción en blockchain
        verificationUrl: {
            type: String,
            required: true
        },
        qrCode: String, // URL del código QR para verificación
        digitalSignature: String,
        isVerified: {
            type: Boolean,
            default: true
        },
        verifiedAt: Date
    },
    
    // Diseño y presentación
    design: {
        templateId: {
            type: String,
            default: 'default'
        },
        backgroundColor: {
            type: String,
            default: '#ffffff'
        },
        primaryColor: {
            type: String,
            default: '#f7df1e'
        },
        secondaryColor: {
            type: String,
            default: '#333333'
        },
        logoUrl: String,
        backgroundImageUrl: String,
        borderStyle: {
            type: String,
            enum: ['none', 'simple', 'decorative', 'elegant'],
            default: 'simple'
        }
    },
    
    // Archivos generados
    files: {
        pdfUrl: String,
        imageUrl: String,
        thumbnailUrl: String,
        shareableUrl: String
    },
    
    // Configuración de privacidad
    privacy: {
        isPublic: {
            type: Boolean,
            default: true
        },
        showInProfile: {
            type: Boolean,
            default: true
        },
        allowSharing: {
            type: Boolean,
            default: true
        },
        showPerformanceMetrics: {
            type: Boolean,
            default: false
        }
    },
    
    // Compartir en redes sociales
    socialSharing: {
        linkedInShared: {
            type: Boolean,
            default: false
        },
        twitterShared: {
            type: Boolean,
            default: false
        },
        facebookShared: {
            type: Boolean,
            default: false
        },
        shareCount: {
            type: Number,
            default: 0
        }
    },
    
    // Metadatos adicionales
    metadata: {
        version: {
            type: String,
            default: '1.0'
        },
        language: {
            type: String,
            default: 'es'
        },
        timezone: String,
        ipAddress: String,
        userAgent: String,
        issuerInstitution: {
            type: String,
            default: 'CodeCraft Academy'
        },
        accreditationBody: String,
        credentialId: String
    },
    
    // Estado del certificado
    status: {
        type: String,
        enum: ['active', 'revoked', 'suspended', 'expired'],
        default: 'active'
    },
    
    // Razón de revocación (si aplica)
    revocation: {
        reason: String,
        revokedAt: Date,
        revokedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        notes: String
    },
    
    // Analytics y métricas
    analytics: {
        viewCount: {
            type: Number,
            default: 0
        },
        downloadCount: {
            type: Number,
            default: 0
        },
        verificationCount: {
            type: Number,
            default: 0
        },
        lastViewed: Date,
        lastDownloaded: Date,
        lastVerified: Date
    }

}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Índices para optimización
certificateSchema.index({ certificateId: 1 }, { unique: true });
certificateSchema.index({ userId: 1 });
certificateSchema.index({ courseId: 1 });
certificateSchema.index({ 'verification.hash': 1 }, { unique: true });
certificateSchema.index({ type: 1 });
certificateSchema.index({ status: 1 });
certificateSchema.index({ issuedAt: -1 });
certificateSchema.index({ 'recipient.email': 1 });

// Virtuals
certificateSchema.virtual('isValid').get(function() {
    if (this.status !== 'active') return false;
    if (this.expiresAt && this.expiresAt < new Date()) return false;
    return true;
});

certificateSchema.virtual('daysUntilExpiration').get(function() {
    if (!this.expiresAt) return null;
    const now = new Date();
    const diffTime = this.expiresAt - now;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

certificateSchema.virtual('publicUrl').get(function() {
    return `${process.env.CLIENT_URL}/certificates/${this.certificateId}`;
});

certificateSchema.virtual('verificationUrl').get(function() {
    return `${process.env.CLIENT_URL}/verify/${this.verification.hash}`;
});

certificateSchema.virtual('credibilityScore').get(function() {
    let score = 100;
    
    // Reducir por tiempo transcurrido
    const monthsOld = (new Date() - this.issuedAt) / (1000 * 60 * 60 * 24 * 30);
    score -= Math.min(monthsOld * 2, 20);
    
    // Aumentar por verificaciones
    score += Math.min(this.analytics.verificationCount * 0.5, 10);
    
    // Reducir si está cerca de expirar
    if (this.daysUntilExpiration && this.daysUntilExpiration < 30) {
        score -= 15;
    }
    
    return Math.max(Math.round(score), 0);
});

// Middleware pre-save
certificateSchema.pre('save', function(next) {
    // Generar certificateId si no existe
    if (!this.certificateId) {
        this.certificateId = this.generateCertificateId();
    }
    
    // Generar hash de verificación si no existe
    if (!this.verification.hash) {
        this.verification.hash = this.generateVerificationHash();
    }
    
    // Generar URL de verificación
    if (!this.verification.verificationUrl) {
        this.verification.verificationUrl = this.verificationUrl;
    }
    
    // Determinar rank basado en score
    if (this.performance.finalScore >= 95) {
        this.performance.rank = 'excellent';
    } else if (this.performance.finalScore >= 85) {
        this.performance.rank = 'good';
    } else {
        this.performance.rank = 'satisfactory';
    }
    
    next();
});

// Métodos de instancia
certificateSchema.methods.generateCertificateId = function() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    const courseCode = this.program.language ? this.program.language.toUpperCase().substr(0, 2) : 'CC';
    return `${courseCode}-${timestamp}-${random}`.toUpperCase();
};

certificateSchema.methods.generateVerificationHash = function() {
    const data = `${this.userId}${this.courseId}${this.issuedAt}${this.performance.finalScore}`;
    return crypto.createHash('sha256').update(data).digest('hex');
};

certificateSchema.methods.generateQRCode = function() {
    // Esta función generaría un código QR con la URL de verificación
    // Implementación dependería de una librería como 'qrcode'
    this.verification.qrCode = `${process.env.CLIENT_URL}/qr/${this.verification.hash}`;
    return this.verification.qrCode;
};

certificateSchema.methods.revoke = function(reason, revokedBy) {
    this.status = 'revoked';
    this.revocation = {
        reason,
        revokedAt: new Date(),
        revokedBy,
        notes: `Certificate revoked: ${reason}`
    };
    return this.save();
};

certificateSchema.methods.verify = function() {
    this.analytics.verificationCount += 1;
    this.analytics.lastVerified = new Date();
    return this.save();
};

certificateSchema.methods.incrementView = function() {
    this.analytics.viewCount += 1;
    this.analytics.lastViewed = new Date();
    return this.save();
};

certificateSchema.methods.incrementDownload = function() {
    this.analytics.downloadCount += 1;
    this.analytics.lastDownloaded = new Date();
    return this.save();
};

certificateSchema.methods.shareOnSocial = function(platform) {
    if (this.socialSharing.hasOwnProperty(`${platform}Shared`)) {
        this.socialSharing[`${platform}Shared`] = true;
        this.socialSharing.shareCount += 1;
    }
    return this.save();
};

certificateSchema.methods.generatePDF = function() {
    // Esta función generaría un PDF del certificado
    // Implementación dependería de librerías como 'puppeteer' o 'pdf-lib'
    const pdfUrl = `${process.env.CDN_URL}/certificates/pdf/${this.certificateId}.pdf`;
    this.files.pdfUrl = pdfUrl;
    return pdfUrl;
};

certificateSchema.methods.generateImage = function() {
    // Esta función generaría una imagen del certificado
    const imageUrl = `${process.env.CDN_URL}/certificates/images/${this.certificateId}.png`;
    this.files.imageUrl = imageUrl;
    return imageUrl;
};

certificateSchema.methods.toPublicJSON = function() {
    return {
        certificateId: this.certificateId,
        title: this.title,
        recipient: {
            name: this.recipient.name
        },
        program: {
            title: this.program.title,
            language: this.program.language,
            category: this.program.category,
            level: this.program.level
        },
        issuedAt: this.issuedAt,
        verification: {
            hash: this.verification.hash,
            verificationUrl: this.verification.verificationUrl,
            isVerified: this.verification.isVerified
        },
        skills: this.skills,
        status: this.status,
        isValid: this.isValid
    };
};

// Métodos estáticos
certificateSchema.statics.findByUser = function(userId) {
    return this.find({ userId, status: 'active' })
        .populate('courseId', 'title language category level')
        .sort({ issuedAt: -1 });
};

certificateSchema.statics.findByCourse = function(courseId) {
    return this.find({ courseId, status: 'active' })
        .populate('userId', 'username profile.firstName profile.lastName')
        .sort({ issuedAt: -1 });
};

certificateSchema.statics.verifyByHash = function(hash) {
    return this.findOne({ 
        'verification.hash': hash,
        status: 'active'
    });
};

certificateSchema.statics.findByCertificateId = function(certificateId) {
    return this.findOne({ 
        certificateId,
        status: 'active'
    });
};

certificateSchema.statics.getStatsByUser = function(userId) {
    return this.aggregate([
        { $match: { userId: mongoose.Types.ObjectId(userId), status: 'active' } },
        {
            $group: {
                _id: '$type',
                count: { $sum: 1 },
                averageScore: { $avg: '$performance.finalScore' },
                totalStudyHours: { $sum: '$performance.totalStudyHours' }
            }
        }
    ]);
};

certificateSchema.statics.getStatsByCourse = function(courseId) {
    return this.aggregate([
        { $match: { courseId: mongoose.Types.ObjectId(courseId), status: 'active' } },
        {
            $group: {
                _id: null,
                totalCertificates: { $sum: 1 },
                averageScore: { $avg: '$performance.finalScore' },
                averageCompletionTime: { $avg: '$performance.completionTime' },
                excellentCount: {
                    $sum: { $cond: [{ $eq: ['$performance.rank', 'excellent'] }, 1, 0] }
                },
                goodCount: {
                    $sum: { $cond: [{ $eq: ['$performance.rank', 'good'] }, 1, 0] }
                },
                satisfactoryCount: {
                    $sum: { $cond: [{ $eq: ['$performance.rank', 'satisfactory'] }, 1, 0] }
                }
            }
        }
    ]);
};

certificateSchema.statics.findExpiringSoon = function(days = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() + days);
    
    return this.find({
        status: 'active',
        expiresAt: { $lte: cutoffDate, $gte: new Date() }
    }).populate('userId', 'username email');
};

certificateSchema.statics.getTopPerformers = function(courseId, limit = 10) {
    const query = courseId ? { courseId: mongoose.Types.ObjectId(courseId) } : {};
    
    return this.find({ ...query, status: 'active' })
        .sort({ 'performance.finalScore': -1, 'performance.completionTime': 1 })
        .limit(limit)
        .populate('userId', 'username profile.firstName profile.lastName')
        .populate('courseId', 'title language');
};

module.exports = mongoose.model('Certificate', certificateSchema);
