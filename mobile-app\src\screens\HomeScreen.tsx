/**
 * PANTALLA PRINCIPAL - HOME SCREEN
 * ================================
 * 
 * Pantalla de inicio de la aplicación móvil
 * Dashboard personalizado con progreso, cursos recomendados y actividad
 */

import React, {useEffect, useState, useCallback} from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  Dimensions,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Avatar,
  ProgressBar,
  Chip,
  FAB,
  Portal,
  Modal,
} from 'react-native-paper';
import {useTheme} from 'react-native-paper';
import {useFocusEffect} from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import * as Animatable from 'react-native-animatable';

// Components
import CourseCard from '../components/courses/CourseCard';
import ProgressChart from '../components/charts/ProgressChart';
import AchievementBadge from '../components/gamification/AchievementBadge';
import StudyStreakCard from '../components/progress/StudyStreakCard';
import QuickActionButton from '../components/common/QuickActionButton';
import LoadingSpinner from '../components/common/LoadingSpinner';
import ErrorMessage from '../components/common/ErrorMessage';

// Hooks
import {useAppSelector, useAppDispatch} from '../hooks/redux';
import {useNavigation} from '@react-navigation/native';

// Services
import {courseService} from '../services/courseService';
import {progressService} from '../services/progressService';
import {analyticsService} from '../services/analyticsService';

// Utils
import {formatTime, formatDate} from '../utils/dateUtils';
import {getGreeting} from '../utils/stringUtils';

// Types
import {Course, Progress, Achievement} from '../types';

const {width: screenWidth} = Dimensions.get('window');

const HomeScreen: React.FC = () => {
  const theme = useTheme();
  const navigation = useNavigation();
  const dispatch = useAppDispatch();

  // Redux state
  const {user} = useAppSelector(state => state.auth);
  const {courses, loading: coursesLoading} = useAppSelector(state => state.courses);
  const {progress, loading: progressLoading} = useAppSelector(state => state.progress);

  // Local state
  const [refreshing, setRefreshing] = useState(false);
  const [recommendedCourses, setRecommendedCourses] = useState<Course[]>([]);
  const [recentAchievements, setRecentAchievements] = useState<Achievement[]>([]);
  const [todayProgress, setTodayProgress] = useState<any>(null);
  const [showQuickActions, setShowQuickActions] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Focus effect to refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadHomeData();
      analyticsService.trackScreen('home');
    }, [])
  );

  const loadHomeData = async () => {
    try {
      setError(null);
      
      // Load recommended courses
      const recommended = await courseService.getRecommendedCourses(user?.id);
      setRecommendedCourses(recommended);

      // Load recent achievements
      const achievements = await progressService.getRecentAchievements(user?.id);
      setRecentAchievements(achievements);

      // Load today's progress
      const todayData = await progressService.getTodayProgress(user?.id);
      setTodayProgress(todayData);

    } catch (error) {
      console.error('Error loading home data:', error);
      setError('Error al cargar los datos. Intenta de nuevo.');
      analyticsService.trackError('home_data_load_error', error);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadHomeData();
    setRefreshing(false);
  }, []);

  const handleCoursePress = (course: Course) => {
    analyticsService.trackEvent('course_card_pressed', {
      courseId: course.id,
      source: 'home_recommended'
    });
    
    navigation.navigate('CourseDetail', {courseId: course.id});
  };

  const handleContinueLearning = () => {
    const lastCourse = progress?.find(p => p.lastAccessedAt);
    if (lastCourse) {
      navigation.navigate('CourseDetail', {courseId: lastCourse.courseId});
    } else {
      navigation.navigate('Courses');
    }
  };

  const handleQuickAction = (action: string) => {
    setShowQuickActions(false);
    
    switch (action) {
      case 'search':
        navigation.navigate('Search');
        break;
      case 'downloads':
        navigation.navigate('OfflineContent');
        break;
      case 'achievements':
        navigation.navigate('Achievements');
        break;
      case 'study_plan':
        navigation.navigate('StudyPlan');
        break;
      default:
        break;
    }
  };

  if (coursesLoading || progressLoading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <ErrorMessage
        message={error}
        onRetry={loadHomeData}
      />
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }>
        
        {/* Header with Greeting */}
        <LinearGradient
          colors={[theme.colors.primary, theme.colors.secondary]}
          style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.greetingSection}>
              <Text style={[styles.greeting, {color: theme.colors.onPrimary}]}>
                {getGreeting()}, {user?.profile?.firstName || user?.username}! 👋
              </Text>
              <Text style={[styles.subGreeting, {color: theme.colors.onPrimary}]}>
                ¿Listo para seguir aprendiendo?
              </Text>
            </View>
            <Avatar.Image
              size={50}
              source={{uri: user?.profile?.avatar || 'https://via.placeholder.com/50'}}
            />
          </View>
        </LinearGradient>

        {/* Today's Progress */}
        {todayProgress && (
          <Animatable.View animation="fadeInUp" delay={200}>
            <Card style={styles.progressCard}>
              <Card.Content>
                <Text style={styles.cardTitle}>Progreso de Hoy</Text>
                <View style={styles.progressRow}>
                  <View style={styles.progressItem}>
                    <Icon name="clock-outline" size={24} color={theme.colors.primary} />
                    <Text style={styles.progressValue}>
                      {formatTime(todayProgress.studyTime)}
                    </Text>
                    <Text style={styles.progressLabel}>Tiempo de estudio</Text>
                  </View>
                  <View style={styles.progressItem}>
                    <Icon name="check-circle-outline" size={24} color={theme.colors.primary} />
                    <Text style={styles.progressValue}>
                      {todayProgress.lessonsCompleted}
                    </Text>
                    <Text style={styles.progressLabel}>Lecciones</Text>
                  </View>
                  <View style={styles.progressItem}>
                    <Icon name="fire" size={24} color={theme.colors.primary} />
                    <Text style={styles.progressValue}>
                      {todayProgress.streak}
                    </Text>
                    <Text style={styles.progressLabel}>Racha</Text>
                  </View>
                </View>
              </Card.Content>
            </Card>
          </Animatable.View>
        )}

        {/* Study Streak */}
        <Animatable.View animation="fadeInUp" delay={400}>
          <StudyStreakCard
            currentStreak={user?.academicProgress?.currentStreak || 0}
            longestStreak={user?.academicProgress?.longestStreak || 0}
            lastStudyDate={user?.academicProgress?.lastActivityDate}
          />
        </Animatable.View>

        {/* Continue Learning */}
        {progress && progress.length > 0 && (
          <Animatable.View animation="fadeInUp" delay={600}>
            <Card style={styles.continueCard}>
              <Card.Content>
                <Text style={styles.cardTitle}>Continuar Aprendiendo</Text>
                {progress.slice(0, 2).map((courseProgress, index) => (
                  <View key={courseProgress.courseId} style={styles.courseProgressItem}>
                    <View style={styles.courseProgressInfo}>
                      <Text style={styles.courseTitle}>
                        {courseProgress.course?.title}
                      </Text>
                      <Text style={styles.courseLanguage}>
                        {courseProgress.course?.language}
                      </Text>
                    </View>
                    <View style={styles.progressBarContainer}>
                      <ProgressBar
                        progress={courseProgress.overallProgress.percentage / 100}
                        color={theme.colors.primary}
                        style={styles.progressBar}
                      />
                      <Text style={styles.progressPercentage}>
                        {courseProgress.overallProgress.percentage}%
                      </Text>
                    </View>
                  </View>
                ))}
                <Button
                  mode="contained"
                  onPress={handleContinueLearning}
                  style={styles.continueButton}>
                  Continuar
                </Button>
              </Card.Content>
            </Card>
          </Animatable.View>
        )}

        {/* Recent Achievements */}
        {recentAchievements.length > 0 && (
          <Animatable.View animation="fadeInUp" delay={800}>
            <Card style={styles.achievementsCard}>
              <Card.Content>
                <Text style={styles.cardTitle}>Logros Recientes</Text>
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  style={styles.achievementsScroll}>
                  {recentAchievements.map((achievement, index) => (
                    <AchievementBadge
                      key={achievement.id}
                      achievement={achievement}
                      style={styles.achievementBadge}
                    />
                  ))}
                </ScrollView>
              </Card.Content>
            </Card>
          </Animatable.View>
        )}

        {/* Recommended Courses */}
        <Animatable.View animation="fadeInUp" delay={1000}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Cursos Recomendados</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.coursesScroll}>
              {recommendedCourses.map((course, index) => (
                <CourseCard
                  key={course.id}
                  course={course}
                  onPress={() => handleCoursePress(course)}
                  style={[
                    styles.courseCard,
                    index === 0 && styles.firstCourseCard,
                    index === recommendedCourses.length - 1 && styles.lastCourseCard,
                  ]}
                />
              ))}
            </ScrollView>
          </View>
        </Animatable.View>

        {/* Quick Stats */}
        <Animatable.View animation="fadeInUp" delay={1200}>
          <Card style={styles.statsCard}>
            <Card.Content>
              <Text style={styles.cardTitle}>Estadísticas</Text>
              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>
                    {user?.academicProgress?.totalCoursesCompleted || 0}
                  </Text>
                  <Text style={styles.statLabel}>Cursos Completados</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>
                    {user?.academicProgress?.totalLessonsCompleted || 0}
                  </Text>
                  <Text style={styles.statLabel}>Lecciones</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>
                    {Math.round((user?.academicProgress?.totalStudyTimeMinutes || 0) / 60)}h
                  </Text>
                  <Text style={styles.statLabel}>Tiempo Total</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>
                    {user?.gamification?.level || 1}
                  </Text>
                  <Text style={styles.statLabel}>Nivel</Text>
                </View>
              </View>
            </Card.Content>
          </Card>
        </Animatable.View>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Floating Action Button */}
      <Portal>
        <FAB.Group
          open={showQuickActions}
          visible
          icon={showQuickActions ? 'close' : 'plus'}
          actions={[
            {
              icon: 'magnify',
              label: 'Buscar',
              onPress: () => handleQuickAction('search'),
            },
            {
              icon: 'download',
              label: 'Descargas',
              onPress: () => handleQuickAction('downloads'),
            },
            {
              icon: 'trophy',
              label: 'Logros',
              onPress: () => handleQuickAction('achievements'),
            },
            {
              icon: 'calendar-check',
              label: 'Plan de Estudio',
              onPress: () => handleQuickAction('study_plan'),
            },
          ]}
          onStateChange={({open}) => setShowQuickActions(open)}
          onPress={() => {
            if (showQuickActions) {
              // Close
            }
          }}
        />
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 30,
    paddingTop: 50,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  greetingSection: {
    flex: 1,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subGreeting: {
    fontSize: 16,
    opacity: 0.9,
  },
  progressCard: {
    margin: 16,
    marginTop: -20,
    elevation: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  progressRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  progressItem: {
    alignItems: 'center',
  },
  progressValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  progressLabel: {
    fontSize: 12,
    opacity: 0.7,
    textAlign: 'center',
  },
  continueCard: {
    margin: 16,
    elevation: 2,
  },
  courseProgressItem: {
    marginBottom: 16,
  },
  courseProgressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  courseTitle: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  courseLanguage: {
    fontSize: 12,
    opacity: 0.7,
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    flex: 1,
    height: 8,
    borderRadius: 4,
  },
  progressPercentage: {
    marginLeft: 12,
    fontSize: 14,
    fontWeight: '500',
    minWidth: 40,
    textAlign: 'right',
  },
  continueButton: {
    marginTop: 8,
  },
  achievementsCard: {
    margin: 16,
    elevation: 2,
  },
  achievementsScroll: {
    marginTop: 8,
  },
  achievementBadge: {
    marginRight: 12,
  },
  section: {
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginHorizontal: 16,
    marginBottom: 12,
  },
  coursesScroll: {
    paddingLeft: 16,
  },
  courseCard: {
    width: screenWidth * 0.7,
    marginRight: 12,
  },
  firstCourseCard: {
    marginLeft: 0,
  },
  lastCourseCard: {
    marginRight: 16,
  },
  statsCard: {
    margin: 16,
    elevation: 2,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  statItem: {
    width: '50%',
    alignItems: 'center',
    paddingVertical: 12,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
    textAlign: 'center',
  },
  bottomSpacing: {
    height: 100,
  },
});

export default HomeScreen;
