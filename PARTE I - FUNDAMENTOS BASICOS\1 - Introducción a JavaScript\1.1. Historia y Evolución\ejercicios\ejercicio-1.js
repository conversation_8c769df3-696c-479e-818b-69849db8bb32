/**
 * EJERCICIO 1 - Timeline de JavaScript
 * ====================================
 * 
 * Dificultad: Básico
 * Tiempo estimado: 10 minutos
 * 
 * OBJETIVO:
 * Crear un timeline interactivo de JavaScript que muestre los eventos
 * más importantes en la historia del lenguaje.
 */

// Instrucciones:
// 1. Completa el array 'timeline' con los eventos importantes de JavaScript
// 2. Cada evento debe tener: año, evento, descripción, importancia (1-5)
// 3. Implementa la función 'mostrarTimeline' para mostrar los eventos ordenados
// 4. Implementa la función 'buscarPorAño' para encontrar eventos de un año específico
// 5. Implementa la función 'eventosMasImportantes' para mostrar eventos con importancia >= 4

// Tu código aquí:

// 1. Completa el timeline con al menos 10 eventos importantes
const timeline = [
    {
        año: 1995,
        evento: "Creación de JavaScript",
        descripción: "<PERSON>ich crea JavaScript en Netscape en 10 días",
        importancia: 5
    },
    {
        año: 1997,
        evento: "ECMAScript 1",
        descripción: "Primera especificación oficial de JavaScript",
        importancia: 4
    },
    // TODO: Añade más eventos aquí
    // Sugerencias: ES3 (1999), ES5 (2009), Node.js (2009), ES6 (2015), etc.
];

// 2. Función para mostrar el timeline completo ordenado por año
function mostrarTimeline() {
    console.log("=== TIMELINE DE JAVASCRIPT ===\n");
    
    // TODO: Implementa esta función
    // - Ordena los eventos por año
    // - Muestra cada evento con formato bonito
    // - Incluye año, evento, descripción e importancia
    
    console.log("Timeline mostrado correctamente ✅");
}

// 3. Función para buscar eventos por año
function buscarPorAño(año) {
    console.log(`\n=== EVENTOS DEL AÑO ${año} ===`);
    
    // TODO: Implementa esta función
    // - Busca todos los eventos del año especificado
    // - Muestra los eventos encontrados
    // - Si no hay eventos, muestra un mensaje apropiado
    
    console.log("Búsqueda completada ✅");
}

// 4. Función para mostrar solo los eventos más importantes
function eventosMasImportantes() {
    console.log("\n=== EVENTOS MÁS IMPORTANTES (Importancia >= 4) ===");
    
    // TODO: Implementa esta función
    // - Filtra eventos con importancia >= 4
    // - Muestra los eventos filtrados
    // - Ordena por importancia (descendente) y luego por año
    
    console.log("Eventos importantes mostrados ✅");
}

// 5. Función para añadir un nuevo evento al timeline
function añadirEvento(año, evento, descripción, importancia) {
    // TODO: Implementa esta función
    // - Valida que todos los parámetros estén presentes
    // - Valida que el año sea un número válido
    // - Valida que la importancia esté entre 1 y 5
    // - Añade el evento al timeline
    // - Muestra confirmación
    
    console.log(`Evento "${evento}" añadido correctamente ✅`);
}

// 6. Función para obtener estadísticas del timeline
function obtenerEstadisticas() {
    console.log("\n=== ESTADÍSTICAS DEL TIMELINE ===");
    
    // TODO: Implementa esta función para mostrar:
    // - Total de eventos
    // - Año del primer evento
    // - Año del último evento
    // - Promedio de importancia
    // - Década con más eventos
    
    console.log("Estadísticas calculadas ✅");
}

// Tests (no modificar)
function test() {
    console.log("🧪 EJECUTANDO TESTS...\n");
    
    // Test 1: Timeline debe tener al menos 10 eventos
    if (timeline.length >= 10) {
        console.log("✅ Test 1 PASADO: Timeline tiene suficientes eventos");
    } else {
        console.log("❌ Test 1 FALLIDO: Timeline necesita al menos 10 eventos");
    }
    
    // Test 2: Todos los eventos deben tener las propiedades requeridas
    const propiedadesRequeridas = ['año', 'evento', 'descripción', 'importancia'];
    let todosCompletos = true;
    
    timeline.forEach((evento, index) => {
        propiedadesRequeridas.forEach(prop => {
            if (!evento.hasOwnProperty(prop)) {
                console.log(`❌ Test 2 FALLIDO: Evento ${index} no tiene la propiedad '${prop}'`);
                todosCompletos = false;
            }
        });
    });
    
    if (todosCompletos) {
        console.log("✅ Test 2 PASADO: Todos los eventos tienen las propiedades requeridas");
    }
    
    // Test 3: Importancia debe estar entre 1 y 5
    let importanciaValida = true;
    timeline.forEach((evento, index) => {
        if (evento.importancia < 1 || evento.importancia > 5) {
            console.log(`❌ Test 3 FALLIDO: Evento ${index} tiene importancia inválida: ${evento.importancia}`);
            importanciaValida = false;
        }
    });
    
    if (importanciaValida) {
        console.log("✅ Test 3 PASADO: Todas las importancias están en rango válido (1-5)");
    }
    
    // Ejecutar funciones para verificar implementación
    console.log("\n🔍 VERIFICANDO IMPLEMENTACIÓN...\n");
    
    try {
        mostrarTimeline();
        buscarPorAño(2015);
        eventosMasImportantes();
        añadirEvento(2024, "Nuevo Feature", "Descripción del nuevo feature", 3);
        obtenerEstadisticas();
        
        console.log("\n🎉 ¡Todas las funciones ejecutadas correctamente!");
    } catch (error) {
        console.log(`\n❌ Error en la implementación: ${error.message}`);
    }
}

// Ejecutar tests
test();

// Ejemplo de uso (descomenta para probar)
/*
console.log("\n" + "=".repeat(50));
console.log("EJEMPLO DE USO:");
console.log("=".repeat(50));

mostrarTimeline();
buscarPorAño(2009);
eventosMasImportantes();
obtenerEstadisticas();
*/

// PISTAS PARA LA IMPLEMENTACIÓN:
// 
// Para ordenar por año:
// timeline.sort((a, b) => a.año - b.año)
//
// Para filtrar por año:
// timeline.filter(evento => evento.año === año)
//
// Para filtrar por importancia:
// timeline.filter(evento => evento.importancia >= 4)
//
// Para calcular promedio:
// const suma = timeline.reduce((acc, evento) => acc + evento.importancia, 0);
// const promedio = suma / timeline.length;
//
// Para agrupar por década:
// const decada = Math.floor(evento.año / 10) * 10;
