<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
  <!-- Definiciones -->
  <defs>
    <!-- Flecha mejorada -->
    <marker id="arrow" markerWidth="12" markerHeight="12" refX="6" refY="3" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,6 L9,3 z" fill="#2c3e50" stroke="#2c3e50" stroke-width="1"/>
    </marker>
    
    <!-- Gradientes -->
    <linearGradient id="startGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="varGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2ecc71;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#27ae60;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="functionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9b59b6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8e44ad;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="consoleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f39c12;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e67e22;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="errorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
    </linearGradient>
    
    <!-- Sombra -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Título -->
  <text x="600" y="30" font-size="20" font-weight="bold" text-anchor="middle" fill="#2c3e50">Scope de Variables en JavaScript</text>

  <!-- Inicio -->
  <ellipse cx="600" cy="80" rx="90" ry="35" fill="url(#startGradient)" stroke="#1f618d" stroke-width="2" filter="url(#shadow)"/>
  <text x="600" y="85" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">INICIO</text>

  <!-- Global Scope Box -->
  <rect x="450" y="120" width="300" height="520" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" stroke-dasharray="5,5" rx="15" ry="15" opacity="0.3"/>
  <text x="460" y="140" font-size="14" font-weight="bold" fill="#7f8c8d">GLOBAL SCOPE</text>

  <!-- globalVar -->
  <rect x="500" y="160" width="200" height="50" fill="url(#varGradient)" stroke="#27ae60" stroke-width="2" rx="10" ry="10" filter="url(#shadow)"/>
  <text x="600" y="185" font-size="14" font-weight="bold" text-anchor="middle" fill="#fff">let globalVar = 'Global'</text>

  <!-- function ejemplo() -->
  <rect x="500" y="230" width="200" height="50" fill="url(#functionGradient)" stroke="#8e44ad" stroke-width="2" rx="10" ry="10" filter="url(#shadow)"/>
  <text x="600" y="255" font-size="14" font-weight="bold" text-anchor="middle" fill="#fff">function ejemplo()</text>

  <!-- llamada ejemplo() -->
  <rect x="500" y="300" width="200" height="50" fill="url(#functionGradient)" stroke="#8e44ad" stroke-width="2" rx="10" ry="10" filter="url(#shadow)"/>
  <text x="600" y="325" font-size="14" font-weight="bold" text-anchor="middle" fill="#fff">ejemplo()</text>

  <!-- Function Scope Box -->
  <rect x="480" y="370" width="240" height="260" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" stroke-dasharray="5,5" rx="10" ry="10" opacity="0.4"/>
  <text x="490" y="390" font-size="12" font-weight="bold" fill="#27ae60">FUNCTION SCOPE</text>

  <!-- localVar -->
  <rect x="500" y="400" width="200" height="50" fill="url(#varGradient)" stroke="#27ae60" stroke-width="2" rx="10" ry="10" filter="url(#shadow)"/>
  <text x="600" y="425" font-size="14" font-weight="bold" text-anchor="middle" fill="#fff">let localVar = 'Local'</text>

  <!-- if (true) -->
  <polygon points="600,470 650,500 600,530 550,500" fill="url(#consoleGradient)" stroke="#e67e22" stroke-width="2" filter="url(#shadow)"/>
  <text x="600" y="505" font-size="14" font-weight="bold" text-anchor="middle" fill="#fff">if (true)</text>

  <!-- Block Scope Box -->
  <rect x="350" y="550" width="500" height="150" fill="#fff3cd" stroke="#f39c12" stroke-width="2" stroke-dasharray="3,3" rx="10" ry="10" opacity="0.5"/>
  <text x="360" y="570" font-size="12" font-weight="bold" fill="#f39c12">BLOCK SCOPE</text>

  <!-- blockVar -->
  <rect x="500" y="580" width="200" height="40" fill="url(#varGradient)" stroke="#27ae60" stroke-width="2" rx="8" ry="8" filter="url(#shadow)"/>
  <text x="600" y="605" font-size="13" font-weight="bold" text-anchor="middle" fill="#fff">let blockVar = 'Bloque'</text>

  <!-- Consoles dentro del bloque -->
  <rect x="220" y="640" width="180" height="40" fill="url(#consoleGradient)" stroke="#e67e22" stroke-width="2" rx="8" ry="8" filter="url(#shadow)"/>
  <text x="310" y="665" font-size="12" font-weight="bold" text-anchor="middle" fill="#fff">console.log(globalVar)</text>
  <text x="310" y="685" font-size="10" fill="#27ae60">✓ Accesible</text>

  <rect x="420" y="640" width="160" height="40" fill="url(#consoleGradient)" stroke="#e67e22" stroke-width="2" rx="8" ry="8" filter="url(#shadow)"/>
  <text x="500" y="665" font-size="12" font-weight="bold" text-anchor="middle" fill="#fff">console.log(localVar)</text>
  <text x="500" y="685" font-size="10" fill="#27ae60">✓ Accesible</text>

  <rect x="600" y="640" width="160" height="40" fill="url(#consoleGradient)" stroke="#e67e22" stroke-width="2" rx="8" ry="8" filter="url(#shadow)"/>
  <text x="680" y="665" font-size="12" font-weight="bold" text-anchor="middle" fill="#fff">console.log(blockVar)</text>
  <text x="680" y="685" font-size="10" fill="#27ae60">✓ Accesible</text>

  <!-- Error fuera del bloque -->
  <rect x="480" y="740" width="240" height="50" fill="url(#errorGradient)" stroke="#c0392b" stroke-width="2" rx="10" ry="10" filter="url(#shadow)"/>
  <text x="600" y="765" font-size="13" font-weight="bold" text-anchor="middle" fill="#fff">console.log(blockVar)</text>
  <text x="600" y="780" font-size="12" font-weight="bold" text-anchor="middle" fill="#fff">❌ ReferenceError</text>

  <!-- Fin -->
  <ellipse cx="600" cy="840" rx="90" ry="35" fill="url(#startGradient)" stroke="#1f618d" stroke-width="2" filter="url(#shadow)"/>
  <text x="600" y="845" font-size="16" font-weight="bold" text-anchor="middle" fill="#fff">FIN</text>

  <!-- Flechas principales -->
  <line x1="600" y1="115" x2="600" y2="160" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="600" y1="210" x2="600" y2="230" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="600" y1="280" x2="600" y2="300" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="600" y1="350" x2="600" y2="400" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="600" y1="450" x2="600" y2="470" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="600" y1="530" x2="600" y2="580" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>

  <!-- Flechas a los consoles -->
  <line x1="600" y1="620" x2="310" y2="640" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="600" y1="620" x2="500" y2="640" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="600" y1="620" x2="680" y2="640" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>

  <!-- Convergencia al error -->
  <line x1="310" y1="680" x2="600" y2="740" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="500" y1="680" x2="600" y2="740" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="680" y1="680" x2="600" y2="740" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>

  <!-- Error a fin -->
  <line x1="600" y1="790" x2="600" y2="805" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrow)"/>

  <!-- Etiquetas de scope -->
  <text x="100" y="300" font-size="14" font-weight="bold" fill="#3498db" transform="rotate(-90 100 300)">EJECUCIÓN</text>
  
  <!-- Leyenda -->
  <rect x="50" y="50" width="200" height="120" fill="#fff" stroke="#bdc3c7" stroke-width="1" rx="5" ry="5" opacity="0.9"/>
  <text x="60" y="70" font-size="14" font-weight="bold" fill="#2c3e50">LEYENDA:</text>
  <rect x="60" y="80" width="15" height="15" fill="url(#varGradient)"/>
  <text x="85" y="92" font-size="12" fill="#2c3e50">Variables</text>
  <rect x="60" y="100" width="15" height="15" fill="url(#functionGradient)"/>
  <text x="85" y="112" font-size="12" fill="#2c3e50">Funciones</text>
  <rect x="60" y="120" width="15" height="15" fill="url(#consoleGradient)"/>
  <text x="85" y="132" font-size="12" fill="#2c3e50">Console.log</text>
  <rect x="60" y="140" width="15" height="15" fill="url(#errorGradient)"/>
  <text x="85" y="152" font-size="12" fill="#2c3e50">Error</text>
</svg>