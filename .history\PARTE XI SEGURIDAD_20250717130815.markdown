## **PARTE XI: SEGURIDAD**

### **Capítulo 134: Fundamentos de Seguridad Web**

#### **134.1. Seguridad Web Fundamentals**
134.1.1. ¿Qué es la seguridad web?
134.1.2. Threat landscape
134.1.3. Attack vectors
134.1.4. Security principles
134.1.5. Defense in depth
134.1.6. Risk assessment
134.1.7. Security by design
134.1.8. Compliance requirements
134.1.9. Security culture
134.1.10. Best practices

#### **134.2. Common Vulnerabilities**
134.2.1. OWASP Top 10
134.2.2. Injection attacks
134.2.3. Cross-Site Scripting (XSS)
134.2.4. Cross-Site Request Forgery (CSRF)
134.2.5. Insecure authentication
134.2.6. Session management flaws
134.2.7. Insecure direct object references
134.2.8. Security misconfigurations
134.2.9. Sensitive data exposure
134.2.10. Insufficient logging

#### **134.3. JavaScript Security Context**
134.3.1. Client-side security
134.3.2. Server-side security
134.3.3. Browser security model
134.3.4. Same-origin policy
134.3.5. Content Security Policy
134.3.6. Secure contexts
134.3.7. JavaScript sandboxing
134.3.8. Third-party risks
134.3.9. Supply chain security
134.3.10. Runtime security

#### **134.4. Security Assessment**
134.4.1. Vulnerability scanning
134.4.2. Penetration testing
134.4.3. Code review
134.4.4. Security audits
134.4.5. Threat modeling
134.4.6. Risk analysis
134.4.7. Security testing
134.4.8. Compliance testing
134.4.9. Continuous assessment
134.4.10. Remediation planning

### **Capítulo 135: XSS Prevention**

#### **135.1. Cross-Site Scripting (XSS)**
135.1.1. ¿Qué es XSS?
135.1.2. Types of XSS
135.1.3. Reflected XSS
135.1.4. Stored XSS
135.1.5. DOM-based XSS
135.1.6. Blind XSS
135.1.7. Self-XSS
135.1.8. XSS impact
135.1.9. Attack vectors
135.1.10. Real-world examples

#### **135.2. XSS Prevention Techniques**
135.2.1. Input validation
135.2.2. Output encoding
135.2.3. HTML sanitization
135.2.4. Context-aware encoding
135.2.5. Content Security Policy
135.2.6. X-XSS-Protection header
135.2.7. HttpOnly cookies
135.2.8. Secure coding practices
135.2.9. Framework protections
135.2.10. Testing strategies

#### **135.3. Input Validation**
135.3.1. Validation principles
135.3.2. Whitelist validation
135.3.3. Blacklist validation
135.3.4. Regular expressions
135.3.5. Type validation
135.3.6. Length validation
135.3.7. Format validation
135.3.8. Business logic validation
135.3.9. Client vs server validation
135.3.10. Validation libraries

#### **135.4. Output Encoding**
135.4.1. Encoding principles
135.4.2. HTML encoding
135.4.3. JavaScript encoding
135.4.4. CSS encoding
135.4.5. URL encoding
135.4.6. Attribute encoding
135.4.7. Context-specific encoding
135.4.8. Double encoding
135.4.9. Encoding libraries
135.4.10. Testing encoding

### **Capítulo 136: CSRF Protection**

#### **136.1. Cross-Site Request Forgery (CSRF)**
136.1.1. ¿Qué es CSRF?
136.1.2. CSRF attack mechanics
136.1.3. Attack scenarios
136.1.4. Impact assessment
136.1.5. Vulnerable operations
136.1.6. Social engineering
136.1.7. Real-world examples
136.1.8. Detection techniques
136.1.9. Risk factors
136.1.10. Prevention overview

#### **136.2. CSRF Tokens**
136.2.1. Token-based protection
136.2.2. Synchronizer tokens
136.2.3. Double submit cookies
136.2.4. Token generation
136.2.5. Token validation
136.2.6. Token lifecycle
136.2.7. Token storage
136.2.8. AJAX integration
136.2.9. SPA considerations
136.2.10. Token best practices

#### **136.3. SameSite Cookies**
136.3.1. SameSite attribute
136.3.2. Strict mode
136.3.3. Lax mode
136.3.4. None mode
136.3.5. Browser support
136.3.6. Implementation strategies
136.3.7. Migration considerations
136.3.8. Testing approaches
136.3.9. Compatibility issues
136.3.10. Best practices

#### **136.4. Additional CSRF Protections**
136.4.1. Origin header validation
136.4.2. Referer header validation
136.4.3. Custom headers
136.4.4. CAPTCHA integration
136.4.5. Re-authentication
136.4.6. Transaction signing
136.4.7. Rate limiting
136.4.8. User interaction requirements
136.4.9. Framework protections
136.4.10. Defense in depth

### **Capítulo 137: Content Security Policy**

#### **137.1. CSP Fundamentals**
137.1.1. ¿Qué es CSP?
137.1.2. CSP objectives
137.1.3. Policy directives
137.1.4. Source expressions
137.1.5. Policy delivery
137.1.6. Browser support
137.1.7. CSP levels
137.1.8. Enforcement modes
137.1.9. Reporting mechanisms
137.1.10. Best practices

#### **137.2. CSP Directives**
137.2.1. default-src
137.2.2. script-src
137.2.3. style-src
137.2.4. img-src
137.2.5. connect-src
137.2.6. font-src
137.2.7. object-src
137.2.8. media-src
137.2.9. frame-src
137.2.10. worker-src

#### **137.3. CSP Implementation**
137.3.1. Policy development
137.3.2. Gradual deployment
137.3.3. Report-only mode
137.3.4. Nonce implementation
137.3.5. Hash implementation
137.3.6. Unsafe-inline alternatives
137.3.7. Third-party integration
137.3.8. Framework integration
137.3.9. Testing strategies
137.3.10. Monitoring y reporting

#### **137.4. Advanced CSP**
137.4.1. CSP Level 3 features
137.4.2. Trusted Types
137.4.3. Strict CSP
137.4.4. CSP for SPAs
137.4.5. CSP for APIs
137.4.6. CSP automation
137.4.7. CSP tools
137.4.8. Performance impact
137.4.9. Debugging CSP
137.4.10. Future developments

### **Capítulo 138: HTTPS y TLS**

#### **138.1. HTTPS Fundamentals**
138.1.1. ¿Qué es HTTPS?
138.1.2. TLS/SSL protocols
138.1.3. Encryption basics
138.1.4. Certificate authorities
138.1.5. Public key infrastructure
138.1.6. Handshake process
138.1.7. Security benefits
138.1.8. Performance considerations
138.1.9. SEO benefits
138.1.10. Migration strategies

#### **138.2. TLS Configuration**
138.2.1. TLS versions
138.2.2. Cipher suites
138.2.3. Perfect forward secrecy
138.2.4. HSTS implementation
138.2.5. Certificate management
138.2.6. Certificate pinning
138.2.7. OCSP stapling
138.2.8. Security headers
138.2.9. Performance optimization
138.2.10. Testing tools

#### **138.3. Certificate Management**
138.3.1. Certificate types
138.3.2. Certificate authorities
138.3.3. Let's Encrypt
138.3.4. Certificate lifecycle
138.3.5. Renewal automation
138.3.6. Certificate monitoring
138.3.7. Wildcard certificates
138.3.8. Multi-domain certificates
138.3.9. Certificate transparency
138.3.10. Best practices

#### **138.4. HTTPS Implementation**
138.4.1. Server configuration
138.4.2. Redirect strategies
138.4.3. Mixed content issues
138.4.4. Secure cookies
138.4.5. HSTS deployment
138.4.6. Performance optimization
138.4.7. CDN integration
138.4.8. Load balancer configuration
138.4.9. Monitoring y alerting
138.4.10. Troubleshooting

### **Capítulo 139: Authentication y Authorization**

#### **139.1. Authentication Fundamentals**
139.1.1. Authentication vs authorization
139.1.2. Authentication factors
139.1.3. Password-based authentication
139.1.4. Multi-factor authentication
139.1.5. Biometric authentication
139.1.6. Certificate-based authentication
139.1.7. Token-based authentication
139.1.8. Federated authentication
139.1.9. Single sign-on
139.1.10. Authentication flows

#### **139.2. Password Security**
139.2.1. Password policies
139.2.2. Password hashing
139.2.3. Salt generation
139.2.4. Hash algorithms
139.2.5. Password storage
139.2.6. Password validation
139.2.7. Password reset
139.2.8. Account lockout
139.2.9. Brute force protection
139.2.10. Password managers

#### **139.3. OAuth y OpenID Connect**
139.3.1. OAuth 2.0 framework
139.3.2. Authorization flows
139.3.3. Access tokens
139.3.4. Refresh tokens
139.3.5. Scope management
139.3.6. OpenID Connect
139.3.7. ID tokens
139.3.8. PKCE extension
139.3.9. Security considerations
139.3.10. Implementation best practices

#### **139.4. Authorization Patterns**
139.4.1. Role-based access control
139.4.2. Attribute-based access control
139.4.3. Permission systems
139.4.4. Resource-based authorization
139.4.5. Hierarchical permissions
139.4.6. Dynamic authorization
139.4.7. Policy engines
139.4.8. Authorization caching
139.4.9. Audit logging
139.4.10. Testing strategies
