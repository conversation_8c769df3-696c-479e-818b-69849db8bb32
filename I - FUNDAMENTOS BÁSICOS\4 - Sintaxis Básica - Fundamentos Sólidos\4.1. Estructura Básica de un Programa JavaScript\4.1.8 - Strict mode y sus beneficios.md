## 4.1.8. Strict mode y sus beneficios

### Introducción
El strict mode en JavaScript es una directiva que habilita reglas más estrictas.  
Ayuda a detectar errores silenciosos convirtiéndolos en excepciones.  
Esto es crucial para escribir código más seguro y depurable.  
Además, optimiza el rendimiento en algunos casos.  
Usarlo es una mejor práctica para desarrollo moderno.  
¿ Cuáles beneficios ves en activar strict mode?

### Código de Ejemplo
```javascript
'use strict';
let x = 5;
// y = 10; // Error en strict mode
console.log(x);
function prueba() {
    'use strict';
    // z = 15; // Error
    console.log('Función en strict');
}
prueba();
```
### Resultados en Consola
- `5`
- `Función en strict`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> Strict["Paso 1: 'use strict' <br> - Habilitar modo estricto"]
    Strict --> Declaracion["Paso 2: let x = 5 <br> - Declaración permitida"]
    Declaracion --> Comentario["Paso 3: // y = 10 <br> - Error si descomentado"]
    Comentario --> Log1["Paso 4: console.log(x) <br> - Resultado: 5"]
    Log1 --> Funcion["Paso 5: Definir función prueba() <br> - Con 'use strict' interno"]
    Funcion --> Comentario2["Paso 6: // z = 15 <br> - Error si descomentado"]
    Comentario2 --> Log2["Paso 7: console.log('Función en strict') <br> - Resultado: Función en strict"]
    Log2 --> Llamada["Paso 8: prueba()"]
    Llamada --> Fin["Paso 9: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Beneficios["Beneficios"]
    Strict --> NotaStrict["Nota: Detectar errores"]
    Funcion --> NotaFuncion["Nota: Strict por función"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Strict fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Declaracion fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Comentario fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:12px
    style Log1 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Funcion fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Comentario2 fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:12px
    style Log2 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Llamada fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:12px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Beneficios fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaStrict fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaFuncion fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Habilitar["Habilitar <br> - 'use strict'"]
        Habilitar --> Detectar["Detectar <br> - Errores silenciosos"]
        Detectar --> Lanzar["Lanzar <br> - Excepciones"]
        Lanzar --> Optimizar["Optimizar <br> - Rendimiento"]
        Optimizar --> Segurizar["Segurizar <br> - Código"]
        Inicio --> Desarrollador(("Desarrollador")) --> Modo["Modo"]
        Habilitar --> NotaBeneficio["Nota: Mejora depuración"]
        Detectar --> NotaError["Nota: Prevenir bugs"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Habilitar fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Detectar fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Lanzar fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Optimizar fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Segurizar fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Modo fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaBeneficio fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaError fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Habilitar strict mode en scripts para detectar asignaciones no declaradas.
2. Usar en funciones para reglas estrictas locales.
3. Prevenir uso de palabras reservadas como variables.
4. Mejorar seguridad al prohibir 'this' global.
5. Optimizar código para motores JS modernos.

### Errores comunes
1. Olvidar 'use strict' y permitir errores silenciosos.
2. Usar strict mode en código legacy incompatible.
3. No manejar excepciones lanzadas por strict mode.
4. Confundir scope de strict mode en módulos.
5. Ignorar beneficios en proyectos pequeños.

### Recomendaciones
1. Siempre incluye 'use strict' al inicio de archivos.
2. Prueba código en strict mode durante desarrollo.
3. Entiende diferencias con non-strict para migraciones.
4. Usa linters que enforcen strict mode rules.
5. Documenta uso de strict mode en el equipo.