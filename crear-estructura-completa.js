#!/usr/bin/env node

/**
 * GENERADOR COMPLETO DE ESTRUCTURA DEL CURSO
 * ==========================================
 * 
 * Crea toda la estructura de carpetas, archivos y contenido base
 * para el Curso Completo de JavaScript - Maestría Profesional
 */

const fs = require('fs');
const path = require('path');

// Estructura completa del curso con todos los 95 capítulos
const estructuraCompleta = {
  "PARTE I - FUNDAMENTOS BÁSICOS": {
    capitulos: [
      { num: 1, nombre: "Introducción a JavaScript", temas: ["Historia y Evolución", "Características del Lenguaje", "Ecosistema JavaScript", "Casos de Uso", "Futuro del Lenguaje"] },
      { num: 2, nombre: "Configuración del Entorno", temas: ["Instalación de Node.js", "Configuración de VS Code", "Extensiones Esenciales", "Terminal y CLI", "Debugging Setup"] },
      { num: 3, nombre: "Primeros Pasos", temas: ["Hola Mundo", "Sintaxis Básica", "Comentarios", "Estructura de Archivos", "Ejecución de Código"] },
      { num: 4, nombre: "Sintaxis Básica", temas: ["Tokens y Lexemas", "Palabras Reservadas", "Identificadores", "Literales", "Operadores Básicos"] },
      { num: 5, nombre: "Variables y Declaraciones", temas: ["var vs let vs const", "Scope de Variables", "Hoisting", "Temporal Dead Zone", "Best Practices"] },
      { num: 6, nombre: "Tipos de Datos Primitivos", temas: ["Number", "String", "Boolean", "Undefined y Null", "Symbol y BigInt"] },
      { num: 7, nombre: "Conversión de Tipos", temas: ["Coerción Implícita", "Coerción Explícita", "Truthy y Falsy", "Comparaciones", "Casos Edge"] },
      { num: 8, nombre: "Operadores Básicos", temas: ["Aritméticos", "Asignación", "Incremento/Decremento", "Precedencia", "Asociatividad"] },
      { num: 9, nombre: "Operadores de Comparación y Lógicos", temas: ["Igualdad == vs ===", "Comparación", "Lógicos &&, ||, !", "Nullish Coalescing", "Optional Chaining"] },
      { num: 10, nombre: "Condicionales", temas: ["if/else", "else if", "Operador Ternario", "Switch Statement", "Conditional Logic"] },
      { num: 11, nombre: "Bucles Básicos", temas: ["for Loop", "while Loop", "do-while Loop", "Nested Loops", "Loop Control"] },
      { num: 12, nombre: "Bucles Avanzados", temas: ["for...in", "for...of", "forEach", "Iteradores", "Performance"] },
      { num: 13, nombre: "Control de Flujo", temas: ["break", "continue", "return", "Labels", "Exception Flow"] },
      { num: 14, nombre: "Manejo de Errores Básico", temas: ["try/catch", "finally", "throw", "Error Types", "Error Handling Patterns"] }
    ]
  },
  "PARTE II - ESTRUCTURAS DE DATOS": {
    capitulos: [
      { num: 15, nombre: "Arrays Fundamentales", temas: ["Creación de Arrays", "Acceso a Elementos", "Propiedades", "Métodos Básicos", "Array Literals"] },
      { num: 16, nombre: "Métodos de Arrays", temas: ["Mutación", "Iteración", "Búsqueda", "Transformación", "Reducción"] },
      { num: 17, nombre: "Arrays Multidimensionales", temas: ["Matrices", "Navegación", "Algoritmos", "Performance", "Casos de Uso"] },
      { num: 18, nombre: "Objetos Literales", temas: ["Creación", "Propiedades", "Métodos", "this Context", "Object Patterns"] },
      { num: 19, nombre: "Propiedades de Objetos", temas: ["Descriptores", "Getters/Setters", "Enumerabilidad", "Configurabilidad", "Writable"] },
      { num: 20, nombre: "Métodos de Object", temas: ["Object.keys", "Object.values", "Object.entries", "Object.assign", "Object.create"] },
      { num: 21, nombre: "Prototipos", temas: ["Prototype Chain", "__proto__", "Object.getPrototypeOf", "Inheritance", "Constructor Functions"] },
      { num: 22, nombre: "Maps y Sets", temas: ["Map Fundamentals", "Set Operations", "WeakMap", "WeakSet", "Use Cases"] },
      { num: 23, nombre: "Destructuring", temas: ["Array Destructuring", "Object Destructuring", "Nested Destructuring", "Default Values", "Rest Patterns"] },
      { num: 24, nombre: "Spread y Rest", temas: ["Spread Operator", "Rest Parameters", "Array Spreading", "Object Spreading", "Function Arguments"] },
      { num: 25, nombre: "Iteradores y Generadores", temas: ["Iterator Protocol", "Generator Functions", "yield", "Async Generators", "Custom Iterators"] },
      { num: 26, nombre: "JSON", temas: ["JSON.parse", "JSON.stringify", "Serialización", "Deserialización", "Error Handling"] },
      { num: 27, nombre: "Estructuras Inmutables", temas: ["Immutability", "Object.freeze", "Deep Cloning", "Immutable Patterns", "Libraries"] },
      { num: 28, nombre: "Algoritmos de Búsqueda", temas: ["Linear Search", "Binary Search", "Hash Tables", "Performance", "Implementation"] },
      { num: 29, nombre: "Algoritmos de Ordenamiento", temas: ["Bubble Sort", "Quick Sort", "Merge Sort", "Performance Analysis", "Native Sort"] }
    ]
  }
  // Continuaré con las demás partes...
};

// Función para crear directorio
function crearDirectorio(ruta) {
  if (!fs.existsSync(ruta)) {
    fs.mkdirSync(ruta, { recursive: true });
    console.log(`✅ Creado: ${ruta}`);
  }
}

// Función para crear archivo con contenido
function crearArchivo(ruta, contenido) {
  if (!fs.existsSync(ruta)) {
    fs.writeFileSync(ruta, contenido, 'utf8');
    console.log(`📄 Creado: ${ruta}`);
  }
}

// Función para crear README de capítulo
function crearREADMECapitulo(rutaCapitulo, numeroCapitulo, nombreCapitulo, temas) {
  const contenido = `# **Capítulo ${numeroCapitulo} - ${nombreCapitulo}**

## **📖 Descripción del Capítulo**

Este capítulo cubre los aspectos fundamentales de ${nombreCapitulo.toLowerCase()}, proporcionando una base sólida para el desarrollo profesional en JavaScript.

## **🎯 Objetivos de Aprendizaje**

Al completar este capítulo, serás capaz de:

${temas.map(tema => `- [ ] Dominar ${tema.toLowerCase()}`).join('\n')}

## **📊 Información del Capítulo**

- **Dificultad:** ⭐⭐⭐ (Intermedio)
- **Tiempo estimado:** 6-8 horas
- **Temas:** ${temas.length}
- **Subtemas:** ${temas.length * 10}
- **Ejercicios prácticos:** ${temas.length * 5}

## **📋 Índice de Temas**

${temas.map((tema, index) => `### **[${numeroCapitulo}.${index + 1}. ${tema}](${numeroCapitulo}.${index + 1}.%20${tema.replace(/\s+/g, '%20')}/README.md)** ⭐⭐⭐
**Tiempo:** 1-2 horas | **Subtemas:** 10

Aprende todo sobre ${tema.toLowerCase()} con ejemplos prácticos y ejercicios.`).join('\n\n---\n\n')}

---

## **🎯 Rutas de Aprendizaje del Capítulo**

### **🚀 Ruta Rápida (3-4 horas)**
Conceptos esenciales del capítulo.

### **📚 Ruta Completa (6-8 horas)**
Cobertura completa de todos los temas.

### **🔬 Ruta Experto (8-10 horas)**
Dominio completo y casos avanzados.

---

## **📊 Progreso del Capítulo**

\`\`\`
${temas.map((tema, index) => `Tema ${numeroCapitulo}.${index + 1}: [░░░░░░░░░░] 0% completado`).join('\n')}

Progreso Total: [░░░░░░░░░░] 0% completado
\`\`\`

## **🏆 Logros del Capítulo**

${temas.map((tema, index) => `- 🎯 **${tema.split(' ')[0]}**: Completar tema ${numeroCapitulo}.${index + 1}`).join('\n')}
- 🚀 **Chapter Master**: Completar todos los temas

---

## **📝 Evaluación del Capítulo**

### **Quiz Final**
- ${temas.length * 5} preguntas sobre los temas
- Tiempo límite: ${Math.ceil(temas.length * 2.5)} minutos
- Puntuación mínima: 80%

### **Proyecto Práctico**
Proyecto hands-on que integra todos los conceptos del capítulo.

---

## **➡️ Navegación**

⬅️ **Anterior:** [Capítulo ${numeroCapitulo - 1}](../${numeroCapitulo - 1}%20-%20*/README.md)  
➡️ **Siguiente:** [Capítulo ${numeroCapitulo + 1}](../${numeroCapitulo + 1}%20-%20*/README.md)  
🏠 **Parte:** [Volver a la parte](../README.md)

---

**¡Domina este capítulo y avanza en tu expertise!** 🚀`;

  crearArchivo(path.join(rutaCapitulo, 'README.md'), contenido);
}

// Función para crear estructura de tema
function crearEstructuraTema(rutaTema, numeroCapitulo, numeroTema, nombreTema) {
  // Crear README del tema
  const contenidoREADME = `# **${numeroCapitulo}.${numeroTema}. ${nombreTema}**

## **📖 Descripción del Tema**

Explora en profundidad ${nombreTema.toLowerCase()} y su aplicación práctica en el desarrollo JavaScript profesional.

## **🎯 Objetivos de Aprendizaje**

Al completar este tema, serás capaz de:

- [ ] Comprender los conceptos fundamentales de ${nombreTema.toLowerCase()}
- [ ] Aplicar ${nombreTema.toLowerCase()} en proyectos reales
- [ ] Resolver problemas comunes relacionados con ${nombreTema.toLowerCase()}
- [ ] Optimizar el uso de ${nombreTema.toLowerCase()} para mejor rendimiento
- [ ] Implementar mejores prácticas de ${nombreTema.toLowerCase()}

## **📊 Información del Tema**

- **Dificultad:** ⭐⭐⭐ (Intermedio)
- **Tiempo estimado:** 1-2 horas
- **Subtemas:** 10
- **Ejercicios prácticos:** 5
- **Proyecto mini:** 1

## **📋 Índice de Contenido**

### **📚 1. Contenido Teórico**
- Conceptos fundamentales
- Sintaxis y uso
- Casos de uso comunes
- Mejores prácticas
- Errores comunes a evitar

### **💻 2. Ejemplos Prácticos**
- Ejemplo básico
- Ejemplo intermedio
- Ejemplo avanzado
- Casos de uso reales
- Optimizaciones

### **🧪 3. Ejercicios**
- Ejercicio 1: Básico
- Ejercicio 2: Intermedio
- Ejercicio 3: Avanzado
- Ejercicio 4: Proyecto mini
- Ejercicio 5: Desafío

### **📝 4. Evaluación**
- Quiz de conocimientos
- Ejercicio práctico
- Revisión de código
- Autoevaluación

---

## **🛠️ Herramientas y Recursos**

### **Herramientas Recomendadas**
- VS Code con extensiones
- Chrome DevTools
- Node.js REPL
- Online playgrounds

### **Recursos Adicionales**
- Documentación oficial
- Artículos recomendados
- Videos complementarios
- Ejercicios extra

---

## **➡️ Navegación**

⬅️ **Anterior:** [${numeroCapitulo}.${numeroTema - 1}](../${numeroCapitulo}.${numeroTema - 1}.%20*/README.md)  
➡️ **Siguiente:** [${numeroCapitulo}.${numeroTema + 1}](../${numeroCapitulo}.${numeroTema + 1}.%20*/README.md)  
🏠 **Capítulo:** [Volver al capítulo](../README.md)

---

**¡Continúa aprendiendo y dominando JavaScript!** 🚀`;

  crearArchivo(path.join(rutaTema, 'README.md'), contenidoREADME);

  // Crear estructura de carpetas del tema
  const carpetas = ['contenido', 'ejemplos', 'ejercicios', 'recursos', 'evaluacion'];
  
  carpetas.forEach(carpeta => {
    const rutaCarpeta = path.join(rutaTema, carpeta);
    crearDirectorio(rutaCarpeta);
    
    // Crear archivos específicos por carpeta
    switch(carpeta) {
      case 'contenido':
        crearArchivo(path.join(rutaCarpeta, 'teoria.md'), `# Teoría - ${nombreTema}\n\n## Conceptos Fundamentales\n\n[Contenido teórico aquí]\n\n## Ejemplos\n\n[Ejemplos aquí]\n\n## Mejores Prácticas\n\n[Mejores prácticas aquí]`);
        crearArchivo(path.join(rutaCarpeta, 'sintaxis.md'), `# Sintaxis - ${nombreTema}\n\n## Sintaxis Básica\n\n\`\`\`javascript\n// Ejemplo de sintaxis\nconsole.log('${nombreTema}');\n\`\`\``);
        break;
        
      case 'ejemplos':
        crearArchivo(path.join(rutaCarpeta, 'ejemplo-basico.js'), `/**\n * EJEMPLO BÁSICO - ${nombreTema}\n * ${'-'.repeat(50)}\n */\n\n// Ejemplo básico de ${nombreTema}\nconsole.log('Ejemplo básico de ${nombreTema}');\n\n// TODO: Implementar ejemplo`);
        crearArchivo(path.join(rutaCarpeta, 'ejemplo-intermedio.js'), `/**\n * EJEMPLO INTERMEDIO - ${nombreTema}\n * ${'-'.repeat(50)}\n */\n\n// Ejemplo intermedio de ${nombreTema}\nconsole.log('Ejemplo intermedio de ${nombreTema}');\n\n// TODO: Implementar ejemplo`);
        crearArchivo(path.join(rutaCarpeta, 'ejemplo-avanzado.js'), `/**\n * EJEMPLO AVANZADO - ${nombreTema}\n * ${'-'.repeat(50)}\n */\n\n// Ejemplo avanzado de ${nombreTema}\nconsole.log('Ejemplo avanzado de ${nombreTema}');\n\n// TODO: Implementar ejemplo`);
        break;
        
      case 'ejercicios':
        for(let i = 1; i <= 5; i++) {
          crearArchivo(path.join(rutaCarpeta, `ejercicio-${i}.js`), `/**\n * EJERCICIO ${i} - ${nombreTema}\n * ${'-'.repeat(50)}\n * \n * Dificultad: ${i <= 2 ? 'Básico' : i <= 4 ? 'Intermedio' : 'Avanzado'}\n * Tiempo estimado: ${i * 10} minutos\n */\n\n// Instrucciones:\n// TODO: Escribir instrucciones del ejercicio\n\n// Tu código aquí:\n\n\n// Tests (no modificar)\nfunction test() {\n  // TODO: Implementar tests\n  console.log('Tests pendientes');\n}\n\ntest();`);
        }
        break;
        
      case 'recursos':
        crearArchivo(path.join(rutaCarpeta, 'enlaces.md'), `# Enlaces y Recursos - ${nombreTema}\n\n## Documentación Oficial\n- [MDN Web Docs](https://developer.mozilla.org/)\n\n## Artículos Recomendados\n- [Artículo 1](enlace)\n- [Artículo 2](enlace)\n\n## Videos\n- [Video 1](enlace)\n- [Video 2](enlace)\n\n## Herramientas\n- [Herramienta 1](enlace)\n- [Herramienta 2](enlace)`);
        crearDirectorio(path.join(rutaCarpeta, 'imagenes'));
        crearDirectorio(path.join(rutaCarpeta, 'diagramas'));
        break;
        
      case 'evaluacion':
        crearArchivo(path.join(rutaCarpeta, 'quiz.md'), `# Quiz - ${nombreTema}\n\n## Pregunta 1\n**¿Cuál es...?**\na) Opción A\nb) Opción B\nc) Opción C\nd) Opción D\n\n*Respuesta correcta: a*\n\n## Pregunta 2\n**¿Cómo se...?**\na) Opción A\nb) Opción B\nc) Opción C\nd) Opción D\n\n*Respuesta correcta: b*\n\n## Pregunta 3\n**¿Por qué...?**\na) Opción A\nb) Opción B\nc) Opción C\nd) Opción D\n\n*Respuesta correcta: c*`);
        crearArchivo(path.join(rutaCarpeta, 'proyecto.md'), `# Proyecto Práctico - ${nombreTema}\n\n## Objetivo\nCrear un proyecto que demuestre el dominio de ${nombreTema}.\n\n## Requisitos\n1. Requisito 1\n2. Requisito 2\n3. Requisito 3\n\n## Entregables\n- [ ] Código fuente\n- [ ] Documentación\n- [ ] Tests\n- [ ] Demo\n\n## Criterios de Evaluación\n- Funcionalidad (40%)\n- Calidad del código (30%)\n- Documentación (20%)\n- Creatividad (10%)`);
        break;
    }
  });
}

// Función principal
function generarEstructuraCompleta() {
  console.log('🚀 Generando estructura completa del curso...\n');
  
  Object.entries(estructuraCompleta).forEach(([nombreParte, dataParte]) => {
    console.log(`📚 Procesando: ${nombreParte}`);
    
    // Crear directorio de la parte
    crearDirectorio(nombreParte);
    
    // Procesar cada capítulo
    dataParte.capitulos.forEach(capitulo => {
      const nombreCapitulo = `${capitulo.num} - ${capitulo.nombre}`;
      const rutaCapitulo = path.join(nombreParte, nombreCapitulo);
      
      console.log(`  📖 Procesando: ${nombreCapitulo}`);
      
      // Crear directorio del capítulo
      crearDirectorio(rutaCapitulo);
      
      // Crear README del capítulo
      crearREADMECapitulo(rutaCapitulo, capitulo.num, capitulo.nombre, capitulo.temas);
      
      // Procesar cada tema
      capitulo.temas.forEach((tema, index) => {
        const numeroTema = index + 1;
        const nombreTema = `${capitulo.num}.${numeroTema}. ${tema}`;
        const rutaTema = path.join(rutaCapitulo, nombreTema);
        
        console.log(`    📝 Procesando: ${nombreTema}`);
        
        // Crear directorio del tema
        crearDirectorio(rutaTema);
        
        // Crear estructura del tema
        crearEstructuraTema(rutaTema, capitulo.num, numeroTema, tema);
      });
    });
    
    console.log(`✅ Completado: ${nombreParte}\n`);
  });
  
  console.log('🎉 ¡Estructura completa generada exitosamente!');
  
  // Estadísticas finales
  const totalCapitulos = Object.values(estructuraCompleta).reduce((acc, parte) => acc + parte.capitulos.length, 0);
  const totalTemas = Object.values(estructuraCompleta).reduce((acc, parte) => 
    acc + parte.capitulos.reduce((acc2, cap) => acc2 + cap.temas.length, 0), 0);
  
  console.log('\n📊 Estadísticas generadas:');
  console.log(`- Partes: ${Object.keys(estructuraCompleta).length}`);
  console.log(`- Capítulos: ${totalCapitulos}`);
  console.log(`- Temas: ${totalTemas}`);
  console.log(`- Subtemas estimados: ${totalTemas * 10}`);
  console.log(`- Ejercicios estimados: ${totalTemas * 5}`);
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  generarEstructuraCompleta();
}

module.exports = { generarEstructuraCompleta, estructuraCompleta };
