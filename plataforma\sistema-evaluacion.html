<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Evaluación JavaScript - Maestría</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #fff;
            min-height: 100vh;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #f7df1e, #ffeb3b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .quiz-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .quiz-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }

        .quiz-card:hover {
            transform: translateY(-5px);
            border-color: #f7df1e;
            box-shadow: 0 10px 30px rgba(247, 223, 30, 0.2);
        }

        .quiz-title {
            font-size: 1.3rem;
            color: #f7df1e;
            margin-bottom: 0.5rem;
        }

        .quiz-info {
            color: #ccc;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .quiz-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .quiz-difficulty {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .difficulty-beginner {
            background: rgba(86, 211, 100, 0.2);
            color: #56d364;
        }

        .difficulty-intermediate {
            background: rgba(247, 223, 30, 0.2);
            color: #f7df1e;
        }

        .difficulty-advanced {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
        }

        .quiz-container {
            display: none;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 2rem;
        }

        .quiz-container.active {
            display: block;
        }

        .quiz-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .quiz-progress {
            flex: 1;
            margin: 0 2rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #f7df1e, #ffeb3b);
            transition: width 0.3s;
            width: 0%;
        }

        .progress-text {
            text-align: center;
            margin-top: 0.5rem;
            color: #ccc;
            font-size: 0.9rem;
        }

        .timer {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 600;
            font-family: 'Courier New', monospace;
        }

        .question-container {
            margin-bottom: 2rem;
        }

        .question-number {
            color: #f7df1e;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .question-text {
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .question-code {
            background: #1e1e1e;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }

        .options-container {
            display: grid;
            gap: 1rem;
        }

        .option {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .option:hover {
            border-color: #f7df1e;
            background: rgba(247, 223, 30, 0.1);
        }

        .option.selected {
            border-color: #f7df1e;
            background: rgba(247, 223, 30, 0.2);
        }

        .option.correct {
            border-color: #56d364;
            background: rgba(86, 211, 100, 0.2);
        }

        .option.incorrect {
            border-color: #ff6b6b;
            background: rgba(255, 107, 107, 0.2);
        }

        .option-letter {
            width: 30px;
            height: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            flex-shrink: 0;
        }

        .option.selected .option-letter {
            background: #f7df1e;
            color: #000;
        }

        .option.correct .option-letter {
            background: #56d364;
            color: #000;
        }

        .option.incorrect .option-letter {
            background: #ff6b6b;
            color: #fff;
        }

        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .btn {
            background: #f7df1e;
            color: #000;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn:hover {
            background: #ffeb3b;
            transform: translateY(-1px);
        }

        .btn:disabled {
            background: rgba(255, 255, 255, 0.1);
            color: #666;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: transparent;
            color: #f7df1e;
            border: 2px solid #f7df1e;
        }

        .btn-secondary:hover {
            background: #f7df1e;
            color: #000;
        }

        .results-container {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .results-container.active {
            display: block;
        }

        .score-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            position: relative;
        }

        .score-excellent {
            background: conic-gradient(#56d364 0deg 360deg, rgba(86, 211, 100, 0.2) 360deg);
            color: #56d364;
        }

        .score-good {
            background: conic-gradient(#f7df1e 0deg 288deg, rgba(247, 223, 30, 0.2) 288deg);
            color: #f7df1e;
        }

        .score-needs-improvement {
            background: conic-gradient(#ff6b6b 0deg 216deg, rgba(255, 107, 107, 0.2) 216deg);
            color: #ff6b6b;
        }

        .results-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .result-stat {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 1rem;
        }

        .result-stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #f7df1e;
        }

        .result-stat-label {
            color: #ccc;
            font-size: 0.9rem;
        }

        .explanation {
            background: rgba(255, 255, 255, 0.05);
            border-left: 4px solid #f7df1e;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }

        .explanation h4 {
            color: #f7df1e;
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .quiz-header {
                flex-direction: column;
                gap: 1rem;
            }

            .quiz-progress {
                margin: 0;
                width: 100%;
            }

            .controls {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📝 Sistema de Evaluación</h1>
            <p>Evalúa tus conocimientos de JavaScript con quizzes interactivos</p>
        </div>

        <!-- Quiz Selector -->
        <div class="quiz-selector" id="quizSelector">
            <div class="quiz-card" data-quiz="fundamentals">
                <div class="quiz-title">Fundamentos de JavaScript</div>
                <div class="quiz-info">Variables, tipos de datos, operadores y control de flujo</div>
                <div class="quiz-stats">
                    <span>10 preguntas • 15 min</span>
                    <div class="quiz-difficulty difficulty-beginner">Principiante</div>
                </div>
            </div>

            <div class="quiz-card" data-quiz="functions">
                <div class="quiz-title">Funciones Avanzadas</div>
                <div class="quiz-info">Closures, arrow functions, callbacks y programación funcional</div>
                <div class="quiz-stats">
                    <span>12 preguntas • 20 min</span>
                    <div class="quiz-difficulty difficulty-intermediate">Intermedio</div>
                </div>
            </div>

            <div class="quiz-card" data-quiz="async">
                <div class="quiz-title">Programación Asíncrona</div>
                <div class="quiz-info">Promises, async/await, event loop y manejo de errores</div>
                <div class="quiz-stats">
                    <span>15 preguntas • 25 min</span>
                    <div class="quiz-difficulty difficulty-advanced">Avanzado</div>
                </div>
            </div>

            <div class="quiz-card" data-quiz="oop">
                <div class="quiz-title">Programación Orientada a Objetos</div>
                <div class="quiz-info">Clases, herencia, prototipos y patrones de diseño</div>
                <div class="quiz-stats">
                    <span>12 preguntas • 20 min</span>
                    <div class="quiz-difficulty difficulty-intermediate">Intermedio</div>
                </div>
            </div>
        </div>

        <!-- Quiz Container -->
        <div class="quiz-container" id="quizContainer">
            <div class="quiz-header">
                <button class="btn-secondary" id="backBtn">← Volver</button>
                <div class="quiz-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Pregunta 1 de 10</div>
                </div>
                <div class="timer" id="timer">15:00</div>
            </div>

            <div class="question-container" id="questionContainer">
                <!-- Questions will be dynamically inserted here -->
            </div>

            <div class="controls">
                <button class="btn-secondary" id="prevBtn" disabled>← Anterior</button>
                <div>
                    <button class="btn" id="nextBtn" disabled>Siguiente →</button>
                    <button class="btn" id="submitBtn" style="display: none;">Finalizar Quiz</button>
                </div>
            </div>
        </div>

        <!-- Results Container -->
        <div class="results-container" id="resultsContainer">
            <h2 style="color: #f7df1e; margin-bottom: 2rem;">¡Quiz Completado!</h2>
            
            <div class="score-circle" id="scoreCircle">
                <span id="scoreText">85%</span>
            </div>

            <div class="results-details">
                <div class="result-stat">
                    <div class="result-stat-value" id="correctAnswers">8</div>
                    <div class="result-stat-label">Respuestas Correctas</div>
                </div>
                <div class="result-stat">
                    <div class="result-stat-value" id="totalQuestions">10</div>
                    <div class="result-stat-label">Total de Preguntas</div>
                </div>
                <div class="result-stat">
                    <div class="result-stat-value" id="timeSpent">12:34</div>
                    <div class="result-stat-label">Tiempo Utilizado</div>
                </div>
                <div class="result-stat">
                    <div class="result-stat-value" id="finalScore">85%</div>
                    <div class="result-stat-label">Puntuación Final</div>
                </div>
            </div>

            <div id="feedback" style="margin: 2rem 0;">
                <!-- Feedback will be inserted here -->
            </div>

            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                <button class="btn" id="retryBtn">🔄 Intentar de Nuevo</button>
                <button class="btn-secondary" id="reviewBtn">📖 Revisar Respuestas</button>
                <button class="btn-secondary" id="newQuizBtn">📝 Otro Quiz</button>
            </div>
        </div>
    </div>

    <script>
        class QuizSystem {
            constructor() {
                this.currentQuiz = null;
                this.currentQuestion = 0;
                this.answers = [];
                this.startTime = null;
                this.timeLimit = 0;
                this.timerInterval = null;
                
                this.quizzes = {
                    fundamentals: {
                        title: "Fundamentos de JavaScript",
                        timeLimit: 15 * 60, // 15 minutes
                        questions: [
                            {
                                question: "¿Cuál es la diferencia principal entre 'let' y 'var'?",
                                code: `let x = 1;
var y = 2;
if (true) {
    let x = 3;
    var y = 4;
}
console.log(x, y);`,
                                options: [
                                    "let tiene function scope, var tiene block scope",
                                    "let tiene block scope, var tiene function scope",
                                    "No hay diferencia",
                                    "let no puede ser redeclarado, var sí"
                                ],
                                correct: 1,
                                explanation: "'let' tiene block scope (limitado al bloque donde se declara), mientras que 'var' tiene function scope (visible en toda la función)."
                            },
                            {
                                question: "¿Qué imprime el siguiente código?",
                                code: `console.log(typeof null);
console.log(typeof undefined);
console.log(typeof []);`,
                                options: [
                                    "null, undefined, array",
                                    "object, undefined, object",
                                    "null, undefined, object",
                                    "object, undefined, array"
                                ],
                                correct: 1,
                                explanation: "En JavaScript, 'typeof null' retorna 'object' (un bug histórico), 'typeof undefined' retorna 'undefined', y 'typeof []' retorna 'object' porque los arrays son objetos."
                            }
                            // Más preguntas se añadirían aquí
                        ]
                    },
                    functions: {
                        title: "Funciones Avanzadas",
                        timeLimit: 20 * 60,
                        questions: [
                            {
                                question: "¿Qué es un closure en JavaScript?",
                                code: `function outer(x) {
    return function inner(y) {
        return x + y;
    };
}
const add5 = outer(5);
console.log(add5(3));`,
                                options: [
                                    "Una función que se ejecuta inmediatamente",
                                    "Una función que tiene acceso a variables de su scope externo",
                                    "Una función que no retorna nada",
                                    "Una función que se llama a sí misma"
                                ],
                                correct: 1,
                                explanation: "Un closure es una función que tiene acceso a variables de su scope externo incluso después de que la función externa haya terminado de ejecutarse."
                            }
                        ]
                    }
                };
                
                this.init();
            }

            init() {
                this.setupEventListeners();
            }

            setupEventListeners() {
                // Quiz selection
                document.querySelectorAll('.quiz-card').forEach(card => {
                    card.addEventListener('click', () => {
                        const quizId = card.dataset.quiz;
                        this.startQuiz(quizId);
                    });
                });

                // Navigation buttons
                document.getElementById('backBtn').addEventListener('click', () => {
                    this.showQuizSelector();
                });

                document.getElementById('prevBtn').addEventListener('click', () => {
                    this.previousQuestion();
                });

                document.getElementById('nextBtn').addEventListener('click', () => {
                    this.nextQuestion();
                });

                document.getElementById('submitBtn').addEventListener('click', () => {
                    this.submitQuiz();
                });

                // Results buttons
                document.getElementById('retryBtn').addEventListener('click', () => {
                    this.retryQuiz();
                });

                document.getElementById('newQuizBtn').addEventListener('click', () => {
                    this.showQuizSelector();
                });

                document.getElementById('reviewBtn').addEventListener('click', () => {
                    this.reviewAnswers();
                });
            }

            startQuiz(quizId) {
                if (!this.quizzes[quizId]) {
                    alert('Quiz no disponible aún. ¡Próximamente!');
                    return;
                }

                this.currentQuiz = this.quizzes[quizId];
                this.currentQuestion = 0;
                this.answers = new Array(this.currentQuiz.questions.length).fill(null);
                this.startTime = Date.now();
                this.timeLimit = this.currentQuiz.timeLimit;

                this.showQuizContainer();
                this.startTimer();
                this.displayQuestion();
                this.updateProgress();
            }

            showQuizSelector() {
                document.getElementById('quizSelector').style.display = 'grid';
                document.getElementById('quizContainer').classList.remove('active');
                document.getElementById('resultsContainer').classList.remove('active');
                this.stopTimer();
            }

            showQuizContainer() {
                document.getElementById('quizSelector').style.display = 'none';
                document.getElementById('quizContainer').classList.add('active');
                document.getElementById('resultsContainer').classList.remove('active');
            }

            showResultsContainer() {
                document.getElementById('quizSelector').style.display = 'none';
                document.getElementById('quizContainer').classList.remove('active');
                document.getElementById('resultsContainer').classList.add('active');
            }

            displayQuestion() {
                const question = this.currentQuiz.questions[this.currentQuestion];
                const container = document.getElementById('questionContainer');

                container.innerHTML = `
                    <div class="question-number">
                        Pregunta ${this.currentQuestion + 1} de ${this.currentQuiz.questions.length}
                    </div>
                    <div class="question-text">${question.question}</div>
                    ${question.code ? `<div class="question-code">${question.code}</div>` : ''}
                    <div class="options-container">
                        ${question.options.map((option, index) => `
                            <div class="option" data-option="${index}">
                                <div class="option-letter">${String.fromCharCode(65 + index)}</div>
                                <div class="option-text">${option}</div>
                            </div>
                        `).join('')}
                    </div>
                `;

                // Add event listeners to options
                container.querySelectorAll('.option').forEach(option => {
                    option.addEventListener('click', () => {
                        this.selectOption(parseInt(option.dataset.option));
                    });
                });

                // Restore previous selection
                if (this.answers[this.currentQuestion] !== null) {
                    this.selectOption(this.answers[this.currentQuestion]);
                }

                this.updateNavigationButtons();
            }

            selectOption(optionIndex) {
                // Remove previous selection
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('selected');
                });

                // Add selection to clicked option
                document.querySelector(`[data-option="${optionIndex}"]`).classList.add('selected');

                // Store answer
                this.answers[this.currentQuestion] = optionIndex;

                // Enable next button
                document.getElementById('nextBtn').disabled = false;
            }

            nextQuestion() {
                if (this.currentQuestion < this.currentQuiz.questions.length - 1) {
                    this.currentQuestion++;
                    this.displayQuestion();
                    this.updateProgress();
                }
            }

            previousQuestion() {
                if (this.currentQuestion > 0) {
                    this.currentQuestion--;
                    this.displayQuestion();
                    this.updateProgress();
                }
            }

            updateProgress() {
                const progress = ((this.currentQuestion + 1) / this.currentQuiz.questions.length) * 100;
                document.getElementById('progressFill').style.width = `${progress}%`;
                document.getElementById('progressText').textContent = 
                    `Pregunta ${this.currentQuestion + 1} de ${this.currentQuiz.questions.length}`;
            }

            updateNavigationButtons() {
                const prevBtn = document.getElementById('prevBtn');
                const nextBtn = document.getElementById('nextBtn');
                const submitBtn = document.getElementById('submitBtn');

                prevBtn.disabled = this.currentQuestion === 0;
                
                if (this.currentQuestion === this.currentQuiz.questions.length - 1) {
                    nextBtn.style.display = 'none';
                    submitBtn.style.display = 'inline-block';
                } else {
                    nextBtn.style.display = 'inline-block';
                    submitBtn.style.display = 'none';
                }

                nextBtn.disabled = this.answers[this.currentQuestion] === null;
            }

            startTimer() {
                let timeLeft = this.timeLimit;
                
                this.timerInterval = setInterval(() => {
                    timeLeft--;
                    
                    const minutes = Math.floor(timeLeft / 60);
                    const seconds = timeLeft % 60;
                    
                    document.getElementById('timer').textContent = 
                        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    
                    if (timeLeft <= 0) {
                        this.submitQuiz();
                    }
                }, 1000);
            }

            stopTimer() {
                if (this.timerInterval) {
                    clearInterval(this.timerInterval);
                    this.timerInterval = null;
                }
            }

            submitQuiz() {
                this.stopTimer();
                this.calculateResults();
                this.showResultsContainer();
            }

            calculateResults() {
                const correctAnswers = this.answers.reduce((count, answer, index) => {
                    return count + (answer === this.currentQuiz.questions[index].correct ? 1 : 0);
                }, 0);

                const totalQuestions = this.currentQuiz.questions.length;
                const score = Math.round((correctAnswers / totalQuestions) * 100);
                const timeSpent = Math.floor((Date.now() - this.startTime) / 1000);

                // Update results display
                document.getElementById('correctAnswers').textContent = correctAnswers;
                document.getElementById('totalQuestions').textContent = totalQuestions;
                document.getElementById('finalScore').textContent = `${score}%`;
                document.getElementById('scoreText').textContent = `${score}%`;
                
                const minutes = Math.floor(timeSpent / 60);
                const seconds = timeSpent % 60;
                document.getElementById('timeSpent').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                // Update score circle
                const scoreCircle = document.getElementById('scoreCircle');
                scoreCircle.className = 'score-circle';
                
                if (score >= 80) {
                    scoreCircle.classList.add('score-excellent');
                } else if (score >= 60) {
                    scoreCircle.classList.add('score-good');
                } else {
                    scoreCircle.classList.add('score-needs-improvement');
                }

                // Generate feedback
                this.generateFeedback(score, correctAnswers, totalQuestions);
            }

            generateFeedback(score, correct, total) {
                const feedback = document.getElementById('feedback');
                let message, recommendations;

                if (score >= 80) {
                    message = "¡Excelente trabajo! Tienes un dominio sólido de este tema.";
                    recommendations = "Continúa con el siguiente nivel o explora temas más avanzados.";
                } else if (score >= 60) {
                    message = "Buen trabajo, pero hay espacio para mejorar.";
                    recommendations = "Revisa los temas donde tuviste dificultades y practica más.";
                } else {
                    message = "Necesitas repasar más este tema antes de continuar.";
                    recommendations = "Te recomendamos revisar el material teórico y hacer más ejercicios.";
                }

                feedback.innerHTML = `
                    <div class="explanation">
                        <h4>Retroalimentación</h4>
                        <p>${message}</p>
                        <p><strong>Recomendación:</strong> ${recommendations}</p>
                    </div>
                `;
            }

            retryQuiz() {
                this.startQuiz(Object.keys(this.quizzes).find(key => this.quizzes[key] === this.currentQuiz));
            }

            reviewAnswers() {
                // TODO: Implement answer review functionality
                alert('Funcionalidad de revisión en desarrollo');
            }
        }

        // Initialize the quiz system
        document.addEventListener('DOMContentLoaded', () => {
            new QuizSystem();
        });
    </script>
</body>
</html>
