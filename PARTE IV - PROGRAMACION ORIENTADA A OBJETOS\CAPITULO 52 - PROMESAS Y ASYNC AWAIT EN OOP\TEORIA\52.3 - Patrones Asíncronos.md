# **52.3 - PATRONES DE DISEÑO ASÍNCRONOS**

## **📖 INTRODUCCIÓN**

Los patrones de diseño asíncronos representan la evolución natural de los patrones clásicos de GoF adaptados al mundo de la programación asíncrona moderna. Estos patrones no son simplemente versiones "async" de patrones existentes, sino arquitecturas completamente nuevas que emergen de las necesidades únicas de sistemas distribuidos, aplicaciones en tiempo real y arquitecturas reactivas. En el contexto de JavaScript y la programación orientada a objetos, dominar estos patrones es esencial para construir aplicaciones que no solo funcionen correctamente bajo carga variable, sino que también mantengan la responsividad, escalabilidad y mantenibilidad que demandan las aplicaciones empresariales modernas. Estos patrones abordan desafíos complejos como la coordinación de múltiples operaciones asíncronas, el manejo de estado distribuido, la implementación de circuit breakers para resilencia, y la orquestación de workflows complejos que pueden involucrar servicios externos, bases de datos y sistemas de mensajería. La maestría en patrones asíncronos te permitirá diseñar arquitecturas que se adapten elegantemente a fallos, escalen horizontalmente sin perder coherencia, y proporcionen experiencias de usuario fluidas incluso cuando los sistemas subyacentes enfrentan latencias variables o fallos temporales.

## **🔍 CONCEPTOS FUNDAMENTALES**

### **Patrón Async Observer**

![Patrón Async Observer](../VISUALIZACIONES/patrones/async-observer-pattern.svg)

```javascript
class AsyncObserver {
    constructor() {
        // Map para organizar handlers por evento (O(1) lookup)
        this.observers = new Map();
        // Cola para procesamiento diferido de eventos
        this.eventQueue = [];
        // Flag para evitar procesamiento concurrente
        this.isProcessing = false;
        // Control de concurrencia máxima
        this.maxConcurrentObservers = 5;
    }
    
    /**
     * Suscribe un observer asíncrono a un evento
     * @param {string} event - Nombre del evento
     * @param {Function} asyncHandler - Handler asíncrono
     * @param {Object} options - Opciones del observer
     */
    async subscribe(event, asyncHandler, options = {}) {
        // Lazy initialization del array de observers por evento
        if (!this.observers.has(event)) {
            this.observers.set(event, []);
        }
        
        // Configuración del observer con metadatos para control avanzado
        const observer = {
            id: this.generateObserverId(),
            handler: asyncHandler,
            priority: options.priority || 0,        // Prioridad de ejecución
            timeout: options.timeout || 30000,     // Timeout individual
            retries: options.retries || 0,         // Reintentos automáticos
            filter: options.filter || (() => true) // Filtro de eventos
        };
        
        // Agregar observer al array correspondiente al evento
        this.observers.get(event).push(observer);
        
        // Ordenar por prioridad (mayor prioridad primero)
        this.observers.get(event).sort((a, b) => b.priority - a.priority);
        
        // Retornar ID único para posterior desuscripción
        return observer.id;
    }
    
    /**
     * Emite un evento de forma asíncrona
     * @param {string} event - Nombre del evento
     * @param {*} data - Datos del evento
     */
    async emit(event, data) {
        // Obtener observers registrados para el evento específico
        const observers = this.observers.get(event) || [];
        
        // Filtrar observers según sus criterios
        const filteredObservers = observers.filter(obs => obs.filter(data));
        
        // Promise.allSettled ejecuta todos los observers en paralelo
        const results = await Promise.allSettled(
            filteredObservers.map(observer => this.executeObserver(observer, data))
        );
        
        // Retornar resultados incluyendo éxitos y fallos
        return results;
    }
}
```

### **🔍 EXPLICACIÓN EXHAUSTIVA DEL CÓDIGO**

**Líneas 3-11: Constructor y Estado**
- `observers`: Map que organiza handlers por evento para O(1) lookup
- `eventQueue`: Cola para procesamiento diferido de eventos
- `isProcessing`: Flag para evitar procesamiento concurrente
- `maxConcurrentObservers`: Límite de observers ejecutándose simultáneamente

**Líneas 19-37: Método subscribe**
- **Línea 21**: Lazy initialization del array de observers por evento
- **Líneas 25-32**: Configuración del observer con metadatos para control avanzado
- **Línea 35**: Agregar observer al array correspondiente al evento
- **Línea 38**: Ordenar por prioridad para ejecución controlada
- **Línea 41**: Retornar ID único para posterior desuscripción

**Líneas 49-62: Método emit**
- **Línea 51**: Obtener observers registrados para el evento específico
- **Línea 54**: Filtrar observers según sus criterios personalizados
- **Líneas 57-59**: `Promise.allSettled` ejecuta todos los observers en paralelo
- **Línea 62**: Retornar resultados incluyendo éxitos y fallos

### **💻 EMULACIÓN DE CONSOLA**

```bash
# Crear instancia y suscribir observers
const observer = new AsyncObserver();

# Suscribir handler para evento 'userCreated'
const id1 = await observer.subscribe('userCreated', async (userData) => {
    console.log('📧 Enviando email de bienvenida...');
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log('✅ Email enviado');
}, { priority: 1 });

const id2 = await observer.subscribe('userCreated', async (userData) => {
    console.log('👤 Creando perfil de usuario...');
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log('✅ Perfil creado');
}, { priority: 2 });

# Emitir evento
const results = await observer.emit('userCreated', { id: 1, name: 'Juan' });

# Salida de consola (orden por prioridad):
👤 Creando perfil de usuario...
📧 Enviando email de bienvenida...
✅ Perfil creado
✅ Email enviado

# Resultados
console.log(results);
[
  { status: 'fulfilled', value: undefined },
  { status: 'fulfilled', value: undefined }
]
```

## **⚙️ PATRONES AVANZADOS**

### **1. Patrón Circuit Breaker**

```javascript
class AsyncCircuitBreaker {
    constructor(options = {}) {
        this.failureThreshold = options.failureThreshold || 5;
        this.recoveryTimeout = options.recoveryTimeout || 60000;
        this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
        this.failureCount = 0;
        this.lastFailureTime = null;
    }
    
    /**
     * Ejecuta una operación a través del circuit breaker
     * @param {Function} operation - Operación asíncrona a ejecutar
     * @returns {Promise} Resultado de la operación
     */
    async execute(operation) {
        // Verificar estado del circuit
        if (this.state === 'OPEN') {
            if (Date.now() - this.lastFailureTime < this.recoveryTimeout) {
                throw new Error('Circuit breaker is OPEN');
            } else {
                this.state = 'HALF_OPEN';
            }
        }
        
        try {
            const result = await operation();
            this.onSuccess();
            return result;
        } catch (error) {
            this.onFailure();
            throw error;
        }
    }
}
```

### **2. Patrón Saga**

```javascript
class AsyncSaga {
    constructor() {
        this.steps = [];
        this.compensations = [];
        this.executedSteps = [];
    }
    
    /**
     * Añade un paso a la saga
     * @param {Function} action - Acción a ejecutar
     * @param {Function} compensation - Compensación en caso de fallo
     */
    addStep(action, compensation) {
        this.steps.push({ action, compensation });
        return this;
    }
    
    /**
     * Ejecuta la saga completa
     * @returns {Promise} Resultado de la saga
     */
    async execute() {
        try {
            // Ejecutar todos los pasos
            for (let i = 0; i < this.steps.length; i++) {
                const result = await this.steps[i].action();
                this.executedSteps.push({ index: i, result });
            }
            
            return { status: 'COMPLETED', steps: this.executedSteps };
        } catch (error) {
            // Ejecutar compensaciones en orden inverso
            await this.compensate();
            throw { status: 'FAILED', error, compensated: true };
        }
    }
}
```

## **🎯 CASOS DE USO PRÁCTICOS**

### **Caso 1: E-commerce con Microservicios**
Sistema de pedidos que coordina inventario, pagos y envíos usando Saga Pattern para garantizar consistencia distribuida.

### **Caso 2: Sistema de Notificaciones**
Observer Pattern asíncrono para manejar eventos de usuario y enviar notificaciones a múltiples canales (email, SMS, push).

### **Caso 3: API Gateway Resiliente**
Circuit Breaker para proteger servicios backend de cascadas de fallos y proporcionar degradación graceful.

## **⚠️ ERRORES COMUNES**

### **Error 1: No Manejar Fallos Parciales en Observer**
```javascript
// ❌ INCORRECTO - Un fallo detiene todo
async emit(event, data) {
    const observers = this.observers.get(event) || [];
    for (const observer of observers) {
        await observer.handler(data); // Si falla uno, se detiene
    }
}

// ✅ CORRECTO - Fallos aislados
async emit(event, data) {
    const observers = this.observers.get(event) || [];
    return await Promise.allSettled(
        observers.map(obs => obs.handler(data))
    );
}
```

### **Error 2: Circuit Breaker sin Estado Persistente**
```javascript
// ❌ INCORRECTO - Estado se pierde al reiniciar
class BadCircuitBreaker {
    constructor() {
        this.state = 'CLOSED'; // Se pierde al reiniciar
    }
}

// ✅ CORRECTO - Estado persistente
class GoodCircuitBreaker {
    constructor(storage) {
        this.storage = storage;
        this.state = storage.get('circuitState') || 'CLOSED';
    }
}
```

### **Error 3: Saga sin Compensación Idempotente**
```javascript
// ❌ INCORRECTO - Compensación no idempotente
async compensatePayment(paymentId) {
    await refundPayment(paymentId); // Puede fallar si ya se refundó
}

// ✅ CORRECTO - Compensación idempotente
async compensatePayment(paymentId) {
    const payment = await getPayment(paymentId);
    if (payment.status === 'CHARGED') {
        await refundPayment(paymentId);
    }
}
```

## **💡 MEJORES PRÁCTICAS**

### **1. Diseñar para Fallos**
Asume que las operaciones asíncronas pueden fallar y diseña compensaciones apropiadas.

### **2. Implementar Timeouts**
Toda operación asíncrona debe tener un timeout para evitar cuelgues indefinidos.

### **3. Usar Observabilidad**
Implementa métricas y logging para monitorear el comportamiento de los patrones.

### **4. Manejar Backpressure**
Controla la carga de trabajo para evitar saturar recursos del sistema.

### **5. Testear Escenarios de Fallo**
Usa chaos engineering para validar que los patrones funcionan bajo condiciones adversas.

## **🤔 PREGUNTAS PARA REFLEXIÓN**

- ¿Cómo combinarías múltiples patrones asíncronos para crear una arquitectura resiliente?
- ¿Qué estrategias implementarías para manejar la ordenación de eventos en un sistema distribuido?
- ¿Cómo optimizarías el performance de observers cuando hay miles de suscriptores?
- ¿De qué manera integrarías estos patrones con sistemas de message queuing como RabbitMQ?

## **📚 RECURSOS ADICIONALES**

- **Implementación Completa:** Ver `CODIGO/patrones/AsyncObserver.js`
- **Ejemplos Prácticos:** Ver `EJEMPLOS/avanzados/async-patterns.js`
- **Tests:** Ver `TESTS/unit/async-patterns.test.js`
- **Visualizaciones:** Ver `VISUALIZACIONES/patrones/async-observer-pattern.svg`

---

**Próximo:** [52.4 - Manejo de Errores Asíncronos](52.4%20-%20Manejo%20de%20Errores.md)
