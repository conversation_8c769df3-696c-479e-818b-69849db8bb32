# Script para crear estructura de carpetas para las partes restantes
# PARTE VI - DOM Y EVENTOS
mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 60 - INTRODUCCION AL DOM/TEORIA"
mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 60 - INTRODUCCION AL DOM/CODIGO"
mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 60 - INTRODUCCION AL DOM/EJEMPLOS"
mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 60 - INTRODUCCION AL DOM/VISUALIZACIONES"

mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 61 - SELECCION Y MANIPULACION DE ELEMENTOS/TEORIA"
mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 61 - SELECCION Y MANIPULACION DE ELEMENTOS/CODIGO"
mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 61 - SELECCION Y MANIPULACION DE ELEMENTOS/EJEMPLOS"
mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 61 - SELECCION Y MANIPULACION DE ELEMENTOS/VISUALIZACIONES"

mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 62 - EVENTOS Y EVENT LISTENERS/TEORIA"
mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 62 - EVENTOS Y EVENT LISTENERS/CODIGO"
mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 62 - EVENTOS Y EVENT LISTENERS/EJEMPLOS"
mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 62 - EVENTOS Y EVENT LISTENERS/VISUALIZACIONES"

mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 63 - FORMULARIOS Y VALIDACION/TEORIA"
mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 63 - FORMULARIOS Y VALIDACION/CODIGO"
mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 63 - FORMULARIOS Y VALIDACION/EJEMPLOS"
mkdir -p "PARTE VI - DOM Y EVENTOS/CAPITULO 63 - FORMULARIOS Y VALIDACION/VISUALIZACIONES"

# PARTE VII - APIS DEL NAVEGADOR
mkdir -p "PARTE VII - APIS DEL NAVEGADOR/CAPITULO 64 - FETCH API Y AJAX/TEORIA"
mkdir -p "PARTE VII - APIS DEL NAVEGADOR/CAPITULO 64 - FETCH API Y AJAX/CODIGO"
mkdir -p "PARTE VII - APIS DEL NAVEGADOR/CAPITULO 64 - FETCH API Y AJAX/EJEMPLOS"
mkdir -p "PARTE VII - APIS DEL NAVEGADOR/CAPITULO 64 - FETCH API Y AJAX/VISUALIZACIONES"

mkdir -p "PARTE VII - APIS DEL NAVEGADOR/CAPITULO 65 - LOCAL STORAGE Y SESSION STORAGE/TEORIA"
mkdir -p "PARTE VII - APIS DEL NAVEGADOR/CAPITULO 65 - LOCAL STORAGE Y SESSION STORAGE/CODIGO"
mkdir -p "PARTE VII - APIS DEL NAVEGADOR/CAPITULO 65 - LOCAL STORAGE Y SESSION STORAGE/EJEMPLOS"
mkdir -p "PARTE VII - APIS DEL NAVEGADOR/CAPITULO 65 - LOCAL STORAGE Y SESSION STORAGE/VISUALIZACIONES"

mkdir -p "PARTE VII - APIS DEL NAVEGADOR/CAPITULO 66 - GEOLOCATION Y MULTIMEDIA/TEORIA"
mkdir -p "PARTE VII - APIS DEL NAVEGADOR/CAPITULO 66 - GEOLOCATION Y MULTIMEDIA/CODIGO"
mkdir -p "PARTE VII - APIS DEL NAVEGADOR/CAPITULO 66 - GEOLOCATION Y MULTIMEDIA/EJEMPLOS"
mkdir -p "PARTE VII - APIS DEL NAVEGADOR/CAPITULO 66 - GEOLOCATION Y MULTIMEDIA/VISUALIZACIONES"

# PARTE VIII - MÓDULOS Y BUNDLING
mkdir -p "PARTE VIII - MÓDULOS Y BUNDLING/CAPITULO 67 - ES6 MODULES/TEORIA"
mkdir -p "PARTE VIII - MÓDULOS Y BUNDLING/CAPITULO 67 - ES6 MODULES/CODIGO"
mkdir -p "PARTE VIII - MÓDULOS Y BUNDLING/CAPITULO 67 - ES6 MODULES/EJEMPLOS"
mkdir -p "PARTE VIII - MÓDULOS Y BUNDLING/CAPITULO 67 - ES6 MODULES/VISUALIZACIONES"

mkdir -p "PARTE VIII - MÓDULOS Y BUNDLING/CAPITULO 68 - WEBPACK Y BUNDLERS/TEORIA"
mkdir -p "PARTE VIII - MÓDULOS Y BUNDLING/CAPITULO 68 - WEBPACK Y BUNDLERS/CODIGO"
mkdir -p "PARTE VIII - MÓDULOS Y BUNDLING/CAPITULO 68 - WEBPACK Y BUNDLERS/EJEMPLOS"
mkdir -p "PARTE VIII - MÓDULOS Y BUNDLING/CAPITULO 68 - WEBPACK Y BUNDLERS/VISUALIZACIONES"

mkdir -p "PARTE VIII - MÓDULOS Y BUNDLING/CAPITULO 69 - NPM Y PACKAGE MANAGEMENT/TEORIA"
mkdir -p "PARTE VIII - MÓDULOS Y BUNDLING/CAPITULO 69 - NPM Y PACKAGE MANAGEMENT/CODIGO"
mkdir -p "PARTE VIII - MÓDULOS Y BUNDLING/CAPITULO 69 - NPM Y PACKAGE MANAGEMENT/EJEMPLOS"
mkdir -p "PARTE VIII - MÓDULOS Y BUNDLING/CAPITULO 69 - NPM Y PACKAGE MANAGEMENT/VISUALIZACIONES"

# PARTE IX - TESTING
mkdir -p "PARTE IX - TESTING/CAPITULO 70 - UNIT TESTING/TEORIA"
mkdir -p "PARTE IX - TESTING/CAPITULO 70 - UNIT TESTING/CODIGO"
mkdir -p "PARTE IX - TESTING/CAPITULO 70 - UNIT TESTING/EJEMPLOS"
mkdir -p "PARTE IX - TESTING/CAPITULO 70 - UNIT TESTING/VISUALIZACIONES"

mkdir -p "PARTE IX - TESTING/CAPITULO 71 - INTEGRATION TESTING/TEORIA"
mkdir -p "PARTE IX - TESTING/CAPITULO 71 - INTEGRATION TESTING/CODIGO"
mkdir -p "PARTE IX - TESTING/CAPITULO 71 - INTEGRATION TESTING/EJEMPLOS"
mkdir -p "PARTE IX - TESTING/CAPITULO 71 - INTEGRATION TESTING/VISUALIZACIONES"

mkdir -p "PARTE IX - TESTING/CAPITULO 72 - E2E TESTING/TEORIA"
mkdir -p "PARTE IX - TESTING/CAPITULO 72 - E2E TESTING/CODIGO"
mkdir -p "PARTE IX - TESTING/CAPITULO 72 - E2E TESTING/EJEMPLOS"
mkdir -p "PARTE IX - TESTING/CAPITULO 72 - E2E TESTING/VISUALIZACIONES"

# PARTE X - PERFORMANCE Y OPTIMIZACIÓN
mkdir -p "PARTE X - PERFORMANCE Y OPTIMIZACIÓN/CAPITULO 73 - OPTIMIZACION DE CODIGO/TEORIA"
mkdir -p "PARTE X - PERFORMANCE Y OPTIMIZACIÓN/CAPITULO 73 - OPTIMIZACION DE CODIGO/CODIGO"
mkdir -p "PARTE X - PERFORMANCE Y OPTIMIZACIÓN/CAPITULO 73 - OPTIMIZACION DE CODIGO/EJEMPLOS"
mkdir -p "PARTE X - PERFORMANCE Y OPTIMIZACIÓN/CAPITULO 73 - OPTIMIZACION DE CODIGO/VISUALIZACIONES"

mkdir -p "PARTE X - PERFORMANCE Y OPTIMIZACIÓN/CAPITULO 74 - MEMORY MANAGEMENT/TEORIA"
mkdir -p "PARTE X - PERFORMANCE Y OPTIMIZACIÓN/CAPITULO 74 - MEMORY MANAGEMENT/CODIGO"
mkdir -p "PARTE X - PERFORMANCE Y OPTIMIZACIÓN/CAPITULO 74 - MEMORY MANAGEMENT/EJEMPLOS"
mkdir -p "PARTE X - PERFORMANCE Y OPTIMIZACIÓN/CAPITULO 74 - MEMORY MANAGEMENT/VISUALIZACIONES"

mkdir -p "PARTE X - PERFORMANCE Y OPTIMIZACIÓN/CAPITULO 75 - PROFILING Y DEBUGGING/TEORIA"
mkdir -p "PARTE X - PERFORMANCE Y OPTIMIZACIÓN/CAPITULO 75 - PROFILING Y DEBUGGING/CODIGO"
mkdir -p "PARTE X - PERFORMANCE Y OPTIMIZACIÓN/CAPITULO 75 - PROFILING Y DEBUGGING/EJEMPLOS"
mkdir -p "PARTE X - PERFORMANCE Y OPTIMIZACIÓN/CAPITULO 75 - PROFILING Y DEBUGGING/VISUALIZACIONES"

Write-Host "Estructura de carpetas creada exitosamente para las partes VI-X"
