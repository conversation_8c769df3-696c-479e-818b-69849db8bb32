// Ejemplo de manejo de errores en promesas y async/await

/**
 * Función que simula una operación asíncrona con posible error
 * @param {boolean} shouldFail - Si es true, la promesa se rechaza
 * @returns {Promise<string>}
 */
function operacionAsincrona(shouldFail) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            if (shouldFail) {
                reject(new Error('Ocurrió un error en la operación asíncrona.'));
            } else {
                resolve('Operación completada exitosamente.');
            }
        }, 1000);
    });
}

/**
 * Ejemplo usando async/await con manejo de errores
 */
async function ejecutarOperacion() {
    try {
        // Intentamos ejecutar la operación (puede fallar)
        const resultado = await operacionAsincrona(false);
        console.log('✅ Resultado:', resultado);
    } catch (error) {
        // Capturamos y mostramos el error
        console.error('❌ Error capturado:', error.message);
    }
}

ejecutarOperacion();

// Explicación línea por línea:
// - La función operacionAsincrona retorna una promesa que puede resolverse o rechazarse.
// - ejecutarOperacion usa async/await y try/catch para manejar el error de forma elegante.
// - Si ocurre un error, se captura y se muestra en consola. 