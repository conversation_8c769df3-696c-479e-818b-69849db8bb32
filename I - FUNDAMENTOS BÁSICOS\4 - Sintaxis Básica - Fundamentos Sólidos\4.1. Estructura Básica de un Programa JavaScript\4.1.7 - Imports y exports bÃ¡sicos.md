## 4.1.7. Imports y exports básicos

### Introducción
Los imports y exports básicos en ES6 permiten compartir código entre archivos.  
Exporta variables, funciones o clases para usarlas en otros módulos.  
Esto es esencial para construir aplicaciones modulares.  
Además, mejora la reutilización y organización del código.  
Aprenderlo es clave para el desarrollo moderno en JavaScript.  
¿ Qué exportarías en un módulo básico?

### Código de Ejemplo
```javascript
// utils.js
export const PI = 3.14159;
export function areaCirculo(r) {
    return PI * r * r;
}

// app.js
import { PI, areaCirculo } from './utils.js';
console.log(PI);
console.log(areaCirculo(5));
```
### Resultados en Consola
- `3.14159`
- `78.53975`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> ExportConst["Paso 1: Exportar constante <br> - Nombre: PI <br> - Valor: 3.14159"]
    ExportConst --> ExportFunc["Paso 2: Exportar función <br> - Nombre: areaCirculo(r)"]
    ExportFunc --> ImportItems["Paso 3: Importar {PI, areaCirculo} <br> - Desde: './utils.js'"]
    ImportItems --> LogPI["Paso 4: console.log(PI) <br> - Resultado: 3.14159"]
    LogPI --> CallFunc["Paso 5: Llamar areaCirculo(5)"]
    CallFunc --> LogArea["Paso 6: console.log(areaCirculo(5)) <br> - Resultado: 78.53975"]
    LogArea --> Fin["Paso 7: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Basico["Básico"]
    ExportFunc --> NotaExport["Nota: Definir reutilizables"]
    ImportItems --> NotaImport["Nota: Acceder en app"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ExportConst fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ExportFunc fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ImportItems fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogPI fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CallFunc fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:12px
    style LogArea fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Basico fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaExport fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaImport fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Utils["Utils.js <br> - Export PI, areaCirculo"]
        Utils --> App["App.js <br> - Import {PI, areaCirculo}"]
        App --> Ejecutar["Ejecutar <br> - Log PI, areaCirculo(5)"]
        Ejecutar --> Resultados["Resultados <br> - 3.14159, 78.53975"]
        Inicio --> Desarrollador(("Desarrollador")) --> Compartir["Compartir"]
        Utils --> NotaBasico["Nota: Exports básicos"]
        App --> NotaUso["Nota: Imports para uso"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Utils fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style App fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Ejecutar fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Resultados fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Compartir fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaBasico fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaUso fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Exportar constantes como PI para uso en cálculos matemáticos.
2. Importar funciones utilitarias en scripts principales.
3. Compartir código básico entre diferentes partes de una app.
4. Construir bibliotecas internas con exports simples.
5. Facilitar la integración en proyectos pequeños.

### Errores comunes
1. No especificar el path correcto en imports.
2. Exportar sin usar la palabra clave export.
3. Intentar importar sin exportar previamente.
4. Confundir named exports con default exports.
5. Olvidar comas en imports múltiples.

### Recomendaciones
1. Usa exports named para claridad y escalabilidad.
2. Mantén paths relativos cortos y consistentes.
3. Prueba módulos de forma aislada.
4. Documenta qué se exporta en cada módulo.
5. Usa alias en imports si hay conflictos de nombres.