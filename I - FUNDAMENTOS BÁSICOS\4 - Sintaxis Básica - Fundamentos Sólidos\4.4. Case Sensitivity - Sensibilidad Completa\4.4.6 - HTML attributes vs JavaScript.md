# 4.4.6 - Atributos HTML vs propiedades JavaScript

## Introducción
En JavaScript, al interactuar con el DOM, hay una distinción importante entre los atributos HTML, que son case-insensitive en su definición original (como 'classname' vs 'className'), y las propiedades JavaScript correspondientes, que siguen la sensibilidad al case del lenguaje y suelen usar camelCase para atributos con guiones. Esto significa que mientras HTML trata 'data-id' y 'DATA-ID' como equivalentes, en JavaScript acceder a element.dataset.id requiere coincidencia exacta en case, y discrepancias pueden llevar a undefined o comportamientos inesperados. Entender esta diferencia es crucial para manipular elementos DOM de manera efectiva, evitando errores comunes en aplicaciones web donde se mezclan markup y scripting, y promoviendo prácticas consistentes como usar siempre camelCase en JS para propiedades que corresponden a atributos HTML con guiones, lo que mejora la legibilidad y reduce bugs en código que interactúa con el frontend, especialmente en frameworks modernos que abstraen estas interacciones.

## Ejemplo de Código
```javascript
const element = document.createElement('div');
element.setAttribute('class', 'miClase');

console.log(element.getAttribute('class')); // 'miClase'
console.log(element.getAttribute('CLASS')); // null (en algunos casos, pero generalmente case-insensitive en HTML)
console.log(element.className); // 'miClase' (propiedad JS en camelCase)
```

## Resultados en Consola
miClase
null
miClase

## Diagrama de Flujo de Código
[Flujo: Creación de elemento -> Set atributo case-insensitive -> Get atributo con case diferente -> Null; Acceso a propiedad JS -> Valor correcto]

## Visualización Conceptual
Imagina un puente entre HTML (río case-insensitive) y JavaScript (río case-sensitive); los atributos fluyen y se transforman en propiedades con case específico al cruzar.

## Casos de Uso
Esta distinción es vital en el desarrollo de interfaces dinámicas donde JavaScript manipula atributos HTML, como en librerías de componentes donde propiedades como style.backgroundColor deben usarse en camelCase para estilizar elementos, asegurando compatibilidad cross-browser y evitando fallos en renderizados. En aplicaciones de e-commerce, manejar atributos data-* para tracking requiere precisión en JS para queries correctas, previniendo errores en analíticas que dependen de datos precisos. Además, en frameworks como Vue o Angular, las directivas binden atributos HTML a propiedades JS, y entender el case mapping previene issues en two-way binding, mejorando la reactividad en apps de usuario interactivas, y en accesibilidad, atributos como aria-label deben manejarse con case correcto en JS para cumplir estándares WCAG sin comprometer funcionalidad.

## Errores Comunes
Un error frecuente es asumir que atributos HTML son case-sensitive en JS, llevando a intentos fallidos como element.getAttribute('Class') en lugar de 'class', resultando en null y lógica rota en event handlers. Otro problema ocurre al mezclar notación de guiones en HTML con camelCase en JS, como asignar element.Style.backgroundColor en lugar de element.style.backgroundColor, causando TypeErrors por propiedades undefined. En entornos legacy, código viejo podría depender de quirks mode donde case se ignora, pero en strict mode moderno falla, amplificando issues en migraciones y requiriendo refactorizaciones exhaustivas para alinear con estándares actuales.

## Recomendaciones
Para manejar estas diferencias, siempre usa camelCase para propiedades DOM en JS y lowercase con guiones para atributos HTML, y emplea métodos como getAttribute para accesos directos cuando sea necesario. Utiliza linters con reglas específicas para DOM manipulations, y en pruebas unitarias, incluye casos que verifiquen case sensitivity en diferentes browsers. Documenta en el equipo las convenciones para attribute-property mapping, y considera polyfills para consistencia en browsers antiguos, reduciendo bugs y asegurando código robusto en aplicaciones web complejas a largo plazo.