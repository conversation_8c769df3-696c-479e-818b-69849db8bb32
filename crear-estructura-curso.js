/**
 * GENERADOR DE ESTRUCTURA COMPLETA DEL CURSO
 * ==========================================
 * 
 * Este script crea toda la estructura de carpetas y archivos README
 * para el Curso Completo de JavaScript - Maestría Profesional
 */

const fs = require('fs');
const path = require('path');

// Estructura completa del curso
const estructuraCurso = {
  "PARTE I - FUNDAMENTOS BÁSICOS": {
    capitulos: [
      {
        nombre: "1 - Introducción y Configuración",
        temas: [
          "1.1. Historia y Evolución de JavaScript",
          "1.2. Configuración del Entorno de Desarrollo", 
          "1.3. Herramientas Esenciales",
          "1.4. Primer Programa en JavaScript",
          "1.5. Debugging y Herramientas de Desarrollo"
        ]
      },
      {
        nombre: "2 - Variables y Tipos de Datos",
        temas: [
          "2.1. Declaración de Variables",
          "2.2. Tipos de Datos Primitivos",
          "2.3. Tipos de Datos Complejos", 
          "2.4. Conversión de Tipos",
          "2.5. Scope y Hoisting"
        ]
      },
      {
        nombre: "3 - Operadores y Expresiones",
        temas: [
          "3.1. Operadores Aritméticos",
          "3.2. Operadores de Comparación",
          "3.3. Operadores Lógicos",
          "3.4. Operadores de Asignación",
          "3.5. Precedencia y Asociatividad"
        ]
      },
      {
        nombre: "4 - Estructuras de Control",
        temas: [
          "4.1. Condicionales if-else",
          "4.2. Switch Statement",
          "4.3. Bucles for y while",
          "4.4. Bucles Avanzados",
          "4.5. Control de Flujo"
        ]
      },
      {
        nombre: "5 - Funciones Fundamentales",
        temas: [
          "5.1. Declaración y Expresión de Funciones",
          "5.2. Parámetros y Argumentos",
          "5.3. Arrow Functions",
          "5.4. Scope de Funciones",
          "5.5. Funciones de Alto Orden"
        ]
      }
    ]
  },
  "PARTE II - ESTRUCTURAS DE DATOS": {
    capitulos: [
      {
        nombre: "6 - Arrays y Métodos",
        temas: [
          "6.1. Fundamentos de Arrays",
          "6.2. Métodos de Mutación",
          "6.3. Métodos de Iteración",
          "6.4. Métodos de Búsqueda",
          "6.5. Arrays Multidimensionales"
        ]
      },
      {
        nombre: "7 - Objetos y Propiedades",
        temas: [
          "7.1. Objetos Literales",
          "7.2. Propiedades y Descriptores",
          "7.3. Métodos de Object",
          "7.4. Prototipos y Herencia",
          "7.5. Objetos Avanzados"
        ]
      },
      {
        nombre: "8 - Maps, Sets y WeakMaps",
        temas: [
          "8.1. Map Fundamentals",
          "8.2. Set Operations",
          "8.3. WeakMap y WeakSet",
          "8.4. Comparación con Arrays y Objects",
          "8.5. Casos de Uso Avanzados"
        ]
      },
      {
        nombre: "9 - Estructuras de Datos Avanzadas",
        temas: [
          "9.1. Listas Enlazadas",
          "9.2. Pilas y Colas",
          "9.3. Árboles y Grafos",
          "9.4. Hash Tables",
          "9.5. Algoritmos de Ordenamiento"
        ]
      }
    ]
  },
  "PARTE III - FUNCIONES AVANZADAS": {
    capitulos: [
      {
        nombre: "10 - Closures y Scope Avanzado",
        temas: [
          "10.1. Closures Fundamentales",
          "10.2. Lexical Scope Avanzado",
          "10.3. Module Pattern",
          "10.4. Factory Functions",
          "10.5. Memory Management"
        ]
      },
      {
        nombre: "11 - Programación Funcional",
        temas: [
          "11.1. Principios Funcionales",
          "11.2. Funciones Puras",
          "11.3. Inmutabilidad",
          "11.4. Recursión Avanzada",
          "11.5. Monads y Functors"
        ]
      },
      {
        nombre: "12 - Currying y Composición",
        temas: [
          "12.1. Currying Fundamentals",
          "12.2. Partial Application",
          "12.3. Function Composition",
          "12.4. Point-Free Programming",
          "12.5. Combinators"
        ]
      },
      {
        nombre: "13 - Optimización de Funciones",
        temas: [
          "13.1. Memoización",
          "13.2. Lazy Evaluation",
          "13.3. Debouncing y Throttling",
          "13.4. Performance Profiling",
          "13.5. JIT Optimization"
        ]
      },
      {
        nombre: "14 - Patrones Funcionales Avanzados",
        temas: [
          "14.1. Observer Pattern Funcional",
          "14.2. State Management Funcional",
          "14.3. Pipeline Pattern",
          "14.4. Functional Error Handling",
          "14.5. Functional Architecture"
        ]
      }
    ]
  },
  "PARTE IV - PROGRAMACIÓN ORIENTADA A OBJETOS": {
    capitulos: [
      {
        nombre: "15 - Prototipos y Herencia",
        temas: [
          "15.1. Sistema de Prototipos",
          "15.2. Constructor Functions",
          "15.3. Herencia Prototípica",
          "15.4. Mixins y Composition",
          "15.5. Prototype vs Class"
        ]
      },
      {
        nombre: "16 - Clases ES6+",
        temas: [
          "16.1. Sintaxis de Clases",
          "16.2. Métodos y Propiedades",
          "16.3. Herencia con Extends",
          "16.4. Propiedades Privadas",
          "16.5. Decorators y Metaprogramming"
        ]
      },
      {
        nombre: "17 - Principios SOLID",
        temas: [
          "17.1. Single Responsibility",
          "17.2. Open-Closed Principle",
          "17.3. Liskov Substitution",
          "17.4. Interface Segregation",
          "17.5. Dependency Inversion"
        ]
      },
      {
        nombre: "18 - Patrones de Diseño OOP",
        temas: [
          "18.1. Patrones Creacionales",
          "18.2. Patrones Estructurales",
          "18.3. Patrones de Comportamiento",
          "18.4. Patrones Modernos",
          "18.5. Anti-Patrones"
        ]
      }
    ]
  }
};

// Función para crear directorio si no existe
function crearDirectorio(ruta) {
  if (!fs.existsSync(ruta)) {
    fs.mkdirSync(ruta, { recursive: true });
    console.log(`✅ Creado directorio: ${ruta}`);
  }
}

// Función para crear archivo README básico
function crearREADME(ruta, titulo, tipo = 'tema') {
  const rutaArchivo = path.join(ruta, 'README.md');
  
  if (!fs.existsSync(rutaArchivo)) {
    let contenido = '';
    
    if (tipo === 'tema') {
      contenido = `# **${titulo}**

## **📖 Descripción del Tema**

[Descripción pendiente de completar]

## **🎯 Objetivos de Aprendizaje**

Al completar este tema, serás capaz de:

- [ ] [Objetivo 1]
- [ ] [Objetivo 2]
- [ ] [Objetivo 3]

## **📊 Información del Tema**

- **Dificultad:** ⭐⭐⭐ (Intermedio)
- **Tiempo estimado:** 2-3 horas
- **Subtemas:** 10
- **Ejercicios prácticos:** 5

## **📋 Índice de Contenido**

### **📚 Contenido Teórico**
[Pendiente de completar]

### **💻 Ejemplos Prácticos**
[Pendiente de completar]

### **🧪 Evaluación**
[Pendiente de completar]

---

## **➡️ Navegación**

⬅️ **Anterior:** [Tema anterior]  
➡️ **Siguiente:** [Tema siguiente]  
🏠 **Capítulo:** [Volver al capítulo]

---

**¡Continúa aprendiendo y dominando JavaScript!** 🚀`;
    } else if (tipo === 'capitulo') {
      contenido = `# **${titulo}**

## **📖 Descripción del Capítulo**

[Descripción pendiente de completar]

## **🎯 Objetivos de Aprendizaje**

Al completar este capítulo, serás capaz de:

- [ ] [Objetivo 1]
- [ ] [Objetivo 2]
- [ ] [Objetivo 3]

## **📊 Información del Capítulo**

- **Dificultad:** ⭐⭐⭐ (Intermedio)
- **Tiempo estimado:** 10-15 horas
- **Temas:** 5
- **Subtemas:** 50
- **Ejercicios prácticos:** 25

## **📋 Índice de Temas**

[Pendiente de completar]

---

## **➡️ Navegación**

⬅️ **Anterior:** [Capítulo anterior]  
➡️ **Siguiente:** [Capítulo siguiente]  
🏠 **Parte:** [Volver a la parte]

---

**¡Domina este capítulo y avanza en tu maestría de JavaScript!** 🚀`;
    }
    
    fs.writeFileSync(rutaArchivo, contenido);
    console.log(`📄 Creado README: ${rutaArchivo}`);
  }
}

// Función para crear estructura de tema completa
function crearEstructuraTema(rutaTema, nombreTema) {
  // Crear carpetas principales
  const carpetas = ['contenido', 'ejemplos', 'recursos', 'evaluacion'];
  
  carpetas.forEach(carpeta => {
    const rutaCarpeta = path.join(rutaTema, carpeta);
    crearDirectorio(rutaCarpeta);
    
    // Crear subcarpetas específicas
    if (carpeta === 'recursos') {
      crearDirectorio(path.join(rutaCarpeta, 'diagramas'));
      crearDirectorio(path.join(rutaCarpeta, 'imagenes'));
      
      // Crear archivo de referencias
      const rutaReferencias = path.join(rutaCarpeta, 'referencias.md');
      if (!fs.existsSync(rutaReferencias)) {
        fs.writeFileSync(rutaReferencias, `# **Referencias - ${nombreTema}**

## **📚 Documentación Oficial**
[Pendiente de completar]

## **🔗 Enlaces Útiles**
[Pendiente de completar]

## **📖 Recursos Adicionales**
[Pendiente de completar]`);
      }
    }
    
    if (carpeta === 'ejemplos') {
      // Crear archivos de ejemplo básicos
      const ejemplos = ['ejemplo-basico.js', 'ejemplo-avanzado.js', 'ejercicios.js'];
      ejemplos.forEach(ejemplo => {
        const rutaEjemplo = path.join(rutaCarpeta, ejemplo);
        if (!fs.existsSync(rutaEjemplo)) {
          fs.writeFileSync(rutaEjemplo, `/**
 * ${ejemplo.toUpperCase()} - ${nombreTema}
 * ${'='.repeat(50)}
 * 
 * [Descripción del ejemplo]
 */

// Código de ejemplo aquí
console.log('${nombreTema} - ${ejemplo}');`);
        }
      });
    }
    
    if (carpeta === 'evaluacion') {
      // Crear archivos de evaluación
      const evaluaciones = ['quiz.md', 'proyecto-practico.md'];
      evaluaciones.forEach(eval => {
        const rutaEval = path.join(rutaCarpeta, eval);
        if (!fs.existsSync(rutaEval)) {
          const contenido = eval === 'quiz.md' ? 
            `# **Quiz - ${nombreTema}**

## **📋 Información del Quiz**
- **Preguntas:** 15
- **Tiempo límite:** 20 minutos
- **Puntuación mínima:** 80%

## **📝 Preguntas**
[Pendiente de completar]` :
            `# **Proyecto Práctico - ${nombreTema}**

## **🎯 Objetivo**
[Descripción del proyecto]

## **📋 Requisitos**
[Lista de requisitos]

## **🚀 Implementación**
[Guía de implementación]`;
          
          fs.writeFileSync(rutaEval, contenido);
        }
      });
    }
  });
}

// Función principal para crear toda la estructura
function crearEstructuraCompleta() {
  console.log('🚀 Iniciando creación de estructura completa del curso...\n');
  
  Object.entries(estructuraCurso).forEach(([nombreParte, dataParte]) => {
    console.log(`📚 Procesando: ${nombreParte}`);
    
    // Crear directorio de la parte
    crearDirectorio(nombreParte);
    
    // Procesar cada capítulo
    dataParte.capitulos.forEach(capitulo => {
      const rutaCapitulo = path.join(nombreParte, capitulo.nombre);
      console.log(`  📖 Procesando capítulo: ${capitulo.nombre}`);
      
      // Crear directorio del capítulo
      crearDirectorio(rutaCapitulo);
      
      // Crear README del capítulo
      crearREADME(rutaCapitulo, capitulo.nombre, 'capitulo');
      
      // Procesar cada tema
      capitulo.temas.forEach(tema => {
        const rutaTema = path.join(rutaCapitulo, tema);
        console.log(`    📝 Procesando tema: ${tema}`);
        
        // Crear directorio del tema
        crearDirectorio(rutaTema);
        
        // Crear README del tema
        crearREADME(rutaTema, tema, 'tema');
        
        // Crear estructura completa del tema
        crearEstructuraTema(rutaTema, tema);
        
        // Crear subtemas en la carpeta contenido
        const rutaContenido = path.join(rutaTema, 'contenido');
        for (let i = 1; i <= 10; i++) {
          const numeroTema = tema.split('.')[0] + '.' + tema.split('.')[1];
          const nombreSubtema = `${numeroTema}.${i} - Subtema ${i}.md`;
          const rutaSubtema = path.join(rutaContenido, nombreSubtema);
          
          if (!fs.existsSync(rutaSubtema)) {
            const contenidoSubtema = `# ${numeroTema}.${i} - Subtema ${i}

📍 **Ubicación:** ${nombreParte} > ${capitulo.nombre} > ${tema} > Subtema ${i}

## 🎯 Objetivos de Aprendizaje
[Pendiente de completar]

## 📚 Contenido
[Pendiente de completar]

## 💻 Ejemplos
[Pendiente de completar]

## 🧪 Ejercicios
[Pendiente de completar]

---

## 📝 Resumen
[Pendiente de completar]

## ➡️ Navegación
⬅️ **Anterior:** [Subtema anterior]  
➡️ **Siguiente:** [Subtema siguiente]  
🏠 **Tema:** [Volver al tema](../README.md)`;
            
            fs.writeFileSync(rutaSubtema, contenidoSubtema);
          }
        }
      });
    });
    
    console.log(`✅ Completado: ${nombreParte}\n`);
  });
  
  console.log('🎉 ¡Estructura completa del curso creada exitosamente!');
  console.log('\n📊 Estadísticas:');
  console.log(`- Partes: ${Object.keys(estructuraCurso).length}`);
  console.log(`- Capítulos: ${Object.values(estructuraCurso).reduce((acc, parte) => acc + parte.capitulos.length, 0)}`);
  console.log(`- Temas: ${Object.values(estructuraCurso).reduce((acc, parte) => acc + parte.capitulos.reduce((acc2, cap) => acc2 + cap.temas.length, 0), 0)}`);
  console.log(`- Subtemas: ${Object.values(estructuraCurso).reduce((acc, parte) => acc + parte.capitulos.reduce((acc2, cap) => acc2 + cap.temas.length * 10, 0), 0)}`);
}

// Ejecutar la creación de estructura
if (require.main === module) {
  crearEstructuraCompleta();
}

module.exports = { crearEstructuraCompleta, estructuraCurso };
