<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="constGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#388E3C;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="letGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="varGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F57C00;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="decisionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7B1FA2;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- Title -->
  <rect x="10" y="10" width="1180" height="50" fill="url(#headerGradient)" rx="5" filter="url(#shadow)"/>
  <text x="600" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">
    Declaración de Variables en JavaScript: var, let, const
  </text>
  
  <!-- Start: Need Variable -->
  <rect x="500" y="80" width="200" height="40" fill="url(#constGradient)" rx="5" filter="url(#shadow)"/>
  <text x="600" y="105" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Necesidad de Variable
  </text>
  
  <!-- Arrow 1 -->
  <line x1="600" y1="120" x2="600" y2="140" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Decision: Type of Declaration -->
  <polygon points="600,140 700,170 600,200 500,170" fill="url(#decisionGradient)" filter="url(#shadow)"/>
  <text x="600" y="175" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Tipo de Declaración?
  </text>
  
  <!-- Branch: Constant Value -->
  <line x1="500" y1="170" x2="200" y2="170" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="350" y="165" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="10">Valor Constante</text>
  
  <!-- const Declaration -->
  <rect x="100" y="150" width="100" height="40" fill="url(#constGradient)" rx="5" filter="url(#shadow)"/>
  <text x="150" y="175" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    const
  </text>
  
  <!-- const characteristics -->
  <rect x="50" y="200" width="200" height="80" fill="#e8f5e8" stroke="#4CAF50" stroke-width="2" rx="5"/>
  <text x="150" y="220" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Características:
  </text>
  <text x="60" y="240" fill="#333" font-family="Arial, sans-serif" font-size="10">
    • Block scope
  </text>
  <text x="60" y="255" fill="#333" font-family="Arial, sans-serif" font-size="10">
    • No reasignación
  </text>
  <text x="60" y="270" fill="#333" font-family="Arial, sans-serif" font-size="10">
    • Temporal Dead Zone
  </text>
  
  <!-- Branch: Variable Value -->
  <line x1="600" y1="200" x2="600" y2="240" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="620" y="220" fill="#333" font-family="Arial, sans-serif" font-size="10">Valor Variable</text>
  
  <!-- Decision: Scope Required -->
  <polygon points="600,240 700,270 600,300 500,270" fill="url(#decisionGradient)" filter="url(#shadow)"/>
  <text x="600" y="275" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Scope Requerido?
  </text>
  
  <!-- Branch: Block Scope -->
  <line x1="500" y1="270" x2="350" y2="270" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="425" y="265" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="10">Block Scope</text>
  
  <!-- let Declaration -->
  <rect x="300" y="250" width="100" height="40" fill="url(#letGradient)" rx="5" filter="url(#shadow)"/>
  <text x="350" y="275" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    let
  </text>
  
  <!-- let characteristics -->
  <rect x="250" y="300" width="200" height="80" fill="#e3f2fd" stroke="#2196F3" stroke-width="2" rx="5"/>
  <text x="350" y="320" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Características:
  </text>
  <text x="260" y="340" fill="#333" font-family="Arial, sans-serif" font-size="10">
    • Block scope
  </text>
  <text x="260" y="355" fill="#333" font-family="Arial, sans-serif" font-size="10">
    • Reasignación permitida
  </text>
  <text x="260" y="370" fill="#333" font-family="Arial, sans-serif" font-size="10">
    • Temporal Dead Zone
  </text>
  
  <!-- Branch: Function Scope / Legacy -->
  <line x1="700" y1="270" x2="850" y2="270" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="775" y="265" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="10">Function Scope</text>
  
  <!-- var Declaration -->
  <rect x="800" y="250" width="100" height="40" fill="url(#varGradient)" rx="5" filter="url(#shadow)"/>
  <text x="850" y="275" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    var
  </text>
  
  <!-- var characteristics -->
  <rect x="750" y="300" width="200" height="80" fill="#fff3e0" stroke="#FF9800" stroke-width="2" rx="5"/>
  <text x="850" y="320" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Características:
  </text>
  <text x="760" y="340" fill="#333" font-family="Arial, sans-serif" font-size="10">
    • Function scope
  </text>
  <text x="760" y="355" fill="#333" font-family="Arial, sans-serif" font-size="10">
    • Hoisting con undefined
  </text>
  <text x="760" y="370" fill="#333" font-family="Arial, sans-serif" font-size="10">
    • Redeclaración permitida
  </text>
  
  <!-- Legacy branch from main decision -->
  <line x1="700" y1="170" x2="1000" y2="170" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="850" y="165" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="10">Legacy/Compatibilidad</text>
  
  <!-- Legacy var -->
  <rect x="950" y="150" width="100" height="40" fill="url(#varGradient)" rx="5" filter="url(#shadow)"/>
  <text x="1000" y="175" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    var (legacy)
  </text>
  
  <!-- Convergence arrows to behavior analysis -->
  <line x1="150" y1="280" x2="400" y2="450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="350" y1="380" x2="400" y2="450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="850" y1="380" x2="400" y2="450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1000" y1="190" x2="400" y2="450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Behavior Analysis -->
  <rect x="300" y="450" width="200" height="40" fill="url(#decisionGradient)" rx="5" filter="url(#shadow)"/>
  <text x="400" y="475" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Análisis de Comportamiento
  </text>
  
  <!-- Arrow to behavior details -->
  <line x1="400" y1="490" x2="400" y2="520" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Behavior Details -->
  <rect x="50" y="520" width="700" height="200" fill="#f5f5f5" stroke="#ddd" stroke-width="2" rx="5"/>
  <text x="400" y="540" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="16" font-weight="bold">
    Comparación de Comportamientos
  </text>
  
  <!-- const behavior -->
  <text x="70" y="565" fill="#4CAF50" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    const:
  </text>
  <text x="70" y="580" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • const PI = 3.14159; // Inmutable
  </text>
  <text x="70" y="595" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • const obj = {}; obj.prop = 'value'; // Contenido mutable
  </text>
  <text x="70" y="610" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • Block scope, no hoisting
  </text>
  
  <!-- let behavior -->
  <text x="70" y="635" fill="#2196F3" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    let:
  </text>
  <text x="70" y="650" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • let counter = 0; counter++; // Mutable
  </text>
  <text x="70" y="665" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • Block scope, Temporal Dead Zone
  </text>
  <text x="70" y="680" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • No redeclaración en mismo scope
  </text>
  
  <!-- var behavior -->
  <text x="400" y="565" fill="#FF9800" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    var:
  </text>
  <text x="400" y="580" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • var name = 'John'; // Function scope
  </text>
  <text x="400" y="595" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • Hoisting: undefined antes de declaración
  </text>
  <text x="400" y="610" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • Redeclaración permitida
  </text>
  <text x="400" y="625" fill="#333" font-family="Arial, sans-serif" font-size="11">
    • Problemas en loops con closures
  </text>
  
  <!-- Best Practices -->
  <text x="400" y="650" fill="#333" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Mejores Prácticas:
  </text>
  <text x="400" y="665" fill="#333" font-family="Arial, sans-serif" font-size="11">
    1. Preferir const por defecto
  </text>
  <text x="400" y="680" fill="#333" font-family="Arial, sans-serif" font-size="11">
    2. Usar let cuando reasignación es necesaria
  </text>
  <text x="400" y="695" fill="#333" font-family="Arial, sans-serif" font-size="11">
    3. Evitar var en código nuevo
  </text>
  
  <!-- Arrow to final recommendation -->
  <line x1="400" y1="720" x2="400" y2="750" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Final Recommendation -->
  <rect x="250" y="750" width="300" height="60" fill="url(#constGradient)" rx="5" filter="url(#shadow)"/>
  <text x="400" y="775" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Código Moderno y Mantenible
  </text>
  <text x="400" y="795" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12">
    const > let > var (evitar)
  </text>
  
  <!-- Hoisting Demonstration -->
  <rect x="800" y="520" width="350" height="180" fill="#fff8e1" stroke="#FFC107" stroke-width="2" rx="5"/>
  <text x="975" y="540" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    Demostración de Hoisting
  </text>
  
  <text x="820" y="560" fill="#333" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    var (hoisted):
  </text>
  <text x="820" y="575" fill="#666" font-family="Courier, monospace" font-size="10">
    console.log(x); // undefined
  </text>
  <text x="820" y="590" fill="#666" font-family="Courier, monospace" font-size="10">
    var x = 5;
  </text>
  
  <text x="820" y="610" fill="#333" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    let/const (TDZ):
  </text>
  <text x="820" y="625" fill="#666" font-family="Courier, monospace" font-size="10">
    console.log(y); // ReferenceError
  </text>
  <text x="820" y="640" fill="#666" font-family="Courier, monospace" font-size="10">
    let y = 5;
  </text>
  
  <text x="820" y="660" fill="#333" font-family="Arial, sans-serif" font-size="11" font-weight="bold">
    Temporal Dead Zone:
  </text>
  <text x="820" y="675" fill="#333" font-family="Arial, sans-serif" font-size="10">
    Zona donde la variable existe pero
  </text>
  <text x="820" y="690" fill="#333" font-family="Arial, sans-serif" font-size="10">
    no puede ser accedida antes de su
  </text>
  <text x="820" y="705" fill="#333" font-family="Arial, sans-serif" font-size="10">
    declaración con let/const
  </text>
  
  <!-- Scope Visualization -->
  <rect x="50" y="750" width="180" height="120" fill="#e8f5e8" stroke="#4CAF50" stroke-width="2" rx="5"/>
  <text x="140" y="770" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
    Scope Visualization
  </text>
  
  <text x="60" y="790" fill="#333" font-family="Arial, sans-serif" font-size="10" font-weight="bold">
    Function Scope (var):
  </text>
  <text x="60" y="805" fill="#666" font-family="Arial, sans-serif" font-size="9">
    function() { var x; if(true) { x = 1; } }
  </text>
  
  <text x="60" y="825" fill="#333" font-family="Arial, sans-serif" font-size="10" font-weight="bold">
    Block Scope (let/const):
  </text>
  <text x="60" y="840" fill="#666" font-family="Arial, sans-serif" font-size="9">
    function() { if(true) { let x = 1; } }
  </text>
  <text x="60" y="855" fill="#666" font-family="Arial, sans-serif" font-size="9">
    // x no existe fuera del bloque
  </text>
  
</svg>