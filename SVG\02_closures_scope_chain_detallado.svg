<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .code { font-family: 'Courier New', monospace; font-size: 11px; fill: #e74c3c; }
      .global-scope { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .function-scope { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .block-scope { fill: #f39c12; stroke: #d68910; stroke-width: 2; }
      .closure-scope { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .lexical-env { fill: #1abc9c; stroke: #16a085; stroke-width: 2; }
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
      .memory-ref { stroke: #e67e22; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title">JavaScript Closures y Scope Chain - Análisis Ultra Detallado</text>
  
  <!-- Global Scope -->
  <rect x="50" y="60" width="1300" height="1000" class="global-scope" rx="15" opacity="0.3"/>
  <text x="700" y="85" text-anchor="middle" class="subtitle">🌍 GLOBAL SCOPE (Window/Global Object)</text>
  
  <!-- Global Variables -->
  <rect x="70" y="100" width="300" height="150" fill="#ffffff" stroke="#2980b9" stroke-width="2" rx="5"/>
  <text x="220" y="125" text-anchor="middle" class="subtitle">📋 Global Variables</text>
  <text x="80" y="145" class="text">var globalVar = "I'm global"</text>
  <text x="80" y="160" class="text">let globalLet = 42</text>
  <text x="80" y="175" class="text">const globalConst = true</text>
  <text x="80" y="190" class="text">function outerFunction() {...}</text>
  <text x="80" y="205" class="small-text">Memory Address: 0x7fff5fbff000</text>
  <text x="80" y="220" class="small-text">Hoisted: var, function declarations</text>
  <text x="80" y="235" class="code">[[GlobalObject]]: window</text>
  
  <!-- Outer Function Scope -->
  <rect x="100" y="280" width="1200" height="750" class="function-scope" rx="10" opacity="0.4"/>
  <text x="700" y="305" text-anchor="middle" class="subtitle">🔧 OUTER FUNCTION SCOPE - outerFunction()</text>
  
  <!-- Outer Function Variables -->
  <rect x="120" y="320" width="350" height="180" fill="#ffffff" stroke="#c0392b" stroke-width="2" rx="5"/>
  <text x="295" y="345" text-anchor="middle" class="subtitle">📦 Outer Function Variables</text>
  <text x="130" y="365" class="text">function outerFunction(param1) {</text>
  <text x="140" y="380" class="code">var outerVar = "I'm outer"</text>
  <text x="140" y="395" class="code">let outerLet = param1 * 2</text>
  <text x="140" y="410" class="code">const outerConst = [1,2,3]</text>
  <text x="140" y="425" class="code">let sharedCounter = 0</text>
  <text x="130" y="440" class="text">}</text>
  <text x="130" y="455" class="small-text">Execution Context ID: #ec002</text>
  <text x="130" y="470" class="small-text">Memory Address: 0x7fff5fbff100</text>
  <text x="130" y="485" class="code">[[Scope]]: Global Environment</text>
  
  <!-- Lexical Environment Chain -->
  <rect x="500" y="320" width="350" height="180" fill="#ffffff" stroke="#16a085" stroke-width="2" rx="5"/>
  <text x="675" y="345" text-anchor="middle" class="subtitle">🔗 Lexical Environment</text>
  <text x="510" y="365" class="text">Environment Record:</text>
  <text x="520" y="380" class="code">outerVar: "I'm outer"</text>
  <text x="520" y="395" class="code">outerLet: 84</text>
  <text x="520" y="410" class="code">outerConst: [1,2,3]</text>
  <text x="520" y="425" class="code">sharedCounter: 0</text>
  <text x="510" y="445" class="text">Outer Environment Reference:</text>
  <text x="520" y="460" class="code">→ Global Environment</text>
  <text x="510" y="480" class="small-text">Type: Function Environment</text>
  <text x="510" y="495" class="code">[[ThisBinding]]: undefined</text>
  
  <!-- Inner Function Scope -->
  <rect x="150" y="530" width="1100" height="480" class="function-scope" rx="10" opacity="0.5"/>
  <text x="700" y="555" text-anchor="middle" class="subtitle">⚙️ INNER FUNCTION SCOPE - innerFunction()</text>
  
  <!-- Inner Function Variables -->
  <rect x="170" y="570" width="350" height="200" fill="#ffffff" stroke="#c0392b" stroke-width="2" rx="5"/>
  <text x="345" y="595" text-anchor="middle" class="subtitle">📦 Inner Function Variables</text>
  <text x="180" y="615" class="text">function innerFunction(innerParam) {</text>
  <text x="190" y="630" class="code">var innerVar = "I'm inner"</text>
  <text x="190" y="645" class="code">let innerLet = innerParam + 10</text>
  <text x="190" y="660" class="code">const innerConst = {}</text>
  <text x="190" y="675" class="code">// Access outer variables:</text>
  <text x="190" y="690" class="code">console.log(outerVar)</text>
  <text x="190" y="705" class="code">sharedCounter++</text>
  <text x="180" y="720" class="text">}</text>
  <text x="180" y="735" class="small-text">Execution Context ID: #ec003</text>
  <text x="180" y="750" class="small-text">Memory Address: 0x7fff5fbff200</text>
  <text x="180" y="765" class="code">[[Scope]]: Outer Function Env</text>
  
  <!-- Closure Creation -->
  <rect x="550" y="570" width="350" height="200" fill="#ffffff" stroke="#8e44ad" stroke-width="2" rx="5"/>
  <text x="725" y="595" text-anchor="middle" class="subtitle">🔒 CLOSURE CREATION</text>
  <text x="560" y="615" class="text">return function() {</text>
  <text x="570" y="630" class="code">// Closure captures:</text>
  <text x="570" y="645" class="code">outerVar: "I'm outer"</text>
  <text x="570" y="660" class="code">outerLet: 84</text>
  <text x="570" y="675" class="code">sharedCounter: 0</text>
  <text x="570" y="690" class="code">// Plus references to:</text>
  <text x="570" y="705" class="code">Global Environment</text>
  <text x="560" y="720" class="text">}</text>
  <text x="560" y="735" class="small-text">Closure ID: #closure001</text>
  <text x="560" y="750" class="small-text">Captured Variables: 3</text>
  <text x="560" y="765" class="code">[[ClosureScope]]: Preserved</text>
  
  <!-- Block Scope Example -->
  <rect x="200" y="800" width="400" height="180" class="block-scope" rx="5" opacity="0.6"/>
  <text x="400" y="825" text-anchor="middle" class="subtitle">📦 BLOCK SCOPE (ES6+)</text>
  
  <rect x="220" y="840" width="360" height="130" fill="#ffffff" stroke="#d68910" stroke-width="2" rx="5"/>
  <text x="400" y="865" text-anchor="middle" class="subtitle">🧱 Block Variables</text>
  <text x="230" y="885" class="text">if (condition) {</text>
  <text x="240" y="900" class="code">let blockLet = "block scoped"</text>
  <text x="240" y="915" class="code">const blockConst = 123</text>
  <text x="240" y="930" class="code">var blockVar = "function scoped"</text>
  <text x="230" y="945" class="text">}</text>
  <text x="230" y="960" class="small-text">Block Environment Record</text>
  
  <!-- Memory Representation -->
  <rect x="650" y="800" width="400" height="180" fill="#ffffff" stroke="#e67e22" stroke-width="2" rx="5"/>
  <text x="850" y="825" text-anchor="middle" class="subtitle">💾 MEMORY REPRESENTATION</text>
  <text x="660" y="845" class="text">Heap Memory Layout:</text>
  <text x="670" y="860" class="code">Global Env: 0x1000 → 0x1100</text>
  <text x="670" y="875" class="code">Outer Env: 0x1100 → 0x1200</text>
  <text x="670" y="890" class="code">Inner Env: 0x1200 → 0x1300</text>
  <text x="670" y="905" class="code">Closure: 0x1300 → 0x1400</text>
  <text x="660" y="925" class="text">Reference Chain:</text>
  <text x="670" y="940" class="code">Inner → Outer → Global → null</text>
  <text x="660" y="960" class="small-text">Total Memory: ~2.4KB</text>
  
  <!-- Scope Chain Visualization -->
  <rect x="880" y="320" width="350" height="400" fill="#ffffff" stroke="#27ae60" stroke-width="2" rx="5"/>
  <text x="1055" y="345" text-anchor="middle" class="subtitle">🔍 SCOPE CHAIN LOOKUP</text>
  
  <!-- Variable Lookup Steps -->
  <rect x="890" y="360" width="330" height="50" fill="#d5f4e6" stroke="#27ae60" stroke-width="1" rx="3"/>
  <text x="900" y="375" class="text">1. Variable Lookup: "outerVar"</text>
  <text x="900" y="390" class="small-text">Search Order: Inner → Outer → Global</text>
  <text x="900" y="405" class="code">Found in: Outer Function Scope</text>
  
  <rect x="890" y="420" width="330" height="50" fill="#fef9e7" stroke="#f1c40f" stroke-width="1" rx="3"/>
  <text x="900" y="435" class="text">2. Variable Lookup: "globalVar"</text>
  <text x="900" y="450" class="small-text">Search Order: Inner → Outer → Global</text>
  <text x="900" y="465" class="code">Found in: Global Scope</text>
  
  <rect x="890" y="480" width="330" height="50" fill="#fadbd8" stroke="#e74c3c" stroke-width="1" rx="3"/>
  <text x="900" y="495" class="text">3. Variable Lookup: "undefinedVar"</text>
  <text x="900" y="510" class="small-text">Search Order: Inner → Outer → Global</text>
  <text x="900" y="525" class="code">Result: ReferenceError</text>
  
  <rect x="890" y="540" width="330" height="50" fill="#e8f8f5" stroke="#1abc9c" stroke-width="1" rx="3"/>
  <text x="900" y="555" class="text">4. Closure Access: "sharedCounter"</text>
  <text x="900" y="570" class="small-text">Preserved in closure scope</text>
  <text x="900" y="585" class="code">Value persists after return</text>
  
  <rect x="890" y="600" width="330" height="110" fill="#f4f6f7" stroke="#85929e" stroke-width="1" rx="3"/>
  <text x="900" y="615" class="text">🔧 Optimization Notes:</text>
  <text x="900" y="630" class="small-text">• V8 optimizes scope chain lookup</text>
  <text x="900" y="645" class="small-text">• Hidden classes for property access</text>
  <text x="900" y="660" class="small-text">• Inline caching for repeated lookups</text>
  <text x="900" y="675" class="small-text">• Dead variable elimination</text>
  <text x="900" y="690" class="small-text">• Closure optimization in JIT</text>
  <text x="900" y="705" class="code">Performance: O(1) avg, O(n) worst</text>
  
  <!-- Arrows showing scope chain -->
  <path d="M 345 500 Q 400 450 500 400" class="arrow"/>
  <path d="M 675 500 Q 750 450 880 400" class="arrow"/>
  <path d="M 675 320 Q 750 280 1055 280 Q 1055 300 1055 320" class="arrow dashed"/>
  
  <!-- Memory references -->
  <path d="M 470 400 Q 520 350 550 400" class="memory-ref"/>
  <path d="M 725 570 Q 800 520 850 480" class="memory-ref"/>
  
  <!-- Closure Persistence -->
  <rect x="1080" y="800" width="250" height="180" fill="#ffffff" stroke="#9b59b6" stroke-width="2" rx="5"/>
  <text x="1205" y="825" text-anchor="middle" class="subtitle">♾️ CLOSURE PERSISTENCE</text>
  <text x="1090" y="845" class="text">After Function Returns:</text>
  <text x="1100" y="860" class="code">✅ Closure variables preserved</text>
  <text x="1100" y="875" class="code">✅ Scope chain maintained</text>
  <text x="1100" y="890" class="code">✅ Memory references intact</text>
  <text x="1090" y="910" class="text">Garbage Collection:</text>
  <text x="1100" y="925" class="code">❌ Cannot collect outer vars</text>
  <text x="1100" y="940" class="code">✅ Can collect unused vars</text>
  <text x="1090" y="960" class="small-text">Memory Leak Risk: Medium</text>
  
  <!-- Performance Metrics -->
  <rect x="70" y="270" width="300" height="200" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
  <text x="220" y="295" text-anchor="middle" class="subtitle">📊 PERFORMANCE IMPACT</text>
  <text x="80" y="315" class="text">Scope Chain Depth: 3 levels</text>
  <text x="80" y="330" class="text">Variable Lookups: 1,247/sec</text>
  <text x="80" y="345" class="text">Closure Memory: 1.2KB</text>
  <text x="80" y="360" class="text">GC Pressure: Low</text>
  <text x="80" y="375" class="text">Optimization Level: High</text>
  <text x="80" y="395" class="text">Common Patterns:</text>
  <text x="90" y="410" class="small-text">• Module Pattern</text>
  <text x="90" y="425" class="small-text">• Event Handlers</text>
  <text x="90" y="440" class="small-text">• Callbacks</text>
  <text x="90" y="455" class="small-text">• Private Variables</text>
  
  <!-- Code Example -->
  <rect x="1080" y="570" width="250" height="200" fill="#ffffff" stroke="#34495e" stroke-width="2" rx="5"/>
  <text x="1205" y="595" text-anchor="middle" class="subtitle">💻 CODE EXAMPLE</text>
  <text x="1090" y="615" class="code">function createCounter() {</text>
  <text x="1100" y="630" class="code">let count = 0;</text>
  <text x="1100" y="645" class="code">return function() {</text>
  <text x="1110" y="660" class="code">return ++count;</text>
  <text x="1100" y="675" class="code">};</text>
  <text x="1090" y="690" class="code">}</text>
  <text x="1090" y="710" class="code">const counter = createCounter();</text>
  <text x="1090" y="725" class="code">counter(); // 1</text>
  <text x="1090" y="740" class="code">counter(); // 2</text>
  <text x="1090" y="760" class="small-text">Closure preserves 'count'</text>
</svg>
