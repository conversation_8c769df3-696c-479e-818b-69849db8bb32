<svg width="1000" height="700" xmlns="http://www.w3.org/2000/svg">
  <!-- Definiciones -->
  <defs>
    <!-- Gradientes -->
    <linearGradient id="managerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="promiseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="queueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="metricsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    
    <!-- Sombra -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="3" dy="3" stdDeviation="4" flood-color="#000" flood-opacity="0.3"/>
    </filter>
    
    <!-- Flecha -->
    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
  </defs>

  <!-- Título -->
  <text x="500" y="30" text-anchor="middle" font-size="22" font-weight="bold" fill="#1f2937">
    Anatomía del Sistema de Gestión de Promesas
  </text>
  
  <!-- Contenedor Principal -->
  <rect x="50" y="60" width="900" height="600" fill="#f8fafc" stroke="#64748b" stroke-width="2" rx="15" filter="url(#shadow)"/>
  <text x="70" y="85" font-size="18" font-weight="bold" fill="#374151">AdvancedPromiseManager</text>
  
  <!-- Configuración -->
  <rect x="80" y="100" width="200" height="120" fill="#e0e7ff" stroke="#3b82f6" stroke-width="2" rx="8"/>
  <text x="90" y="120" font-size="14" font-weight="bold" fill="#1e40af">Configuración</text>
  <text x="90" y="140" font-size="11" fill="#1e40af">• maxConcurrency: 5</text>
  <text x="90" y="155" font-size="11" fill="#1e40af">• defaultTimeout: 30000ms</text>
  <text x="90" y="170" font-size="11" fill="#1e40af">• retryAttempts: 3</text>
  <text x="90" y="185" font-size="11" fill="#1e40af">• retryDelay: 1000ms</text>
  <text x="90" y="200" font-size="11" fill="#1e40af">• cleanupInterval: 60000ms</text>
  
  <!-- Estado Interno -->
  <rect x="300" y="100" width="280" height="120" fill="#dcfce7" stroke="#16a34a" stroke-width="2" rx="8"/>
  <text x="310" y="120" font-size="14" font-weight="bold" fill="#16a34a">Estado Interno</text>
  <rect x="310" y="130" width="120" height="25" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="370" y="147" text-anchor="middle" font-size="10" fill="#14532d">activePromises</text>
  <rect x="440" y="130" width="120" height="25" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="500" y="147" text-anchor="middle" font-size="10" fill="#14532d">promiseQueue</text>
  <rect x="310" y="160" width="120" height="25" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="370" y="177" text-anchor="middle" font-size="10" fill="#14532d">completedPromises</text>
  <rect x="440" y="160" width="120" height="25" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="500" y="177" text-anchor="middle" font-size="10" fill="#14532d">failedPromises</text>
  <rect x="310" y="190" width="250" height="25" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="435" y="207" text-anchor="middle" font-size="10" fill="#14532d">eventListeners</text>
  
  <!-- Métricas -->
  <rect x="600" y="100" width="200" height="120" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" rx="8"/>
  <text x="610" y="120" font-size="14" font-weight="bold" fill="#92400e">Métricas</text>
  <text x="610" y="140" font-size="11" fill="#92400e">• totalPromises: 0</text>
  <text x="610" y="155" font-size="11" fill="#92400e">• successfulPromises: 0</text>
  <text x="610" y="170" font-size="11" fill="#92400e">• failedPromises: 0</text>
  <text x="610" y="185" font-size="11" fill="#92400e">• averageExecutionTime: 0</text>
  <text x="610" y="200" font-size="11" fill="#92400e">• concurrentPeak: 0</text>
  
  <!-- Métodos Principales -->
  <rect x="80" y="250" width="820" height="180" fill="#f1f5f9" stroke="#64748b" stroke-width="2" rx="8"/>
  <text x="90" y="275" font-size="16" font-weight="bold" fill="#374151">Métodos Principales</text>
  
  <!-- Creación de Promesas -->
  <rect x="100" y="290" width="180" height="120" fill="url(#promiseGradient)" stroke="#047857" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="190" y="310" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">Creación</text>
  <text x="110" y="330" font-size="10" fill="#fff">• createPromise()</text>
  <text x="110" y="345" font-size="10" fill="#fff">• createRetryablePromise()</text>
  <text x="110" y="360" font-size="10" fill="#fff">• promisify()</text>
  <text x="110" y="375" font-size="10" fill="#fff">• delay()</text>
  <text x="110" y="390" font-size="10" fill="#fff">• timeout()</text>
  
  <!-- Gestión de Concurrencia -->
  <rect x="300" y="290" width="180" height="120" fill="url(#queueGradient)" stroke="#d97706" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="390" y="310" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">Concurrencia</text>
  <text x="310" y="330" font-size="10" fill="#fff">• executeConcurrent()</text>
  <text x="310" y="345" font-size="10" fill="#fff">• executeSequential()</text>
  <text x="310" y="360" font-size="10" fill="#fff">• updateConcurrencyMetrics()</text>
  <text x="310" y="375" font-size="10" fill="#fff">• cancelAllPromises()</text>
  
  <!-- Eventos y Monitoreo -->
  <rect x="500" y="290" width="180" height="120" fill="url(#metricsGradient)" stroke="#7c3aed" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="590" y="310" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">Monitoreo</text>
  <text x="510" y="330" font-size="10" fill="#fff">• on() / emit()</text>
  <text x="510" y="345" font-size="10" fill="#fff">• getMetrics()</text>
  <text x="510" y="360" font-size="10" fill="#fff">• getActivePromises()</text>
  <text x="510" y="375" font-size="10" fill="#fff">• handlePromiseSuccess()</text>
  <text x="510" y="390" font-size="10" fill="#fff">• handlePromiseFailure()</text>
  
  <!-- Utilidades -->
  <rect x="700" y="290" width="180" height="120" fill="#e0e7ff" stroke="#3b82f6" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="790" y="310" text-anchor="middle" font-size="12" font-weight="bold" fill="#1e40af">Utilidades</text>
  <text x="710" y="330" font-size="10" fill="#1e40af">• generatePromiseId()</text>
  <text x="710" y="345" font-size="10" fill="#1e40af">• cleanup()</text>
  <text x="710" y="360" font-size="10" fill="#1e40af">• updateAverageExecutionTime()</text>
  <text x="710" y="375" font-size="10" fill="#1e40af">• destroy()</text>
  
  <!-- Flujo de Datos -->
  <rect x="80" y="450" width="820" height="180" fill="#f8fafc" stroke="#64748b" stroke-width="2" rx="8"/>
  <text x="90" y="475" font-size="16" font-weight="bold" fill="#374151">Flujo de Ejecución de Promesas</text>
  
  <!-- Paso 1: Creación -->
  <rect x="100" y="490" width="140" height="60" fill="#dbeafe" stroke="#2563eb" stroke-width="2" rx="5"/>
  <text x="170" y="510" text-anchor="middle" font-size="11" font-weight="bold" fill="#1e40af">1. Creación</text>
  <text x="170" y="525" text-anchor="middle" font-size="9" fill="#1e40af">createPromise()</text>
  <text x="170" y="540" text-anchor="middle" font-size="9" fill="#1e40af">+ timeout + retry</text>
  
  <!-- Paso 2: Registro -->
  <rect x="260" y="490" width="140" height="60" fill="#dcfce7" stroke="#16a34a" stroke-width="2" rx="5"/>
  <text x="330" y="510" text-anchor="middle" font-size="11" font-weight="bold" fill="#16a34a">2. Registro</text>
  <text x="330" y="525" text-anchor="middle" font-size="9" fill="#16a34a">activePromises.set()</text>
  <text x="330" y="540" text-anchor="middle" font-size="9" fill="#16a34a">emit('promiseCreated')</text>
  
  <!-- Paso 3: Ejecución -->
  <rect x="420" y="490" width="140" height="60" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" rx="5"/>
  <text x="490" y="510" text-anchor="middle" font-size="11" font-weight="bold" fill="#92400e">3. Ejecución</text>
  <text x="490" y="525" text-anchor="middle" font-size="9" fill="#92400e">Promise.race()</text>
  <text x="490" y="540" text-anchor="middle" font-size="9" fill="#92400e">main vs timeout</text>
  
  <!-- Paso 4: Resolución -->
  <rect x="580" y="490" width="140" height="60" fill="#f0fdf4" stroke="#22c55e" stroke-width="2" rx="5"/>
  <text x="650" y="510" text-anchor="middle" font-size="11" font-weight="bold" fill="#16a34a">4. Resolución</text>
  <text x="650" y="525" text-anchor="middle" font-size="9" fill="#16a34a">handleSuccess() /</text>
  <text x="650" y="540" text-anchor="middle" font-size="9" fill="#16a34a">handleFailure()</text>
  
  <!-- Paso 5: Cleanup -->
  <rect x="740" y="490" width="140" height="60" fill="#fce7f3" stroke="#ec4899" stroke-width="2" rx="5"/>
  <text x="810" y="510" text-anchor="middle" font-size="11" font-weight="bold" fill="#be185d">5. Cleanup</text>
  <text x="810" y="525" text-anchor="middle" font-size="9" fill="#be185d">Mover a completed/</text>
  <text x="810" y="540" text-anchor="middle" font-size="9" fill="#be185d">failed, emit evento</text>
  
  <!-- Retry Loop -->
  <rect x="100" y="570" width="300" height="50" fill="#fee2e2" stroke="#dc2626" stroke-width="2" rx="5"/>
  <text x="250" y="590" text-anchor="middle" font-size="12" font-weight="bold" fill="#dc2626">Retry Loop (si falla)</text>
  <text x="250" y="605" text-anchor="middle" font-size="10" fill="#dc2626">Backoff exponencial: delay * 2^(attempt-1)</text>
  
  <!-- Concurrency Control -->
  <rect x="420" y="570" width="300" height="50" fill="#e0f2fe" stroke="#0891b2" stroke-width="2" rx="5"/>
  <text x="570" y="590" text-anchor="middle" font-size="12" font-weight="bold" fill="#0c4a6e">Control de Concurrencia</text>
  <text x="570" y="605" text-anchor="middle" font-size="10" fill="#0c4a6e">Máximo 5 promesas activas simultáneamente</text>
  
  <!-- Flechas de flujo -->
  <line x1="240" y1="520" x2="260" y2="520" stroke="#374151" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="400" y1="520" x2="420" y2="520" stroke="#374151" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="560" y1="520" x2="580" y2="520" stroke="#374151" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="720" y1="520" x2="740" y2="520" stroke="#374151" stroke-width="2" marker-end="url(#arrow)"/>
  
  <!-- Flecha de retry -->
  <path d="M 250 570 Q 200 560 170 550 Q 140 540 170 490" stroke="#dc2626" stroke-width="2" fill="none" stroke-dasharray="5,5" marker-end="url(#arrow)"/>
  
  <!-- Conexiones entre secciones -->
  <line x1="180" y1="220" x2="180" y2="250" stroke="#64748b" stroke-width="1" stroke-dasharray="3,3"/>
  <line x1="440" y1="220" x2="440" y2="250" stroke="#64748b" stroke-width="1" stroke-dasharray="3,3"/>
  <line x1="700" y1="220" x2="700" y2="250" stroke="#64748b" stroke-width="1" stroke-dasharray="3,3"/>
  
  <!-- Etiquetas de conexión -->
  <text x="185" y="240" font-size="9" fill="#64748b">configura</text>
  <text x="445" y="240" font-size="9" fill="#64748b">almacena</text>
  <text x="705" y="240" font-size="9" fill="#64748b">monitorea</text>
</svg>
