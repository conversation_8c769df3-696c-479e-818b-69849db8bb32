/**
 * Sistema Avanzado de Gestión de Datos con Async/Await
 * 
 * Este módulo implementa un gestor completo de datos asíncronos con características empresariales:
 * - Cache inteligente con TTL (Time To Live)
 * - Deduplicación de requests para evitar llamadas duplicadas
 * - Validación asíncrona de datos
 * - Procesamiento en lotes con control de concurrencia
 * - Sistema de métricas y observabilidad
 * - Manejo robusto de errores con retry automático
 * 
 * <AUTHOR> Técnico Superior
 * @version 1.0.0
 */

class AdvancedDataManager {
    constructor(options = {}) {
        // Configuración del gestor
        this.apiBaseUrl = options.apiBaseUrl || 'https://api.example.com';
        this.cacheTimeout = options.cacheTimeout || 300000; // 5 minutos por defecto
        this.maxRetries = options.maxRetries || 3;
        this.batchSize = options.batchSize || 10;
        this.requestTimeout = options.requestTimeout || 30000; // 30 segundos
        
        // Estado interno para cache y deduplicación
        this.cache = new Map();                    // Cache principal con TTL
        this.pendingRequests = new Map();          // Requests en progreso para deduplicación
        this.requestQueue = [];                    // Cola de requests para procesamiento en lotes
        this.isProcessingQueue = false;            // Flag para evitar procesamiento concurrente
        
        // Métricas de performance y observabilidad
        this.metrics = {
            totalRequests: 0,                      // Total de requests realizados
            cacheHits: 0,                          // Número de cache hits
            cacheMisses: 0,                        // Número de cache misses
            averageResponseTime: 0,                // Tiempo promedio de respuesta
            errorRate: 0,                          // Tasa de errores (0-1)
            deduplicationSaves: 0                  // Requests evitados por deduplicación
        };
        
        // Headers por defecto para todas las requests
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'AdvancedDataManager/1.0.0'
        };
        
        // Inicializar limpieza automática de cache
        this.startCacheCleanup();
    }
    
    // ===== MÉTODOS ASYNC PRINCIPALES =====
    
    /**
     * Obtiene datos de una entidad específica con cache inteligente
     * 
     * Este método implementa el patrón Cache-Aside con las siguientes características:
     * - Verificación de cache con TTL automático
     * - Deduplicación de requests simultáneos
     * - Métricas de performance integradas
     * - Manejo robusto de errores
     * 
     * @param {string} entityType - Tipo de entidad (users, products, etc.)
     * @param {string|number} id - ID de la entidad
     * @param {Object} options - Opciones de configuración
     * @param {boolean} options.forceRefresh - Forzar refresh ignorando cache
     * @param {number} options.timeout - Timeout específico para esta request
     * @param {Object} options.headers - Headers adicionales
     * @returns {Promise<Object>} Datos de la entidad con metadata
     */
    async getData(entityType, id, options = {}) {
        const startTime = Date.now();
        const cacheKey = `${entityType}:${id}`;
        const forceRefresh = options.forceRefresh || false;
        
        try {
            // PASO 1: Verificar cache si no se fuerza refresh
            if (!forceRefresh && this.cache.has(cacheKey)) {
                const cachedData = this.cache.get(cacheKey);
                
                // Verificar si el cache no ha expirado
                if (Date.now() - cachedData.timestamp < this.cacheTimeout) {
                    this.metrics.cacheHits++;
                    this.updateMetrics(startTime, true);
                    
                    console.log(`📦 Cache hit para ${entityType}:${id}`);
                    
                    return {
                        ...cachedData.data,
                        _metadata: {
                            source: 'cache',
                            timestamp: cachedData.timestamp,
                            responseTime: Date.now() - startTime,
                            cacheAge: Date.now() - cachedData.timestamp
                        }
                    };
                }
            }
            
            // PASO 2: Verificar si hay una request pendiente (deduplicación)
            if (this.pendingRequests.has(cacheKey)) {
                console.log(`🔗 Reutilizando request en progreso para ${entityType}:${id}`);
                this.metrics.deduplicationSaves++;
                return await this.pendingRequests.get(cacheKey);
            }
            
            // PASO 3: Crear nueva request con deduplicación
            const requestPromise = this.executeRequest('GET', `/${entityType}/${id}`, null, options);
            this.pendingRequests.set(cacheKey, requestPromise);
            
            try {
                console.log(`🚀 Nueva request para ${entityType}:${id}`);
                const data = await requestPromise;
                
                // PASO 4: Guardar en cache con timestamp
                this.cache.set(cacheKey, {
                    data,
                    timestamp: Date.now()
                });
                
                this.metrics.cacheMisses++;
                this.updateMetrics(startTime, true);
                
                return {
                    ...data,
                    _metadata: {
                        source: 'api',
                        timestamp: Date.now(),
                        responseTime: Date.now() - startTime
                    }
                };
                
            } finally {
                // PASO 5: Limpiar request pendiente siempre
                this.pendingRequests.delete(cacheKey);
            }
            
        } catch (error) {
            this.updateMetrics(startTime, false);
            throw this.enhanceError(error, 'getData', { entityType, id });
        }
    }
    
    /**
     * Crea una nueva entidad con validación asíncrona
     * 
     * Implementa el patrón Command con validación previa y retry automático:
     * - Validación asíncrona de datos antes de envío
     * - Retry automático con backoff exponencial
     * - Invalidación inteligente de cache relacionado
     * - Logging detallado para auditoría
     * 
     * @param {string} entityType - Tipo de entidad a crear
     * @param {Object} data - Datos de la entidad
     * @param {Object} options - Opciones de creación
     * @param {boolean} options.validate - Ejecutar validación (default: true)
     * @param {boolean} options.invalidateCache - Invalidar cache relacionado (default: true)
     * @returns {Promise<Object>} Entidad creada con metadata
     */
    async createEntity(entityType, data, options = {}) {
        const startTime = Date.now();
        const validateData = options.validate !== false;
        const invalidateCache = options.invalidateCache !== false;
        
        try {
            console.log(`🔄 Creando ${entityType} con datos:`, Object.keys(data));
            
            // PASO 1: Validación asíncrona de datos si está habilitada
            if (validateData) {
                console.log(`🔍 Validando datos para ${entityType}...`);
                await this.validateEntityData(entityType, data);
                console.log(`✅ Validación exitosa para ${entityType}`);
            }
            
            // PASO 2: Preparar payload con metadatos
            const payload = this.preparePayload(data, options);
            
            // PASO 3: Ejecutar request con retry automático
            console.log(`📤 Enviando request de creación para ${entityType}...`);
            const result = await this.executeWithRetry(
                () => this.executeRequest('POST', `/${entityType}`, payload, options),
                this.maxRetries
            );
            
            // PASO 4: Invalidar cache relacionado si está habilitado
            if (invalidateCache && result.id) {
                const invalidatedCount = await this.invalidateRelatedCache(entityType, result.id);
                console.log(`🗑️ Invalidadas ${invalidatedCount} entradas de cache`);
            }
            
            this.updateMetrics(startTime, true);
            console.log(`✅ ${entityType} creado exitosamente con ID: ${result.id}`);
            
            return {
                ...result,
                _metadata: {
                    created: true,
                    timestamp: Date.now(),
                    responseTime: Date.now() - startTime,
                    validated: validateData
                }
            };
            
        } catch (error) {
            this.updateMetrics(startTime, false);
            console.error(`❌ Error creando ${entityType}:`, error.message);
            throw this.enhanceError(error, 'createEntity', { entityType, data });
        }
    }
    
    /**
     * Actualiza una entidad existente con merge inteligente
     * 
     * Implementa diferentes estrategias de merge:
     * - 'shallow': Merge superficial de propiedades
     * - 'deep': Merge profundo recursivo
     * - 'replace': Reemplaza completamente la entidad
     * 
     * @param {string} entityType - Tipo de entidad
     * @param {string|number} id - ID de la entidad
     * @param {Object} updates - Datos a actualizar
     * @param {Object} options - Opciones de actualización
     * @param {string} options.mergeStrategy - Estrategia de merge ('shallow'|'deep'|'replace')
     * @returns {Promise<Object>} Entidad actualizada
     */
    async updateEntity(entityType, id, updates, options = {}) {
        const startTime = Date.now();
        const mergeStrategy = options.mergeStrategy || 'shallow';
        
        try {
            console.log(`🔄 Actualizando ${entityType}:${id} con estrategia ${mergeStrategy}`);
            
            // PASO 1: Obtener datos actuales si se requiere merge
            let currentData = null;
            if (mergeStrategy !== 'replace') {
                console.log(`📥 Obteniendo datos actuales para merge...`);
                currentData = await this.getData(entityType, id, { forceRefresh: false });
            }
            
            // PASO 2: Aplicar estrategia de merge
            const finalData = this.mergeData(currentData, updates, mergeStrategy);
            console.log(`🔀 Datos merged con estrategia ${mergeStrategy}`);
            
            // PASO 3: Validar datos finales si está habilitado
            if (options.validate !== false) {
                console.log(`🔍 Validando datos finales...`);
                await this.validateEntityData(entityType, finalData);
            }
            
            // PASO 4: Ejecutar actualización con retry
            console.log(`📤 Enviando actualización para ${entityType}:${id}...`);
            const result = await this.executeWithRetry(
                () => this.executeRequest('PUT', `/${entityType}/${id}`, finalData, options),
                this.maxRetries
            );
            
            // PASO 5: Actualizar cache con nuevos datos
            const cacheKey = `${entityType}:${id}`;
            this.cache.set(cacheKey, {
                data: result,
                timestamp: Date.now()
            });
            
            this.updateMetrics(startTime, true);
            console.log(`✅ ${entityType}:${id} actualizado exitosamente`);
            
            return {
                ...result,
                _metadata: {
                    updated: true,
                    mergeStrategy,
                    timestamp: Date.now(),
                    responseTime: Date.now() - startTime
                }
            };
            
        } catch (error) {
            this.updateMetrics(startTime, false);
            console.error(`❌ Error actualizando ${entityType}:${id}:`, error.message);
            throw this.enhanceError(error, 'updateEntity', { entityType, id, updates });
        }
    }
    
    /**
     * Elimina una entidad con cleanup automático
     * 
     * @param {string} entityType - Tipo de entidad
     * @param {string|number} id - ID de la entidad
     * @param {Object} options - Opciones de eliminación
     * @returns {Promise<Object>} Confirmación de eliminación
     */
    async deleteEntity(entityType, id, options = {}) {
        const startTime = Date.now();
        
        try {
            console.log(`🗑️ Eliminando ${entityType}:${id}...`);
            
            // Ejecutar eliminación
            const result = await this.executeWithRetry(
                () => this.executeRequest('DELETE', `/${entityType}/${id}`, null, options),
                this.maxRetries
            );
            
            // Limpiar cache relacionado
            const cacheKey = `${entityType}:${id}`;
            this.cache.delete(cacheKey);
            
            // Invalidar cache relacionado
            const invalidatedCount = await this.invalidateRelatedCache(entityType, id);
            
            this.updateMetrics(startTime, true);
            console.log(`✅ ${entityType}:${id} eliminado exitosamente`);
            
            return {
                deleted: true,
                id,
                entityType,
                invalidatedCache: invalidatedCount,
                _metadata: {
                    timestamp: Date.now(),
                    responseTime: Date.now() - startTime
                }
            };
            
        } catch (error) {
            this.updateMetrics(startTime, false);
            console.error(`❌ Error eliminando ${entityType}:${id}:`, error.message);
            throw this.enhanceError(error, 'deleteEntity', { entityType, id });
        }
    }
    
    // ===== MÉTODOS DE UTILIDAD ASYNC =====
    
    /**
     * Ejecuta una operación con retry automático y backoff exponencial
     * 
     * @param {Function} operation - Función asíncrona a ejecutar
     * @param {number} maxRetries - Número máximo de reintentos
     * @param {number} baseDelay - Delay base en ms para backoff
     * @returns {Promise} Resultado de la operación
     */
    async executeWithRetry(operation, maxRetries = 3, baseDelay = 1000) {
        let lastError;
        
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error;
                
                // No reintentar en el último intento
                if (attempt === maxRetries) {
                    break;
                }
                
                // No reintentar errores de cliente (4xx)
                if (error.status >= 400 && error.status < 500) {
                    break;
                }
                
                // Calcular delay con backoff exponencial
                const delay = baseDelay * Math.pow(2, attempt);
                console.log(`⏳ Reintentando en ${delay}ms (intento ${attempt + 1}/${maxRetries})...`);
                await this.delay(delay);
            }
        }
        
        throw lastError;
    }
    
    /**
     * Ejecuta una request HTTP con timeout y headers configurables
     * 
     * @param {string} method - Método HTTP
     * @param {string} endpoint - Endpoint de la API
     * @param {Object} data - Datos a enviar
     * @param {Object} options - Opciones de la request
     * @returns {Promise<Object>} Respuesta de la API
     */
    async executeRequest(method, endpoint, data = null, options = {}) {
        const url = `${this.apiBaseUrl}${endpoint}`;
        const timeout = options.timeout || this.requestTimeout;
        const headers = { ...this.defaultHeaders, ...options.headers };
        
        // Configurar request
        const config = {
            method,
            headers,
            signal: AbortSignal.timeout(timeout) // Timeout automático
        };
        
        // Añadir body para métodos que lo requieren
        if (data && ['POST', 'PUT', 'PATCH'].includes(method)) {
            config.body = JSON.stringify(data);
        }
        
        console.log(`🌐 ${method} ${url}`);
        const response = await fetch(url, config);
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            const error = new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
            error.status = response.status;
            error.data = errorData;
            throw error;
        }
        
        return await response.json();
    }
    
    /**
     * Valida datos de entidad de forma asíncrona
     *
     * @param {string} entityType - Tipo de entidad
     * @param {Object} data - Datos a validar
     * @returns {Promise<boolean>} True si es válido
     */
    async validateEntityData(entityType, data) {
        // Simular validación asíncrona (en producción sería llamada a servicio de validación)
        await this.delay(100); // Simular latencia de validación

        // Validaciones básicas
        if (!data || typeof data !== 'object') {
            throw new Error('Los datos deben ser un objeto válido');
        }

        // Validaciones específicas por tipo de entidad
        switch (entityType) {
            case 'users':
                if (!data.email || !data.email.includes('@')) {
                    throw new Error('Email válido es requerido para usuarios');
                }
                if (!data.name || data.name.length < 2) {
                    throw new Error('Nombre debe tener al menos 2 caracteres');
                }
                break;

            case 'products':
                if (!data.name || data.name.length < 3) {
                    throw new Error('Nombre de producto debe tener al menos 3 caracteres');
                }
                if (data.price !== undefined && (data.price < 0 || isNaN(data.price))) {
                    throw new Error('Precio debe ser un número positivo');
                }
                break;
        }

        return true;
    }

    /**
     * Prepara payload para envío con metadatos
     *
     * @param {Object} data - Datos originales
     * @param {Object} options - Opciones adicionales
     * @returns {Object} Payload preparado
     */
    preparePayload(data, options = {}) {
        return {
            ...data,
            _metadata: {
                timestamp: new Date().toISOString(),
                source: 'AdvancedDataManager',
                version: '1.0.0',
                ...options.metadata
            }
        };
    }

    /**
     * Merge datos según estrategia especificada
     *
     * @param {Object} currentData - Datos actuales
     * @param {Object} updates - Actualizaciones
     * @param {string} strategy - Estrategia de merge
     * @returns {Object} Datos merged
     */
    mergeData(currentData, updates, strategy) {
        switch (strategy) {
            case 'replace':
                return updates;

            case 'shallow':
                return { ...currentData, ...updates };

            case 'deep':
                return this.deepMerge(currentData, updates);

            default:
                throw new Error(`Estrategia de merge no soportada: ${strategy}`);
        }
    }

    /**
     * Merge profundo recursivo
     *
     * @param {Object} target - Objeto destino
     * @param {Object} source - Objeto fuente
     * @returns {Object} Objeto merged
     */
    deepMerge(target, source) {
        const result = { ...target };

        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.deepMerge(result[key] || {}, source[key]);
            } else {
                result[key] = source[key];
            }
        }

        return result;
    }

    /**
     * Invalida cache relacionado de forma inteligente
     *
     * @param {string} entityType - Tipo de entidad
     * @param {string|number} entityId - ID de la entidad
     * @returns {Promise<number>} Número de entradas invalidadas
     */
    async invalidateRelatedCache(entityType, entityId) {
        let invalidatedCount = 0;

        // Invalidar cache directo
        const directKey = `${entityType}:${entityId}`;
        if (this.cache.has(directKey)) {
            this.cache.delete(directKey);
            invalidatedCount++;
        }

        // Invalidar búsquedas y listas relacionadas
        for (const [key] of this.cache) {
            if (key.startsWith(`${entityType}:list`) ||
                key.startsWith(`${entityType}:search`) ||
                key.includes(`related:${entityId}`)) {
                this.cache.delete(key);
                invalidatedCount++;
            }
        }

        return invalidatedCount;
    }

    /**
     * Inicia limpieza automática de cache
     */
    startCacheCleanup() {
        setInterval(() => {
            this.cleanupExpiredCache();
        }, 60000); // Cada minuto
    }

    /**
     * Limpia entradas de cache expiradas
     */
    cleanupExpiredCache() {
        const now = Date.now();
        let cleanedCount = 0;

        for (const [key, value] of this.cache) {
            if (now - value.timestamp > this.cacheTimeout) {
                this.cache.delete(key);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            console.log(`🧹 Limpiadas ${cleanedCount} entradas de cache expiradas`);
        }
    }

    /**
     * Actualiza métricas de performance
     *
     * @param {number} startTime - Tiempo de inicio
     * @param {boolean} success - Si la operación fue exitosa
     */
    updateMetrics(startTime, success) {
        const responseTime = Date.now() - startTime;
        this.metrics.totalRequests++;

        // Actualizar tiempo promedio de respuesta
        const totalRequests = this.metrics.totalRequests;
        const currentAvg = this.metrics.averageResponseTime;
        this.metrics.averageResponseTime = ((currentAvg * (totalRequests - 1)) + responseTime) / totalRequests;

        // Actualizar tasa de error
        if (!success) {
            this.metrics.errorRate = ((this.metrics.errorRate * (totalRequests - 1)) + 1) / totalRequests;
        } else {
            this.metrics.errorRate = (this.metrics.errorRate * (totalRequests - 1)) / totalRequests;
        }
    }

    /**
     * Mejora errores con contexto adicional
     *
     * @param {Error} error - Error original
     * @param {string} method - Método donde ocurrió el error
     * @param {Object} context - Contexto adicional
     * @returns {Error} Error mejorado
     */
    enhanceError(error, method, context) {
        const enhancedError = new Error(`${method}: ${error.message}`);
        enhancedError.originalError = error;
        enhancedError.method = method;
        enhancedError.context = context;
        enhancedError.timestamp = new Date().toISOString();
        enhancedError.stack = error.stack;

        return enhancedError;
    }

    /**
     * Crea un delay asíncrono
     *
     * @param {number} ms - Milisegundos de delay
     * @returns {Promise<void>}
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Obtiene métricas actuales del gestor
     *
     * @returns {Object} Métricas detalladas
     */
    getMetrics() {
        return {
            ...this.metrics,
            cacheSize: this.cache.size,
            pendingRequests: this.pendingRequests.size,
            cacheHitRate: this.metrics.totalRequests > 0 ?
                (this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) * 100).toFixed(2) + '%' : '0%'
        };
    }

    /**
     * Limpia cache y reinicia métricas
     *
     * @returns {Object} Estadísticas de limpieza
     */
    async cleanup() {
        const stats = {
            cacheCleared: this.cache.size,
            pendingCancelled: this.pendingRequests.size
        };

        this.cache.clear();
        this.pendingRequests.clear();

        // Reiniciar métricas
        this.metrics = {
            totalRequests: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageResponseTime: 0,
            errorRate: 0,
            deduplicationSaves: 0
        };

        return stats;
    }
}

module.exports = AdvancedDataManager;
