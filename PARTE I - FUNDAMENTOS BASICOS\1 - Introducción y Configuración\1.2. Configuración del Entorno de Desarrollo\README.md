# **1.2. Configuración del Entorno de Desarrollo**

## **📖 Descripción del Tema**

La configuración de un entorno de desarrollo profesional es fundamental para el éxito en JavaScript. Este tema te guiará paso a paso para establecer todas las herramientas necesarias, desde la instalación de Node.js hasta la configuración de extensiones avanzadas en VS Code. Al final, tendrás un entorno optimizado que te permitirá desarrollar de manera eficiente y profesional.

## **🎯 Objetivos de Aprendizaje**

Al completar este tema, serás capaz de:

- [ ] Instalar y configurar Node.js correctamente
- [ ] Configurar Visual Studio Code con todas las extensiones esenciales
- [ ] Establecer un workflow de desarrollo eficiente
- [ ] Configurar herramientas de formateo y linting
- [ ] Crear y organizar proyectos JavaScript profesionales
- [ ] Usar la terminal y línea de comandos efectivamente
- [ ] Implementar mejores prácticas desde el inicio

## **📊 Información del Tema**

- **Dificultad:** ⭐⭐⭐ (Intermedio)
- **Tiempo estimado:** 2-3 horas
- **Subtemas:** 10
- **Ejercicios prácticos:** 5
- **Proyecto práctico:** 1

## **📋 Índice de Contenido**

### **📚 Contenido Teórico**

1. **[1.2.1 - Instalación de Node.js](contenido/1.2.1%20-%20Instalación%20de%20Node.js.md)**
   - Descarga e instalación en diferentes sistemas operativos
   - Verificación de la instalación
   - Gestión de versiones con nvm

2. **[1.2.2 - Configuración de Visual Studio Code](contenido/1.2.2%20-%20Configuración%20de%20Visual%20Studio%20Code.md)**
   - Instalación y configuración inicial
   - Personalización de la interfaz
   - Configuración de temas y fuentes

3. **[1.2.3 - Extensiones Esenciales](contenido/1.2.3%20-%20Extensiones%20Esenciales.md)**
   - Extensiones para JavaScript
   - Herramientas de productividad
   - Extensiones de debugging

4. **[1.2.4 - Configuración de Git](contenido/1.2.4%20-%20Configuración%20de%20Git.md)**
   - Instalación y configuración inicial
   - Configuración de usuario
   - Integración con VS Code

5. **[1.2.5 - Terminal y Línea de Comandos](contenido/1.2.5%20-%20Terminal%20y%20Línea%20de%20Comandos.md)**
   - Comandos básicos
   - Terminal integrada en VS Code
   - Personalización del terminal

6. **[1.2.6 - Gestores de Paquetes](contenido/1.2.6%20-%20Gestores%20de%20Paquetes.md)**
   - npm vs yarn
   - Configuración y uso básico
   - Gestión de dependencias

7. **[1.2.7 - Configuración de Prettier y ESLint](contenido/1.2.7%20-%20Configuración%20de%20Prettier%20y%20ESLint.md)**
   - Instalación y configuración
   - Reglas y personalización
   - Integración con VS Code

8. **[1.2.8 - Live Server y Desarrollo Local](contenido/1.2.8%20-%20Live%20Server%20y%20Desarrollo%20Local.md)**
   - Configuración de servidor local
   - Hot reload y live preview
   - Debugging en tiempo real

9. **[1.2.9 - Estructura de Proyectos](contenido/1.2.9%20-%20Estructura%20de%20Proyectos.md)**
   - Organización de archivos y carpetas
   - Convenciones de nomenclatura
   - Mejores prácticas

10. **[1.2.10 - Mejores Prácticas de Configuración](contenido/1.2.10%20-%20Mejores%20Prácticas%20de%20Configuración.md)**
    - Configuraciones recomendadas
    - Optimización del workflow
    - Mantenimiento del entorno

---

### **💻 Ejemplos Prácticos**

- **[ejemplo-basico.js](ejemplos/ejemplo-basico.js)** - Configuración básica de proyecto
- **[ejemplo-avanzado.js](ejemplos/ejemplo-avanzado.js)** - Proyecto con todas las herramientas
- **[ejercicios.js](ejemplos/ejercicios.js)** - Ejercicios de configuración

---

### **📊 Recursos Adicionales**

- **[Diagramas](recursos/diagramas/)** - Visualizaciones del proceso de configuración
- **[Imágenes](recursos/imagenes/)** - Screenshots paso a paso
- **[Referencias](recursos/referencias.md)** - Enlaces y documentación adicional

---

### **🧪 Evaluación**

- **[Quiz](evaluacion/quiz.md)** - Evaluación de conocimientos
- **[Proyecto Práctico](evaluacion/proyecto-practico.md)** - Configuración completa del entorno

---

## **🎯 Rutas de Aprendizaje**

### **🚀 Ruta Rápida (1 hora)**
Configuración mínima para comenzar:
- 1.2.1 - Instalación de Node.js
- 1.2.2 - Configuración básica de VS Code
- 1.2.3 - Extensiones esenciales (solo las básicas)

### **📚 Ruta Completa (2-3 horas)**
Configuración profesional completa:
- Todos los subtemas
- Ejercicios prácticos
- Proyecto de configuración

### **🔬 Ruta Experto (3-4 horas)**
Configuración avanzada y personalizada:
- Ruta completa
- Configuraciones personalizadas
- Herramientas adicionales
- Optimizaciones avanzadas

---

## **📊 Progreso del Tema**

```
1.2.1 - Node.js:           [░░░░░░░░░░] 0%
1.2.2 - VS Code:           [░░░░░░░░░░] 0%
1.2.3 - Extensiones:       [░░░░░░░░░░] 0%
1.2.4 - Git:               [░░░░░░░░░░] 0%
1.2.5 - Terminal:          [░░░░░░░░░░] 0%
1.2.6 - Gestores:          [░░░░░░░░░░] 0%
1.2.7 - Prettier/ESLint:   [░░░░░░░░░░] 0%
1.2.8 - Live Server:       [░░░░░░░░░░] 0%
1.2.9 - Estructura:        [░░░░░░░░░░] 0%
1.2.10 - Mejores Prácticas:[░░░░░░░░░░] 0%

Progreso Total: [░░░░░░░░░░] 0% completado
```

## **🏆 Logros Disponibles**

- 🔧 **Instalador**: Completar instalación de Node.js
- 💻 **Configurador**: Configurar VS Code completamente
- 🎨 **Personalizado**: Personalizar entorno de desarrollo
- 📦 **Gestor**: Configurar gestores de paquetes
- ✨ **Profesional**: Completar configuración completa
- 🚀 **Experto**: Configuración avanzada y optimizada

---

## **📝 Checklist de Configuración**

### **Instalaciones Básicas**
- [ ] Node.js instalado y verificado
- [ ] Visual Studio Code instalado
- [ ] Git instalado y configurado
- [ ] Terminal configurado

### **Extensiones VS Code**
- [ ] ES7+ React/Redux/React-Native snippets
- [ ] Prettier - Code formatter
- [ ] ESLint
- [ ] Live Server
- [ ] Bracket Pair Colorizer
- [ ] Auto Rename Tag
- [ ] Path Intellisense
- [ ] GitLens

### **Configuraciones**
- [ ] Prettier configurado
- [ ] ESLint configurado
- [ ] Git usuario configurado
- [ ] Terminal personalizado
- [ ] Workspace settings configurado

### **Verificaciones**
- [ ] `node --version` funciona
- [ ] `npm --version` funciona
- [ ] `git --version` funciona
- [ ] Live Server funciona
- [ ] Formateo automático funciona
- [ ] Linting funciona

---

## **⚠️ Problemas Comunes y Soluciones**

### **1. Node.js no se reconoce**
**Problema:** Comando `node` no encontrado
**Solución:** Verificar PATH del sistema y reinstalar Node.js

### **2. Extensiones no funcionan**
**Problema:** Extensiones instaladas pero no activas
**Solución:** Recargar VS Code y verificar configuración

### **3. Git no configurado**
**Problema:** Error al hacer commit
**Solución:** Configurar usuario y email de Git

### **4. Prettier no formatea**
**Problema:** Código no se formatea automáticamente
**Solución:** Verificar configuración de formato al guardar

### **5. Live Server no inicia**
**Problema:** Servidor local no se ejecuta
**Solución:** Verificar puerto y permisos

---

## **🔗 Enlaces Útiles**

- [Node.js Official Website](https://nodejs.org/)
- [Visual Studio Code](https://code.visualstudio.com/)
- [Git Documentation](https://git-scm.com/doc)
- [npm Documentation](https://docs.npmjs.com/)
- [Prettier Documentation](https://prettier.io/docs/)
- [ESLint Documentation](https://eslint.org/docs/)

---

## **📍 Navegación**

⬅️ **Anterior:** [1.1. Historia y Evolución de JavaScript](../1.1.%20Historia%20y%20Evolución%20de%20JavaScript/README.md)  
➡️ **Siguiente:** [1.3. Herramientas Esenciales](../1.3.%20Herramientas%20Esenciales/README.md)  
🏠 **Capítulo:** [Capítulo 1 - Introducción y Configuración](../README.md)  
📚 **Parte:** [Parte I - Fundamentos Básicos](../../README.md)

---

**¡Comienza configurando tu entorno de desarrollo profesional! Un buen setup es la base del éxito en programación.** 🚀
