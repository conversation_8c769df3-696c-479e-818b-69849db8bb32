# 4.5.2 - Reserved words actuales

## Introducción
Las reserved words actuales en JavaScript incluyen términos como 'break', 'case', 'catch' y otros que están protegidos para usos específicos en la sintaxis del lenguaje, impidiendo su empleo como nombres de variables o funciones para mantener la integridad del código y evitar ambigüedades en la interpretación por el motor de ejecución. Estas palabras son parte integral de estructuras como bucles, condicionales y manejo de excepciones, y su comprensión es vital para desarrollar código robusto, especialmente al migrar entre versiones de ECMAScript donde algunas palabras podrían cambiar de estatus, lo que afecta la compatibilidad y requiere precauciones en entornos legacy o modernos, fomentando prácticas que eviten colisiones y promuevan claridad en proyectos colaborativos.

## Ejemplo de Código
```javascript
// Intento inválido: let break = 1; // SyntaxError
let myBreak = 1; // Válido
console.log(myBreak);
```

## Resultados en Consola
1

## Diagrama de Flujo de Código
<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg"><rect x="10" y="10" width="280" height="180" fill="none" stroke="black"/><text x="150" y="50" text-anchor="middle">Intento de uso</text><line x1="150" y1="60" x2="150" y2="100" stroke="black"/><text x="150" y="130" text-anchor="middle">Error si reserved</text><line x1="150" y1="140" x2="150" y2="180" stroke="black"/></svg>

## Visualización Conceptual
<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg"><rect x="20" y="20" width="160" height="160" fill="lightgreen"/><text x="100" y="100" text-anchor="middle">Reserved Words</text><text x="100" y="120" text-anchor="middle">Actuales</text></svg>

## Casos de Uso
- Uso de 'break' para salir de bucles en iteraciones condicionales.
- Implementación de 'case' en switches para manejo de múltiples escenarios.
- Empleo de 'catch' en bloques try-catch para gestión de errores.
- Aplicación de 'do' en bucles do-while para ejecuciones garantizadas.
- Utilización de 'else' en condicionales para ramas alternativas.

## Errores Comunes
- Asignar reserved words a variables, generando SyntaxError.
- Ignorar reserved words en strict mode, causando fallos inesperados.
- Confundir reserved words con variables en scopes globales.
- No actualizar código legacy con nuevas reserved words.
- Usar reserved words en objetos sin comillas, rompiendo sintaxis.

## Recomendaciones
- Verificar listas de reserved words por versión de ECMAScript.
- Implementar linters para alertas sobre usos inválidos.
- Usar prefijos en nombres para evitar conflictos con reserved words.
- Activar strict mode para protecciones adicionales.
- Realizar pruebas de compatibilidad al introducir nuevas reserved words.