/**
 * Ejemplos Intermedios de Async/Await en OOP
 * 
 * Este archivo demuestra patrones avanzados de async/await en programación
 * orientada a objetos, incluyendo casos de uso empresariales reales.
 */

const AdvancedDataManager = require('../../CODIGO/async-await/DataManager');

// ===== EJEMPLO 1: SISTEMA DE AUTENTICACIÓN ASÍNCRONO =====

/**
 * Sistema de autenticación que maneja login, permisos y sesiones
 * de forma completamente asíncrona con cache de tokens y validación.
 */
class AsyncAuthenticationSystem {
    constructor(options = {}) {
        this.dataManager = new AdvancedDataManager(options);
        this.tokenCache = new Map();
        this.sessionTimeout = options.sessionTimeout || 3600000; // 1 hora
        this.maxLoginAttempts = options.maxLoginAttempts || 3;
        this.loginAttempts = new Map(); // Track intentos por usuario
    }
    
    /**
     * Proceso completo de login asíncrono
     * 
     * @param {string} email - Email del usuario
     * @param {string} password - Contraseña
     * @returns {Promise<Object>} Resultado del login con token y permisos
     */
    async login(email, password) {
        const startTime = Date.now();
        console.log(`🔐 Iniciando login para: ${email}`);
        
        try {
            // PASO 1: Verificar rate limiting de intentos de login
            await this.checkLoginRateLimit(email);
            
            // PASO 2: Validar credenciales de forma asíncrona
            console.log(`🔍 Validando credenciales para ${email}...`);
            const user = await this.validateCredentials(email, password);
            
            // PASO 3: Verificar estado de la cuenta
            console.log(`👤 Verificando estado de cuenta...`);
            await this.checkAccountStatus(user);
            
            // PASO 4: Obtener permisos del usuario
            console.log(`🔑 Obteniendo permisos para ${user.role}...`);
            const permissions = await this.getUserPermissions(user.id, user.role);
            
            // PASO 5: Generar token de sesión
            console.log(`🎫 Generando token de sesión...`);
            const token = await this.generateSessionToken(user, permissions);
            
            // PASO 6: Registrar login exitoso
            await this.logSuccessfulLogin(user, startTime);
            
            // Limpiar intentos fallidos
            this.loginAttempts.delete(email);
            
            const loginTime = Date.now() - startTime;
            console.log(`✅ Login exitoso para ${email} en ${loginTime}ms`);
            
            return {
                success: true,
                user: {
                    id: user.id,
                    email: user.email,
                    name: user.name,
                    role: user.role
                },
                token,
                permissions,
                expiresAt: Date.now() + this.sessionTimeout,
                loginTime
            };
            
        } catch (error) {
            // Registrar intento fallido
            await this.recordFailedLogin(email, error);
            
            const loginTime = Date.now() - startTime;
            console.error(`❌ Login fallido para ${email}: ${error.message} (${loginTime}ms)`);
            
            throw {
                success: false,
                error: error.message,
                loginTime,
                attemptsRemaining: this.getRemainingAttempts(email)
            };
        }
    }
    
    /**
     * Valida credenciales contra base de datos
     * 
     * @param {string} email - Email del usuario
     * @param {string} password - Contraseña
     * @returns {Promise<Object>} Datos del usuario si es válido
     */
    async validateCredentials(email, password) {
        // Simular búsqueda en base de datos
        await this.delay(200); // Simular latencia de DB
        
        // En producción sería: SELECT * FROM users WHERE email = ? AND password_hash = ?
        const users = [
            { id: 1, email: '<EMAIL>', password: 'admin123', name: 'Admin User', role: 'admin', status: 'active' },
            { id: 2, email: '<EMAIL>', password: 'user123', name: 'Regular User', role: 'user', status: 'active' },
            { id: 3, email: '<EMAIL>', password: 'blocked123', name: 'Blocked User', role: 'user', status: 'blocked' }
        ];
        
        const user = users.find(u => u.email === email && u.password === password);
        
        if (!user) {
            throw new Error('Credenciales inválidas');
        }
        
        return user;
    }
    
    /**
     * Verifica el estado de la cuenta del usuario
     * 
     * @param {Object} user - Datos del usuario
     * @returns {Promise<void>}
     */
    async checkAccountStatus(user) {
        await this.delay(100); // Simular verificación
        
        switch (user.status) {
            case 'blocked':
                throw new Error('Cuenta bloqueada. Contacte al administrador');
            case 'suspended':
                throw new Error('Cuenta suspendida temporalmente');
            case 'pending':
                throw new Error('Cuenta pendiente de activación');
            case 'active':
                return; // OK
            default:
                throw new Error('Estado de cuenta desconocido');
        }
    }
    
    /**
     * Obtiene permisos del usuario basado en su rol
     * 
     * @param {number} userId - ID del usuario
     * @param {string} role - Rol del usuario
     * @returns {Promise<Array>} Lista de permisos
     */
    async getUserPermissions(userId, role) {
        // Verificar cache de permisos primero
        const cacheKey = `permissions:${role}`;
        
        try {
            // Intentar obtener de cache
            const cached = await this.dataManager.getData('permissions', role);
            return cached.permissions;
        } catch (error) {
            // Si no está en cache, obtener de "base de datos"
            await this.delay(150); // Simular query a DB
            
            const rolePermissions = {
                admin: ['read', 'write', 'delete', 'admin', 'user_management'],
                user: ['read', 'write'],
                guest: ['read']
            };
            
            const permissions = rolePermissions[role] || ['read'];
            
            // Guardar en cache para futuras consultas
            // En producción esto se haría automáticamente por el DataManager
            
            return permissions;
        }
    }
    
    /**
     * Genera token de sesión seguro
     * 
     * @param {Object} user - Datos del usuario
     * @param {Array} permissions - Permisos del usuario
     * @returns {Promise<string>} Token de sesión
     */
    async generateSessionToken(user, permissions) {
        await this.delay(50); // Simular generación criptográfica
        
        // En producción usarías JWT o similar
        const tokenData = {
            userId: user.id,
            email: user.email,
            role: user.role,
            permissions,
            issuedAt: Date.now(),
            expiresAt: Date.now() + this.sessionTimeout
        };
        
        // Simular token JWT
        const token = `jwt.${Buffer.from(JSON.stringify(tokenData)).toString('base64')}.signature`;
        
        // Guardar en cache de tokens
        this.tokenCache.set(token, tokenData);
        
        return token;
    }
    
    /**
     * Verifica rate limiting para intentos de login
     * 
     * @param {string} email - Email del usuario
     * @returns {Promise<void>}
     */
    async checkLoginRateLimit(email) {
        const attempts = this.loginAttempts.get(email) || { count: 0, lastAttempt: 0 };
        const now = Date.now();
        const timeSinceLastAttempt = now - attempts.lastAttempt;
        
        // Reset contador si han pasado más de 15 minutos
        if (timeSinceLastAttempt > 900000) { // 15 minutos
            attempts.count = 0;
        }
        
        if (attempts.count >= this.maxLoginAttempts) {
            const waitTime = Math.max(0, 900000 - timeSinceLastAttempt); // 15 minutos
            throw new Error(`Demasiados intentos fallidos. Intente nuevamente en ${Math.ceil(waitTime / 60000)} minutos`);
        }
    }
    
    /**
     * Registra intento de login fallido
     * 
     * @param {string} email - Email del usuario
     * @param {Error} error - Error ocurrido
     * @returns {Promise<void>}
     */
    async recordFailedLogin(email, error) {
        const attempts = this.loginAttempts.get(email) || { count: 0, lastAttempt: 0 };
        attempts.count++;
        attempts.lastAttempt = Date.now();
        this.loginAttempts.set(email, attempts);
        
        // En producción, registrar en logs de seguridad
        console.log(`🚨 Login fallido para ${email}: ${error.message} (Intento ${attempts.count}/${this.maxLoginAttempts})`);
    }
    
    /**
     * Registra login exitoso para auditoría
     * 
     * @param {Object} user - Datos del usuario
     * @param {number} startTime - Tiempo de inicio del login
     * @returns {Promise<void>}
     */
    async logSuccessfulLogin(user, startTime) {
        // En producción, registrar en logs de auditoría
        const loginRecord = {
            userId: user.id,
            email: user.email,
            timestamp: new Date().toISOString(),
            duration: Date.now() - startTime,
            ip: '127.0.0.1', // En producción obtener IP real
            userAgent: 'AsyncAuthSystem/1.0'
        };
        
        console.log(`📝 Login registrado:`, loginRecord);
    }
    
    /**
     * Obtiene intentos restantes para un email
     * 
     * @param {string} email - Email del usuario
     * @returns {number} Intentos restantes
     */
    getRemainingAttempts(email) {
        const attempts = this.loginAttempts.get(email) || { count: 0 };
        return Math.max(0, this.maxLoginAttempts - attempts.count);
    }
    
    /**
     * Valida token de sesión
     * 
     * @param {string} token - Token a validar
     * @returns {Promise<Object>} Datos del token si es válido
     */
    async validateToken(token) {
        const tokenData = this.tokenCache.get(token);
        
        if (!tokenData) {
            throw new Error('Token inválido o expirado');
        }
        
        if (Date.now() > tokenData.expiresAt) {
            this.tokenCache.delete(token);
            throw new Error('Token expirado');
        }
        
        return tokenData;
    }
    
    /**
     * Logout del usuario
     * 
     * @param {string} token - Token de sesión
     * @returns {Promise<Object>} Confirmación de logout
     */
    async logout(token) {
        try {
            const tokenData = await this.validateToken(token);
            this.tokenCache.delete(token);
            
            console.log(`👋 Logout exitoso para usuario ${tokenData.userId}`);
            
            return {
                success: true,
                message: 'Logout exitoso',
                userId: tokenData.userId
            };
        } catch (error) {
            throw new Error(`Error en logout: ${error.message}`);
        }
    }
    
    /**
     * Utility para crear delays
     * 
     * @param {number} ms - Milisegundos de delay
     * @returns {Promise<void>}
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// ===== EJEMPLO 2: SISTEMA DE NOTIFICACIONES ASÍNCRONO =====

/**
 * Sistema que procesa y envía notificaciones a múltiples canales
 * de forma asíncrona con retry y fallbacks.
 */
class AsyncNotificationSystem {
    constructor(options = {}) {
        this.channels = {
            email: { enabled: true, timeout: 5000 },
            sms: { enabled: true, timeout: 3000 },
            push: { enabled: true, timeout: 2000 },
            webhook: { enabled: false, timeout: 10000 }
        };
        this.retryAttempts = options.retryAttempts || 2;
        this.batchSize = options.batchSize || 5;
    }
    
    /**
     * Envía notificación a múltiples canales
     * 
     * @param {Object} notification - Datos de la notificación
     * @param {Array} channels - Canales a usar
     * @returns {Promise<Object>} Resultado del envío
     */
    async sendNotification(notification, channels = ['email']) {
        console.log(`📢 Enviando notificación: "${notification.title}" a canales: ${channels.join(', ')}`);
        
        const results = await Promise.allSettled(
            channels.map(channel => this.sendToChannel(notification, channel))
        );
        
        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;
        
        console.log(`📊 Notificación enviada: ${successful} exitosos, ${failed} fallidos`);
        
        return {
            notification: notification.id,
            successful,
            failed,
            results: results.map((result, index) => ({
                channel: channels[index],
                status: result.status,
                value: result.value,
                reason: result.reason?.message
            }))
        };
    }
    
    /**
     * Envía notificación a un canal específico
     * 
     * @param {Object} notification - Datos de la notificación
     * @param {string} channel - Canal de envío
     * @returns {Promise<Object>} Resultado del envío
     */
    async sendToChannel(notification, channel) {
        const channelConfig = this.channels[channel];
        
        if (!channelConfig || !channelConfig.enabled) {
            throw new Error(`Canal ${channel} no disponible`);
        }
        
        // Simular envío con posibilidad de fallo
        const success = Math.random() > 0.2; // 80% de éxito
        const delay = Math.random() * channelConfig.timeout;
        
        await new Promise(resolve => setTimeout(resolve, delay));
        
        if (!success) {
            throw new Error(`Fallo en envío por ${channel}`);
        }
        
        return {
            channel,
            messageId: `${channel}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            sentAt: new Date().toISOString(),
            delay: Math.round(delay)
        };
    }
}

// ===== EJECUCIÓN DE EJEMPLOS =====

async function runAsyncPatternExamples() {
    console.log('🚀 Iniciando ejemplos de patrones async/await...\n');
    
    // Ejemplo 1: Sistema de Autenticación
    console.log('=== EJEMPLO 1: Sistema de Autenticación Asíncrono ===');
    const authSystem = new AsyncAuthenticationSystem();
    
    try {
        // Login exitoso
        const loginResult = await authSystem.login('<EMAIL>', 'admin123');
        console.log('Login exitoso:', {
            user: loginResult.user.name,
            role: loginResult.user.role,
            permissions: loginResult.permissions,
            loginTime: loginResult.loginTime
        });
        
        // Validar token
        const tokenValidation = await authSystem.validateToken(loginResult.token);
        console.log('Token válido para usuario:', tokenValidation.userId);
        
        // Logout
        const logoutResult = await authSystem.logout(loginResult.token);
        console.log('Logout:', logoutResult.message);
        
    } catch (error) {
        console.error('Error en autenticación:', error);
    }
    
    // Ejemplo 2: Sistema de Notificaciones
    console.log('\n=== EJEMPLO 2: Sistema de Notificaciones Asíncrono ===');
    const notificationSystem = new AsyncNotificationSystem();
    
    const notification = {
        id: 'notif_123',
        title: 'Bienvenido al sistema',
        message: 'Su cuenta ha sido activada exitosamente',
        userId: 1,
        priority: 'high'
    };
    
    try {
        const result = await notificationSystem.sendNotification(
            notification, 
            ['email', 'sms', 'push']
        );
        
        console.log('Resultado de notificaciones:', result);
        
    } catch (error) {
        console.error('Error en notificaciones:', error);
    }
    
    console.log('\n✅ Ejemplos de patrones async/await completados!');
}

// Ejecutar si es el archivo principal
if (require.main === module) {
    runAsyncPatternExamples().catch(console.error);
}

module.exports = {
    AsyncAuthenticationSystem,
    AsyncNotificationSystem
};
