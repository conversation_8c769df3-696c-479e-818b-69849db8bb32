## 4.1.3. Statements y su terminación

### Introducción
Los statements en JavaScript son instrucciones que realizan acciones.  
Su terminación se marca comúnmente con punto y coma para claridad.  
Esto es crucial para evitar errores de sintaxis en el código.  
Ad<PERSON><PERSON>, ayuda a mantener un código legible y predecible.  
Entenderlo previene problemas comunes en el desarrollo inicial.  
¿ Cómo crees que afecta la terminación a la ejecución del código?

### Código de Ejemplo
```javascript
let a = 5;
let b = 10;
console.log(a + b);
if (a < b) {
    console.log('a es menor que b');
}
```
### Resultados en Consola
- `15`
- `a es menor que b`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> Declaracion1["Paso 1: Declaración <br> - Asignar: let a = 5"]
    Declaracion1 --> Declaracion2["Paso 2: Declaración <br> - Asignar: let b = 10"]
    Declaracion2 --> Log1["Paso 3: console.log(a + b) <br> - Resultado: 15"]
    Log1 --> Condicion["Paso 4: If (a < b) <br> - Condición: verdadera"]
    Condicion --> Log2["Paso 5: console.log('a es menor que b') <br> - Resultado: a es menor que b"]
    Log2 --> Fin["Paso 6: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Estructura["Estructura"]
    Declaracion1 --> NotaDeclaracion["Nota: Definir variables"]
    Condicion --> NotaCondicion["Nota: Evaluar lógica"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Declaracion1 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Declaracion2 fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log1 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Condicion fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Log2 fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Estructura fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaDeclaracion fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaCondicion fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Declarar["Declarar <br> - Variables: a, b"]
        Declarar --> Operar["Operar <br> - Suma: a + b"]
        Operar --> Mostrar["Mostrar <br> - Resultado: 15"]
        Mostrar --> Evaluar["Evaluar <br> - If: a < b"]
        Evaluar --> Ejecutar["Ejecutar <br> - Log: mensaje"]
        Inicio --> Desarrollador(("Desarrollador")) --> Fundamento["Fundamento"]
        Declarar --> Componente["Componente <br> - Statements básicos"]
        Componente --> NotaTerminacion["Nota: Usar ; para terminar"]
        Evaluar --> NotaLogica["Nota: Control de flujo"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Declarar fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Operar fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Mostrar fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Evaluar fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Ejecutar fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fundamento fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Componente fill:#9ACD32,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaTerminacion fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaLogica fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Declaración de variables simples para almacenar datos básicos.
2. Ejecución de operaciones matemáticas y visualización de resultados.
3. Uso de condicionales para controlar el flujo del programa.
4. Terminación adecuada de statements para evitar errores de sintaxis.
5. Construcción de lógica básica en scripts iniciales.

### Errores comunes
1. Olvidar el punto y coma al final de un statement.
2. Mezclar statements sin terminación adecuada causando ambigüedad.
3. Ignorar el scope en bloques de código condicionales.
4. No probar statements individuales para verificar ejecución.
5. Usar terminación inconsistente en equipos de desarrollo.

### Recomendaciones
1. Siempre termina statements con punto y coma para claridad.
2. Prueba cada statement de forma aislada durante el desarrollo.
3. Mantén consistencia en la terminación a lo largo del código.
4. Usa linters para detectar problemas de terminación automáticamente.
5. Entiende las reglas de ASI pero no dependas exclusivamente de ellas.