## 4.3.8. IIFE y semicolons

### Introducción
IIFE requieren ; previo para evitar errores.  
Sin ;, parser une con statement anterior.  
Causa SyntaxError.  
Común en script concatenation.  
Afecta modularidad.  
¿Usas ; antes de IIFE?

### Código de Ejemplo
```javascript
// Sin ;
let x = 1
(function() {
  console.log('IIFE');
})();  // SyntaxError: Unexpected token '('

// Con ;
let y = 2;
(function() {
  console.log('IIFE OK');
})();  // OK, log 'IIFE OK'

// Otro caso
console.log('Hola')
(function() {}());  // Error

// Con async IIFE
(async function() {
  await something();
})();  // Necesita ; previo si no standalone
```
### Resultados en Consola
- SyntaxError sin ;
- Ejecución correcta con ;
- Log de IIFE
- Errores en concatenation

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> PrevStmt["Paso 1: Statement previo <br> - Sin ;?"]
    PrevStmt -->|Sí| ParseError["Paso 2: Parse error <br> - Une con IIFE"]
    PrevStmt -->|No| ExecIIFE["Paso 3: Ejecuta IIFE <br> - Código runs"]
    ParseError --> Debug["Paso 4: Debug <br> - SyntaxError"]
    Debug --> AddSemi["Paso 5: Añadir ; <br> - Antes IIFE"]
    AddSemi --> CorrectExec["Paso 6: Ejecución correcta <br> - IIFE works"]
    CorrectExec --> Fin["Paso 7: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> BestPractice["Siempre ; antes IIFE"]
    PrevStmt --> NotaSemi["Nota: ; separa statements"]
    ParseError --> NotaError["Nota: Common en bundles"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style PrevStmt fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ParseError fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ExecIIFE fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Debug fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style AddSemi fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CorrectExec fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style BestPractice fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaSemi fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaError fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Code["Código <br> - Statement + IIFE"]
        Code --> CheckSemi["Check ; <br> - Entre ellos?"]
        CheckSemi -->|No| Error["Error <br> - SyntaxError"]
        CheckSemi -->|Sí| Success["Éxito <br> - IIFE ejecuta"]
        Error --> Fix["Fix <br> - Añadir ;"]
        Fix --> Success
        Inicio --> Desarrollador(("Desarrollador")) --> Bundle["En bundles <br> - Cuidado concatenation"]
        CheckSemi --> NotaCheck["Nota: ; requerido"]
        Error --> NotaCommon["Nota: Error frecuente"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Code fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CheckSemi fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Error fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Success fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fix fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Bundle fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaCheck fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaCommon fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Modular code en browsers.
2. Encapsular variables.
3. Async IIFE en scripts.
4. En bundles como Webpack.
5. Legacy code concatenation.

### Errores comunes
1. Olvidar ; antes (.
2. No test en minified.
3. Ignorar ASI en IIFE.
4. Multi IIFE sin ;.
5. En arrow functions.

### Recomendaciones
1. Siempre ; antes IIFE.
2. Usa linters para detect.
3. Test concatenated code.
4. Entiende ASI rules.
5. Usa modules ES6.