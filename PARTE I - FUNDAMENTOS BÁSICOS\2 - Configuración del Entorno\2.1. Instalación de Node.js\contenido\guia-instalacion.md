# Guía Completa de Instalación de Node.js

## **📋 Tabla de Contenidos**

1. [¿Qué es Node.js?](#qué-es-nodejs)
2. [Métodos de Instalación](#métodos-de-instalación)
3. [Instalación por Sistema Operativo](#instalación-por-sistema-operativo)
4. [Verificación de Instalación](#verificación-de-instalación)
5. [Gestión de Versiones](#gestión-de-versiones)
6. [Configuración Inicial](#configuración-inicial)
7. [Solución de Problemas](#solución-de-problemas)

---

## **🤔 ¿Qué es Node.js?**

**Node.js** es un runtime de JavaScript construido sobre el motor V8 de Chrome que permite ejecutar JavaScript en el servidor. Incluye:

### **Componentes Principales**
- **Node.js Runtime**: Ejecuta JavaScript fuera del navegador
- **npm (Node Package Manager)**: Gestor de paquetes más grande del mundo
- **V8 Engine**: Motor de JavaScript de Google Chrome
- **libuv**: Librería para operaciones asíncronas

### **¿Por qué Node.js?**
- ✅ **JavaScript en el servidor**: Un solo lenguaje para frontend y backend
- ✅ **Ecosistema masivo**: Millones de paquetes en npm
- ✅ **Performance**: Event-driven, non-blocking I/O
- ✅ **Comunidad activa**: Soporte y actualizaciones constantes

---

## **📥 Métodos de Instalación**

### **1. Instalador Oficial (Recomendado para Principiantes)**
- ✅ Fácil de usar
- ✅ Incluye npm automáticamente
- ✅ Configuración automática del PATH
- ❌ Solo una versión a la vez

### **2. Gestores de Versiones (Recomendado para Desarrolladores)**
- ✅ Múltiples versiones de Node.js
- ✅ Cambio rápido entre versiones
- ✅ Ideal para proyectos diferentes
- ❌ Configuración inicial más compleja

### **3. Gestores de Paquetes del Sistema**
- ✅ Integración con el sistema
- ✅ Actualizaciones automáticas
- ❌ Versiones pueden estar desactualizadas

---

## **💻 Instalación por Sistema Operativo**

### **🪟 Windows**

#### **Método 1: Instalador Oficial**

1. **Descargar Node.js**
   ```
   Visita: https://nodejs.org/
   Descarga: LTS (Long Term Support) - Versión recomendada
   ```

2. **Ejecutar Instalador**
   - Doble clic en el archivo `.msi` descargado
   - Seguir el asistente de instalación
   - ✅ Marcar "Add to PATH" (viene marcado por defecto)
   - ✅ Marcar "Install npm package manager"

3. **Verificar Instalación**
   ```cmd
   # Abrir Command Prompt o PowerShell
   node --version
   npm --version
   ```

#### **Método 2: Chocolatey (Gestor de Paquetes)**

```powershell
# Instalar Chocolatey primero (si no lo tienes)
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Instalar Node.js
choco install nodejs

# Verificar
node --version
npm --version
```

#### **Método 3: nvm-windows (Gestor de Versiones)**

```cmd
# Descargar nvm-windows desde:
# https://github.com/coreybutler/nvm-windows/releases

# Después de instalar nvm-windows:
nvm install latest
nvm use latest
nvm list
```

### **🍎 macOS**

#### **Método 1: Instalador Oficial**

1. **Descargar desde nodejs.org**
   ```
   Visita: https://nodejs.org/
   Descarga: LTS para macOS
   ```

2. **Instalar**
   - Doble clic en el archivo `.pkg`
   - Seguir el asistente
   - Introducir contraseña de administrador

#### **Método 2: Homebrew (Recomendado)**

```bash
# Instalar Homebrew (si no lo tienes)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Instalar Node.js
brew install node

# Verificar
node --version
npm --version
```

#### **Método 3: nvm (Gestor de Versiones)**

```bash
# Instalar nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Reiniciar terminal o ejecutar:
source ~/.bashrc

# Instalar Node.js
nvm install node        # Instala la última versión
nvm install --lts       # Instala la versión LTS
nvm use node           # Usa la última versión
nvm use --lts          # Usa la versión LTS

# Listar versiones
nvm list
```

### **🐧 Linux (Ubuntu/Debian)**

#### **Método 1: Repositorio Oficial de Node.js**

```bash
# Actualizar sistema
sudo apt update

# Instalar curl (si no lo tienes)
sudo apt install curl

# Añadir repositorio oficial de Node.js
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -

# Instalar Node.js
sudo apt-get install -y nodejs

# Verificar
node --version
npm --version
```

#### **Método 2: Snap (Ubuntu)**

```bash
# Instalar Node.js via Snap
sudo snap install node --classic

# Verificar
node --version
npm --version
```

#### **Método 3: nvm (Recomendado para Desarrollo)**

```bash
# Instalar nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Reiniciar terminal
source ~/.bashrc

# Instalar Node.js
nvm install --lts
nvm use --lts

# Verificar
node --version
npm --version
```

---

## **✅ Verificación de Instalación**

### **Comandos de Verificación**

```bash
# Verificar versión de Node.js
node --version
# Salida esperada: v18.17.0 (o similar)

# Verificar versión de npm
npm --version
# Salida esperada: 9.6.7 (o similar)

# Verificar ubicación de instalación
which node     # Linux/macOS
where node     # Windows

# Información detallada
node -p "process.versions"
```

### **Prueba Básica de Funcionamiento**

```bash
# Crear archivo de prueba
echo 'console.log("¡Node.js funciona correctamente!");' > test.js

# Ejecutar archivo
node test.js

# Salida esperada: ¡Node.js funciona correctamente!

# Limpiar
rm test.js  # Linux/macOS
del test.js # Windows
```

### **Verificar npm**

```bash
# Verificar configuración de npm
npm config list

# Verificar registro de npm
npm config get registry

# Instalar paquete de prueba
npm install -g cowsay

# Probar paquete
cowsay "¡npm funciona!"

# Desinstalar paquete de prueba
npm uninstall -g cowsay
```

---

## **🔄 Gestión de Versiones**

### **¿Por qué Usar un Gestor de Versiones?**

- **Proyectos diferentes**: Cada proyecto puede requerir una versión específica
- **Testing**: Probar código en múltiples versiones
- **Compatibilidad**: Mantener compatibilidad con versiones específicas
- **Actualizaciones**: Probar nuevas versiones sin afectar proyectos existentes

### **nvm (Linux/macOS)**

```bash
# Instalar versión específica
nvm install 16.20.0
nvm install 18.17.0
nvm install 20.5.0

# Listar versiones instaladas
nvm list

# Cambiar versión
nvm use 18.17.0

# Establecer versión por defecto
nvm alias default 18.17.0

# Usar versión LTS más reciente
nvm use --lts

# Desinstalar versión
nvm uninstall 16.20.0
```

### **nvm-windows (Windows)**

```cmd
# Instalar versión específica
nvm install 18.17.0

# Listar versiones disponibles
nvm list available

# Listar versiones instaladas
nvm list

# Usar versión específica
nvm use 18.17.0

# Desinstalar versión
nvm uninstall 18.17.0
```

### **Archivo .nvmrc**

```bash
# Crear archivo .nvmrc en la raíz del proyecto
echo "18.17.0" > .nvmrc

# Usar versión especificada en .nvmrc
nvm use

# Instalar versión si no existe
nvm install
```

---

## **⚙️ Configuración Inicial**

### **Configurar npm**

```bash
# Configurar información personal
npm config set init-author-name "Tu Nombre"
npm config set init-author-email "<EMAIL>"
npm config set init-license "MIT"

# Configurar registro (opcional)
npm config set registry https://registry.npmjs.org/

# Ver toda la configuración
npm config list

# Configurar directorio global (opcional)
npm config set prefix "~/.npm-global"
```

### **Configurar PATH para Paquetes Globales**

#### **Linux/macOS**
```bash
# Añadir al ~/.bashrc o ~/.zshrc
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

#### **Windows**
```cmd
# Añadir al PATH del sistema:
# %USERPROFILE%\.npm-global
```

### **Configurar Proxy (Si es Necesario)**

```bash
# Configurar proxy HTTP
npm config set proxy http://proxy.company.com:8080
npm config set https-proxy http://proxy.company.com:8080

# Configurar autenticación de proxy
npm config set proxy http://username:<EMAIL>:8080

# Eliminar configuración de proxy
npm config delete proxy
npm config delete https-proxy
```

---

## **🔧 Solución de Problemas**

### **Problemas Comunes**

#### **1. "node: command not found"**

**Causa**: Node.js no está en el PATH

**Solución**:
```bash
# Verificar PATH
echo $PATH  # Linux/macOS
echo %PATH% # Windows

# Reinstalar Node.js asegurándose de marcar "Add to PATH"
# O añadir manualmente al PATH
```

#### **2. "Permission denied" al instalar paquetes globales**

**Causa**: Permisos insuficientes

**Solución Linux/macOS**:
```bash
# Opción 1: Cambiar directorio por defecto de npm
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# Opción 2: Usar nvm (recomendado)
```

**Solución Windows**:
```cmd
# Ejecutar como administrador o usar nvm-windows
```

#### **3. Versiones desactualizadas**

```bash
# Actualizar npm
npm install -g npm@latest

# Verificar versiones
node --version
npm --version

# Actualizar Node.js (reinstalar desde nodejs.org o usar nvm)
```

#### **4. Problemas de SSL/Certificados**

```bash
# Configuración temporal (NO recomendado para producción)
npm config set strict-ssl false

# Solución recomendada: Actualizar certificados
npm config set ca ""
npm config set registry https://registry.npmjs.org/
```

#### **5. Cache corrupto de npm**

```bash
# Limpiar cache de npm
npm cache clean --force

# Verificar cache
npm cache verify
```

### **Comandos de Diagnóstico**

```bash
# Información del sistema
node -p "process.platform"
node -p "process.arch"
node -p "process.version"

# Información de npm
npm doctor

# Verificar configuración
npm config list
npm config get registry

# Verificar permisos
npm config get prefix
ls -la $(npm config get prefix)  # Linux/macOS
```

---

## **🎯 Verificación Final**

### **Checklist de Instalación Exitosa**

- [ ] `node --version` muestra una versión (ej: v18.17.0)
- [ ] `npm --version` muestra una versión (ej: 9.6.7)
- [ ] Puedes ejecutar `node archivo.js`
- [ ] Puedes instalar paquetes con `npm install`
- [ ] No hay errores de permisos
- [ ] El PATH está configurado correctamente

### **Próximos Pasos**

1. **Instalar VS Code** (siguiente tema)
2. **Configurar extensiones esenciales**
3. **Crear tu primer proyecto**
4. **Aprender comandos npm básicos**

---

**¡Felicitaciones! Node.js está instalado y listo para usar.** 🎉

**Tip**: Guarda esta guía como referencia para futuras instalaciones o cuando ayudes a otros desarrolladores.
