<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .code { font-family: 'Courier New', monospace; font-size: 11px; fill: #e74c3c; }
      .callstack { fill: #ff6b6b; stroke: #c0392b; stroke-width: 2; }
      .heap { fill: #4ecdc4; stroke: #16a085; stroke-width: 2; }
      .webapi { fill: #45b7d1; stroke: #2980b9; stroke-width: 2; }
      .microtask { fill: #96ceb4; stroke: #27ae60; stroke-width: 2; }
      .macrotask { fill: #feca57; stroke: #f39c12; stroke-width: 2; }
      .eventloop { fill: #ff9ff3; stroke: #8e44ad; stroke-width: 3; }
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title">JavaScript Event Loop - Arquitectura Completa Ultra Detallada</text>
  
  <!-- JavaScript Engine -->
  <rect x="50" y="60" width="500" height="400" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
  <text x="300" y="85" text-anchor="middle" class="subtitle">🚀 JAVASCRIPT ENGINE (V8/SpiderMonkey)</text>
  
  <!-- Call Stack -->
  <rect x="70" y="100" width="220" height="320" class="callstack" rx="5"/>
  <text x="180" y="125" text-anchor="middle" class="subtitle">📚 CALL STACK (LIFO)</text>
  
  <!-- Stack Frames -->
  <rect x="80" y="140" width="200" height="60" fill="#ffffff" stroke="#c0392b" stroke-width="1" rx="3"/>
  <text x="90" y="155" class="text">Frame 3: processData()</text>
  <text x="90" y="170" class="small-text">Variables: {data, result, index}</text>
  <text x="90" y="185" class="small-text">Scope: Function | Line: 28</text>
  <text x="90" y="195" class="code">return data.map(item => item * 2)</text>
  
  <rect x="80" y="210" width="200" height="60" fill="#ffffff" stroke="#c0392b" stroke-width="1" rx="3"/>
  <text x="90" y="225" class="text">Frame 2: fetchData()</text>
  <text x="90" y="240" class="small-text">Variables: {url, options, promise}</text>
  <text x="90" y="255" class="small-text">Scope: Function | Line: 15</text>
  <text x="90" y="265" class="code">await fetch('/api/data')</text>
  
  <rect x="80" y="280" width="200" height="60" fill="#ffffff" stroke="#c0392b" stroke-width="1" rx="3"/>
  <text x="90" y="295" class="text">Frame 1: main()</text>
  <text x="90" y="310" class="small-text">Variables: {app, config}</text>
  <text x="90" y="325" class="small-text">Scope: Global | Line: 1</text>
  <text x="90" y="335" class="code">console.log('App started')</text>
  
  <rect x="80" y="350" width="200" height="60" fill="#ffffff" stroke="#c0392b" stroke-width="1" rx="3"/>
  <text x="90" y="365" class="text">Frame 0: Global Execution</text>
  <text x="90" y="380" class="small-text">Variables: {window, document}</text>
  <text x="90" y="395" class="small-text">Scope: Global | Line: 0</text>
  <text x="90" y="405" class="code">// Script execution context</text>
  
  <!-- Heap Memory -->
  <rect x="310" y="100" width="220" height="320" class="heap" rx="5"/>
  <text x="420" y="125" text-anchor="middle" class="subtitle">🧠 HEAP MEMORY</text>
  
  <!-- Objects in Heap -->
  <rect x="320" y="140" width="200" height="80" fill="#ffffff" stroke="#16a085" stroke-width="1" rx="3"/>
  <text x="330" y="155" class="text">📦 Objects & Arrays</text>
  <text x="330" y="170" class="small-text">User: {name: "John", age: 30}</text>
  <text x="330" y="185" class="small-text">Array: [1, 2, 3, 4, 5]</text>
  <text x="330" y="200" class="small-text">Function: callback() {...}</text>
  <text x="330" y="215" class="code">Memory Address: 0x7fff5fbff710</text>
  
  <rect x="320" y="230" width="200" height="80" fill="#ffffff" stroke="#16a085" stroke-width="1" rx="3"/>
  <text x="330" y="245" class="text">🔗 Closures & Scope</text>
  <text x="330" y="260" class="small-text">Lexical Environment Chain</text>
  <text x="330" y="275" class="small-text">Variable References: 15</text>
  <text x="330" y="290" class="small-text">Outer Environment: Global</text>
  <text x="330" y="305" class="code">[[Scope]]: closure_scope</text>
  
  <rect x="320" y="320" width="200" height="90" fill="#ffffff" stroke="#16a085" stroke-width="1" rx="3"/>
  <text x="330" y="335" class="text">🗑️ Garbage Collection</text>
  <text x="330" y="350" class="small-text">Algorithm: Mark & Sweep</text>
  <text x="330" y="365" class="small-text">Heap Size: 45.2 MB</text>
  <text x="330" y="380" class="small-text">Used: 32.1 MB | Free: 13.1 MB</text>
  <text x="330" y="395" class="small-text">GC Cycles: 1,247</text>
  <text x="330" y="405" class="code">Next GC: ~2.3s</text>
  
  <!-- Web APIs -->
  <rect x="580" y="60" width="280" height="400" class="webapi" rx="10"/>
  <text x="720" y="85" text-anchor="middle" class="subtitle">🌐 WEB APIs (Browser/Node.js)</text>
  
  <!-- DOM API -->
  <rect x="590" y="100" width="260" height="70" fill="#ffffff" stroke="#2980b9" stroke-width="1" rx="3"/>
  <text x="600" y="115" class="text">🏠 DOM API</text>
  <text x="600" y="130" class="small-text">document.getElementById('app')</text>
  <text x="600" y="145" class="small-text">addEventListener('click', handler)</text>
  <text x="600" y="160" class="code">Status: 3 listeners active</text>
  
  <!-- Timer API -->
  <rect x="590" y="180" width="260" height="70" fill="#ffffff" stroke="#2980b9" stroke-width="1" rx="3"/>
  <text x="600" y="195" class="text">⏰ Timer API</text>
  <text x="600" y="210" class="small-text">setTimeout(callback, 1000)</text>
  <text x="600" y="225" class="small-text">setInterval(callback, 500)</text>
  <text x="600" y="240" class="code">Active Timers: 2 | Next: 847ms</text>
  
  <!-- Fetch API -->
  <rect x="590" y="260" width="260" height="70" fill="#ffffff" stroke="#2980b9" stroke-width="1" rx="3"/>
  <text x="600" y="275" class="text">🌍 Fetch API</text>
  <text x="600" y="290" class="small-text">fetch('/api/users').then(...)</text>
  <text x="600" y="305" class="small-text">HTTP Status: 200 OK</text>
  <text x="600" y="320" class="code">Response: 1.2KB | Time: 234ms</text>
  
  <!-- Other APIs -->
  <rect x="590" y="340" width="260" height="70" fill="#ffffff" stroke="#2980b9" stroke-width="1" rx="3"/>
  <text x="600" y="355" class="text">📍 Other APIs</text>
  <text x="600" y="370" class="small-text">Geolocation, WebSocket, Storage</text>
  <text x="600" y="385" class="small-text">IndexedDB, Canvas, WebGL</text>
  <text x="600" y="400" class="code">Available APIs: 47</text>
  
  <!-- Event Loop -->
  <ellipse cx="700" cy="520" rx="120" ry="60" class="eventloop"/>
  <text x="700" y="510" text-anchor="middle" class="subtitle">🔄 EVENT LOOP</text>
  <text x="700" y="525" text-anchor="middle" class="small-text">while(true) {</text>
  <text x="700" y="540" text-anchor="middle" class="small-text">checkCallStack()</text>
  <text x="700" y="555" text-anchor="middle" class="small-text">processMicrotasks()</text>
  <text x="700" y="570" text-anchor="middle" class="small-text">}</text>
  
  <!-- Task Queues -->
  <rect x="50" y="600" width="1300" height="350" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
  <text x="700" y="625" text-anchor="middle" class="subtitle">📋 TASK QUEUES - Detailed Priority System</text>
  
  <!-- Microtask Queue -->
  <rect x="70" y="650" width="400" height="280" class="microtask" rx="5"/>
  <text x="270" y="675" text-anchor="middle" class="subtitle">🚀 MICROTASK QUEUE (Priority: 1)</text>
  
  <rect x="80" y="690" width="380" height="50" fill="#ffffff" stroke="#27ae60" stroke-width="1" rx="3"/>
  <text x="90" y="705" class="text">Promise.resolve().then(callback)</text>
  <text x="90" y="720" class="small-text">Priority: 1 | Status: Ready | ID: #mt001</text>
  <text x="90" y="735" class="code">Execution Time: ~0.1ms</text>
  
  <rect x="80" y="750" width="380" height="50" fill="#ffffff" stroke="#27ae60" stroke-width="1" rx="3"/>
  <text x="90" y="765" class="text">queueMicrotask(processData)</text>
  <text x="90" y="780" class="small-text">Priority: 1 | Status: Pending | ID: #mt002</text>
  <text x="90" y="795" class="code">Queued: 1.2ms ago</text>
  
  <rect x="80" y="810" width="380" height="50" fill="#ffffff" stroke="#27ae60" stroke-width="1" rx="3"/>
  <text x="90" y="825" class="text">MutationObserver callback</text>
  <text x="90" y="840" class="small-text">Priority: 1 | Status: Waiting | ID: #mt003</text>
  <text x="90" y="855" class="code">DOM Changes: 3 mutations</text>
  
  <rect x="80" y="870" width="380" height="50" fill="#ffffff" stroke="#27ae60" stroke-width="1" rx="3"/>
  <text x="90" y="885" class="text">async/await continuation</text>
  <text x="90" y="900" class="small-text">Priority: 1 | Status: Ready | ID: #mt004</text>
  <text x="90" y="915" class="code">Function: fetchUserData()</text>
  
  <!-- Macrotask Queue -->
  <rect x="490" y="650" width="400" height="280" class="macrotask" rx="5"/>
  <text x="690" y="675" text-anchor="middle" class="subtitle">⏳ MACROTASK QUEUE (Priority: 2)</text>
  
  <rect x="500" y="690" width="380" height="50" fill="#ffffff" stroke="#f39c12" stroke-width="1" rx="3"/>
  <text x="510" y="705" class="text">setTimeout callback</text>
  <text x="510" y="720" class="small-text">Delay: 1000ms | Remaining: 234ms | ID: #t001</text>
  <text x="510" y="735" class="code">Function: updateUI()</text>
  
  <rect x="500" y="750" width="380" height="50" fill="#ffffff" stroke="#f39c12" stroke-width="1" rx="3"/>
  <text x="510" y="765" class="text">DOM Event: click</text>
  <text x="510" y="780" class="small-text">Element: button#submit | ID: #e001</text>
  <text x="510" y="795" class="code">Handler: submitForm()</text>
  
  <rect x="500" y="810" width="380" height="50" fill="#ffffff" stroke="#f39c12" stroke-width="1" rx="3"/>
  <text x="510" y="825" class="text">setInterval callback</text>
  <text x="510" y="840" class="small-text">Interval: 500ms | Next: 127ms | ID: #i001</text>
  <text x="510" y="855" class="code">Function: heartbeat()</text>
  
  <rect x="500" y="870" width="380" height="50" fill="#ffffff" stroke="#f39c12" stroke-width="1" rx="3"/>
  <text x="510" y="885" class="text">I/O Operation Complete</text>
  <text x="510" y="900" class="small-text">Type: File Read | Size: 2.4KB | ID: #io001</text>
  <text x="510" y="915" class="code">Callback: processFile()</text>
  
  <!-- Render Queue -->
  <rect x="910" y="650" width="400" height="280" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="5"/>
  <text x="1110" y="675" text-anchor="middle" class="subtitle">🎨 RENDER QUEUE (60 FPS)</text>
  
  <rect x="920" y="690" width="380" height="50" fill="#ffffff" stroke="#3498db" stroke-width="1" rx="3"/>
  <text x="930" y="705" class="text">requestAnimationFrame</text>
  <text x="930" y="720" class="small-text">Frame: 1247 | FPS: 59.8 | ID: #raf001</text>
  <text x="930" y="735" class="code">Callback: animateElement()</text>
  
  <rect x="920" y="750" width="380" height="50" fill="#ffffff" stroke="#3498db" stroke-width="1" rx="3"/>
  <text x="930" y="765" class="text">Style Recalculation</text>
  <text x="930" y="780" class="small-text">Elements: 47 | Time: 2.3ms</text>
  <text x="930" y="795" class="code">Trigger: CSS class change</text>
  
  <rect x="920" y="810" width="380" height="50" fill="#ffffff" stroke="#3498db" stroke-width="1" rx="3"/>
  <text x="930" y="825" class="text">Layout (Reflow)</text>
  <text x="930" y="840" class="small-text">Affected: 12 elements | Time: 1.8ms</text>
  <text x="930" y="855" class="code">Trigger: DOM modification</text>
  
  <rect x="920" y="870" width="380" height="50" fill="#ffffff" stroke="#3498db" stroke-width="1" rx="3"/>
  <text x="930" y="885" class="text">Paint & Composite</text>
  <text x="930" y="900" class="small-text">Layers: 3 | GPU: Enabled | Time: 0.9ms</text>
  <text x="930" y="915" class="code">Total Frame Time: 5.0ms</text>
  
  <!-- Arrows showing flow -->
  <path d="M 530 300 Q 600 350 650 400" class="arrow"/>
  <path d="M 650 450 Q 650 500 650 480" class="arrow"/>
  <path d="M 620 520 Q 400 580 270 650" class="arrow"/>
  <path d="M 780 520 Q 900 580 690 650" class="arrow"/>
  <path d="M 820 520 Q 1000 580 1110 650" class="arrow"/>
  
  <!-- Performance Metrics -->
  <rect x="1050" y="100" width="300" height="200" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
  <text x="1200" y="125" text-anchor="middle" class="subtitle">📊 PERFORMANCE METRICS</text>
  <text x="1060" y="145" class="small-text">Call Stack Depth: 4/10000</text>
  <text x="1060" y="160" class="small-text">Heap Usage: 32.1MB/64MB</text>
  <text x="1060" y="175" class="small-text">Microtasks Pending: 4</text>
  <text x="1060" y="190" class="small-text">Macrotasks Pending: 4</text>
  <text x="1060" y="205" class="small-text">Event Loop Lag: 0.2ms</text>
  <text x="1060" y="220" class="small-text">FPS: 59.8 (Target: 60)</text>
  <text x="1060" y="235" class="small-text">Main Thread: 23% busy</text>
  <text x="1060" y="250" class="small-text">GC Pressure: Low</text>
  <text x="1060" y="265" class="small-text">Memory Leaks: 0 detected</text>
  <text x="1060" y="280" class="code">Status: ✅ Healthy</text>
</svg>
