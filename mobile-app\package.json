{"name": "codecraft-academy-mobile", "version": "1.0.0", "description": "CodeCraft Academy - Aplicación móvil para aprendizaje de programación", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace CodeCraftAcademy.xcworkspace -scheme CodeCraftAcademy -configuration Release -destination generic/platform=iOS -archivePath CodeCraftAcademy.xcarchive archive", "bundle:android": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res", "bundle:ios": "react-native bundle --platform ios --dev false --entry-file index.js --bundle-output ios/main.jsbundle --assets-dest ios", "clean": "react-native clean", "clean:android": "cd android && ./gradlew clean", "clean:ios": "cd ios && xcodebuild clean", "pods": "cd ios && pod install", "reset": "npx react-native start --reset-cache", "flipper": "npx flipper", "adb": "adb reverse tcp:3000 tcp:3000 && adb reverse tcp:8081 tcp:8081", "release:android": "cd android && ./gradlew bundleRelease", "release:ios": "cd ios && fastlane release"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.19.3", "@react-native-community/netinfo": "^9.4.1", "@react-native-masked-view/masked-view": "^0.2.9", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/drawer": "^6.6.3", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "@react-navigation/stack": "^6.3.17", "@reduxjs/toolkit": "^1.9.5", "react": "18.2.0", "react-native": "0.72.4", "react-native-animatable": "^1.3.3", "react-native-code-push": "^8.2.1", "react-native-config": "^1.5.1", "react-native-device-info": "^10.8.0", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.12.1", "react-native-get-random-values": "^1.9.0", "react-native-keychain": "^8.1.2", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^2.10.1", "react-native-modal": "^13.0.1", "react-native-orientation-locker": "^1.5.0", "react-native-paper": "^5.10.1", "react-native-permissions": "^3.8.4", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.4.2", "react-native-safe-area-context": "^4.7.2", "react-native-screens": "^3.24.0", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^13.13.0", "react-native-syntax-highlighter": "^2.1.0", "react-native-toast-message": "^2.1.6", "react-native-vector-icons": "^10.0.0", "react-native-video": "^5.2.1", "react-native-webview": "^13.6.0", "react-redux": "^8.1.2", "redux-persist": "^6.0.0", "socket.io-client": "^4.7.2", "react-native-markdown-display": "^7.0.0", "react-native-code-editor": "^0.2.0", "react-native-progress": "^5.0.0", "react-native-chart-kit": "^6.12.0", "react-native-calendars": "^1.1302.0", "react-native-image-picker": "^5.6.0", "react-native-document-picker": "^9.0.1", "react-native-share": "^9.4.1", "react-native-rate": "^1.2.12", "react-native-in-app-review": "^4.3.3", "react-native-biometrics": "^3.0.1", "react-native-camera": "^4.2.1", "react-native-qrcode-scanner": "^1.5.5"}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@babel/runtime": "^7.22.6", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.9", "@tsconfig/react-native": "^3.0.2", "@types/react": "^18.2.19", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.1", "eslint": "^8.46.0", "jest": "^29.6.1", "metro-react-native-babel-preset": "0.76.7", "prettier": "^3.0.0", "react-test-renderer": "18.2.0", "typescript": "^5.1.6", "@types/react-native": "^0.72.2", "detox": "^20.11.3", "flipper-plugin-react-native-performance": "^0.3.0", "reactotron-react-native": "^5.0.3", "reactotron-redux": "^3.1.3"}, "engines": {"node": ">=16", "npm": ">=8"}, "jest": {"preset": "react-native", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "transformIgnorePatterns": ["node_modules/(?!(react-native|@react-native|react-native-.*|@react-navigation|react-redux)/)"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/index.js"]}, "eslintConfig": {"extends": "@react-native", "rules": {"prettier/prettier": "error", "react-native/no-unused-styles": "error", "react-native/split-platform-components": "error", "react-native/no-inline-styles": "warn", "react-native/no-color-literals": "warn"}}, "prettier": {"arrowParens": "avoid", "bracketSameLine": true, "bracketSpacing": false, "singleQuote": true, "trailingComma": "all", "tabWidth": 2, "semi": true}, "repository": {"type": "git", "url": "https://github.com/codecraft-academy/mobile-app.git"}, "keywords": ["react-native", "education", "programming", "learning", "mobile", "ios", "android", "javascript", "coding"], "author": "CodeCraft Academy Team", "license": "MIT", "bugs": {"url": "https://github.com/codecraft-academy/mobile-app/issues"}, "homepage": "https://codecraft-academy.com"}