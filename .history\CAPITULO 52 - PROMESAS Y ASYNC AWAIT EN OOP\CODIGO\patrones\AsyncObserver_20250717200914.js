/**
 * Implementación Avanzada del Patrón Async Observer
 * 
 * Este módulo implementa un sistema completo de observadores asíncronos con características empresariales:
 * - Ejecución paralela de observers con control de concurrencia
 * - Sistema de prioridades para ordenación de ejecución
 * - Timeout y retry automático por observer
 * - Filtrado de eventos con criterios personalizables
 * - Métricas de performance y observabilidad
 * - Manejo robusto de errores con aislamiento de fallos
 * 
 * <AUTHOR> Técnico Superior
 * @version 1.0.0
 */

class AdvancedAsyncObserver {
    constructor(options = {}) {
        // Configuración del sistema de observers
        this.maxConcurrentObservers = options.maxConcurrentObservers || 5;
        this.defaultTimeout = options.defaultTimeout || 30000;
        this.defaultRetries = options.defaultRetries || 2;
        this.enableMetrics = options.enableMetrics !== false;
        
        // Estado interno del sistema
        this.observers = new Map();              // Map<eventName, Array<Observer>>
        this.eventQueue = [];                    // Cola de eventos para procesamiento diferido
        this.isProcessingQueue = false;          // Flag para evitar procesamiento concurrente
        this.activeObservers = new Set();       // Set de observers actualmente ejecutándose
        
        // Sistema de métricas y observabilidad
        this.metrics = {
            totalEvents: 0,                      // Total de eventos emitidos
            totalObserverExecutions: 0,          // Total de ejecuciones de observers
            successfulExecutions: 0,             // Ejecuciones exitosas
            failedExecutions: 0,                 // Ejecuciones fallidas
            averageExecutionTime: 0,             // Tiempo promedio de ejecución
            concurrentPeak: 0,                   // Pico máximo de concurrencia
            eventsByType: new Map()              // Contador por tipo de evento
        };
        
        // Event listeners para el propio observer (meta-observabilidad)
        this.eventListeners = new Map();
        
        // Inicializar procesamiento automático de cola
        this.startQueueProcessing();
    }
    
    // ===== MÉTODOS PRINCIPALES DE SUSCRIPCIÓN Y EMISIÓN =====
    
    /**
     * Suscribe un observer asíncrono a un evento específico
     * 
     * Este método implementa el patrón de suscripción con configuración avanzada:
     * - Prioridades para control de orden de ejecución
     * - Timeouts individuales por observer
     * - Filtros personalizables para eventos
     * - Retry automático con backoff exponencial
     * 
     * @param {string} event - Nombre del evento al cual suscribirse
     * @param {Function} asyncHandler - Función asíncrona que maneja el evento
     * @param {Object} options - Opciones de configuración del observer
     * @param {number} options.priority - Prioridad de ejecución (mayor = primero)
     * @param {number} options.timeout - Timeout en ms para este observer
     * @param {number} options.retries - Número de reintentos en caso de fallo
     * @param {Function} options.filter - Función de filtro para eventos
     * @param {Array<string>} options.tags - Tags para categorización
     * @returns {string} ID único del observer para posterior desuscripción
     */
    async subscribe(event, asyncHandler, options = {}) {
        // Validar parámetros de entrada
        if (typeof event !== 'string' || !event.trim()) {
            throw new Error('El nombre del evento debe ser una cadena no vacía');
        }
        
        if (typeof asyncHandler !== 'function') {
            throw new Error('El handler debe ser una función');
        }
        
        // Lazy initialization del array de observers para este evento
        if (!this.observers.has(event)) {
            this.observers.set(event, []);
        }
        
        // Configuración del observer con valores por defecto inteligentes
        const observer = {
            id: this.generateObserverId(),                              // ID único para tracking
            handler: asyncHandler,                                      // Función handler asíncrona
            priority: options.priority || 0,                           // Prioridad de ejecución
            timeout: options.timeout || this.defaultTimeout,           // Timeout individual
            retries: options.retries || this.defaultRetries,           // Reintentos automáticos
            filter: options.filter || (() => true),                    // Filtro de eventos
            tags: options.tags || [],                                  // Tags para categorización
            createdAt: Date.now(),                                     // Timestamp de creación
            executionCount: 0,                                         // Contador de ejecuciones
            successCount: 0,                                           // Contador de éxitos
            failureCount: 0                                            // Contador de fallos
        };
        
        // Agregar observer al array correspondiente al evento
        this.observers.get(event).push(observer);
        
        // Ordenar observers por prioridad (mayor prioridad primero)
        this.observers.get(event).sort((a, b) => b.priority - a.priority);
        
        // Emitir evento de suscripción para observabilidad
        this.emit('observerSubscribed', {
            observerId: observer.id,
            event,
            priority: observer.priority,
            tags: observer.tags
        });
        
        console.log(`✅ Observer suscrito: ${observer.id} para evento '${event}' con prioridad ${observer.priority}`);
        
        // Retornar ID único para posterior desuscripción
        return observer.id;
    }
    
    /**
     * Desuscribe un observer específico usando su ID
     * 
     * @param {string} observerId - ID del observer a desuscribir
     * @returns {boolean} True si se desuscribió exitosamente
     */
    async unsubscribe(observerId) {
        let found = false;
        
        // Buscar y remover el observer en todos los eventos
        for (const [event, observers] of this.observers) {
            const index = observers.findIndex(obs => obs.id === observerId);
            if (index !== -1) {
                const removedObserver = observers.splice(index, 1)[0];
                found = true;
                
                console.log(`🗑️ Observer desuscrito: ${observerId} del evento '${event}'`);
                
                // Emitir evento de desuscripción
                this.emit('observerUnsubscribed', {
                    observerId,
                    event,
                    executionCount: removedObserver.executionCount,
                    successCount: removedObserver.successCount,
                    failureCount: removedObserver.failureCount
                });
                
                break;
            }
        }
        
        return found;
    }
    
    /**
     * Emite un evento de forma asíncrona a todos los observers suscritos
     * 
     * Este método implementa la emisión de eventos con las siguientes características:
     * - Ejecución paralela de observers con control de concurrencia
     * - Aislamiento de fallos (un observer que falla no afecta a otros)
     * - Filtrado de eventos según criterios de cada observer
     * - Métricas detalladas de performance y éxito/fallo
     * 
     * @param {string} event - Nombre del evento a emitir
     * @param {*} data - Datos del evento a pasar a los observers
     * @param {Object} options - Opciones de emisión
     * @param {boolean} options.immediate - Si ejecutar inmediatamente o encolar
     * @param {number} options.timeout - Timeout global para esta emisión
     * @returns {Promise<Object>} Resultado detallado de la emisión
     */
    async emit(event, data, options = {}) {
        const startTime = Date.now();
        const eventId = this.generateEventId();
        
        // Crear objeto de evento con metadatos
        const eventData = {
            id: eventId,
            event,
            data,
            timestamp: startTime,
            options
        };
        
        // Actualizar métricas de eventos
        this.metrics.totalEvents++;
        this.updateEventTypeMetrics(event);
        
        console.log(`📢 Emitiendo evento '${event}' con ID ${eventId}`);
        
        // Decidir si ejecutar inmediatamente o encolar
        if (options.immediate !== false) {
            return await this.processEvent(eventData);
        } else {
            this.eventQueue.push(eventData);
            this.processQueue(); // Procesamiento asíncrono
            return { queued: true, eventId };
        }
    }
    
    /**
     * Procesa un evento individual ejecutando todos sus observers
     * 
     * @param {Object} eventData - Datos completos del evento
     * @returns {Promise<Object>} Resultado detallado del procesamiento
     */
    async processEvent(eventData) {
        const startTime = Date.now();
        const observers = this.observers.get(eventData.event) || [];
        
        if (observers.length === 0) {
            console.log(`⚠️ No hay observers para el evento '${eventData.event}'`);
            return {
                eventId: eventData.id,
                event: eventData.event,
                observersExecuted: 0,
                results: [],
                executionTime: Date.now() - startTime
            };
        }
        
        // Filtrar observers según sus criterios
        const filteredObservers = observers.filter(observer => {
            try {
                return observer.filter(eventData.data);
            } catch (error) {
                console.error(`❌ Error en filtro del observer ${observer.id}:`, error.message);
                return false; // Excluir observer con filtro defectuoso
            }
        });
        
        console.log(`🔍 ${filteredObservers.length} de ${observers.length} observers pasaron el filtro`);
        
        // Procesar observers en lotes según concurrencia máxima
        const results = [];
        for (let i = 0; i < filteredObservers.length; i += this.maxConcurrentObservers) {
            const batch = filteredObservers.slice(i, i + this.maxConcurrentObservers);
            
            console.log(`⚡ Procesando lote ${Math.floor(i / this.maxConcurrentObservers) + 1} con ${batch.length} observers`);
            
            // Ejecutar lote en paralelo con Promise.allSettled
            const batchResults = await Promise.allSettled(
                batch.map(observer => this.executeObserver(observer, eventData))
            );
            
            results.push(...batchResults);
        }
        
        // Analizar resultados
        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;
        const executionTime = Date.now() - startTime;
        
        // Actualizar métricas globales
        this.updateExecutionMetrics(successful, failed, executionTime);
        
        console.log(`📊 Evento '${eventData.event}' procesado: ${successful} exitosos, ${failed} fallidos en ${executionTime}ms`);
        
        // Emitir evento de finalización para observabilidad
        this.emit('eventProcessed', {
            eventId: eventData.id,
            event: eventData.event,
            successful,
            failed,
            executionTime
        });
        
        return {
            eventId: eventData.id,
            event: eventData.event,
            observersExecuted: results.length,
            successful,
            failed,
            results,
            executionTime
        };
    }
    
    /**
     * Ejecuta un observer individual con timeout y retry automático
     * 
     * @param {Object} observer - Observer a ejecutar
     * @param {Object} eventData - Datos del evento
     * @returns {Promise<*>} Resultado de la ejecución del observer
     */
    async executeObserver(observer, eventData) {
        const startTime = Date.now();
        let attempts = 0;
        
        // Agregar a set de observers activos
        this.activeObservers.add(observer.id);
        
        // Actualizar pico de concurrencia
        if (this.activeObservers.size > this.metrics.concurrentPeak) {
            this.metrics.concurrentPeak = this.activeObservers.size;
        }
        
        try {
            // Intentar ejecución con retry automático
            while (attempts <= observer.retries) {
                attempts++;
                observer.executionCount++;
                
                try {
                    console.log(`🔄 Ejecutando observer ${observer.id} (intento ${attempts}/${observer.retries + 1})`);
                    
                    // Crear promesa de timeout
                    const timeoutPromise = new Promise((_, reject) => {
                        setTimeout(() => {
                            reject(new Error(`Observer ${observer.id} timeout después de ${observer.timeout}ms`));
                        }, observer.timeout);
                    });
                    
                    // Crear promesa de ejecución del handler
                    const handlerPromise = observer.handler(eventData.data, eventData);
                    
                    // Race entre handler y timeout
                    const result = await Promise.race([handlerPromise, timeoutPromise]);
                    
                    // Éxito: actualizar métricas y retornar
                    observer.successCount++;
                    const executionTime = Date.now() - startTime;
                    
                    console.log(`✅ Observer ${observer.id} ejecutado exitosamente en ${executionTime}ms`);
                    
                    return result;
                    
                } catch (error) {
                    // Fallo en este intento
                    console.error(`❌ Observer ${observer.id} falló en intento ${attempts}: ${error.message}`);
                    
                    // Si no es el último intento, esperar antes del retry
                    if (attempts <= observer.retries) {
                        const delay = 1000 * Math.pow(2, attempts - 1); // Backoff exponencial
                        console.log(`⏳ Reintentando observer ${observer.id} en ${delay}ms...`);
                        await this.delay(delay);
                    } else {
                        // Último intento fallido
                        observer.failureCount++;
                        throw error;
                    }
                }
            }
            
        } finally {
            // Remover de set de observers activos
            this.activeObservers.delete(observer.id);
        }
    }
    
    // ===== MÉTODOS DE GESTIÓN DE COLA Y PROCESAMIENTO =====

    /**
     * Inicia el procesamiento automático de la cola de eventos
     */
    startQueueProcessing() {
        // Procesar cola cada 100ms
        setInterval(() => {
            if (!this.isProcessingQueue && this.eventQueue.length > 0) {
                this.processQueue();
            }
        }, 100);
    }

    /**
     * Procesa la cola de eventos de forma asíncrona
     */
    async processQueue() {
        if (this.isProcessingQueue) return;

        this.isProcessingQueue = true;

        try {
            while (this.eventQueue.length > 0) {
                const eventData = this.eventQueue.shift();
                await this.processEvent(eventData);
            }
        } catch (error) {
            console.error('❌ Error procesando cola de eventos:', error.message);
        } finally {
            this.isProcessingQueue = false;
        }
    }

    // ===== MÉTODOS DE MÉTRICAS Y OBSERVABILIDAD =====

    /**
     * Actualiza métricas de ejecución
     *
     * @param {number} successful - Número de ejecuciones exitosas
     * @param {number} failed - Número de ejecuciones fallidas
     * @param {number} executionTime - Tiempo de ejecución en ms
     */
    updateExecutionMetrics(successful, failed, executionTime) {
        this.metrics.totalObserverExecutions += (successful + failed);
        this.metrics.successfulExecutions += successful;
        this.metrics.failedExecutions += failed;

        // Actualizar tiempo promedio de ejecución
        const totalExecutions = this.metrics.totalObserverExecutions;
        const currentAvg = this.metrics.averageExecutionTime;
        this.metrics.averageExecutionTime = ((currentAvg * (totalExecutions - (successful + failed))) + executionTime) / totalExecutions;
    }

    /**
     * Actualiza métricas por tipo de evento
     *
     * @param {string} eventType - Tipo de evento
     */
    updateEventTypeMetrics(eventType) {
        const current = this.metrics.eventsByType.get(eventType) || 0;
        this.metrics.eventsByType.set(eventType, current + 1);
    }

    /**
     * Obtiene métricas actuales del sistema
     *
     * @returns {Object} Métricas detalladas
     */
    getMetrics() {
        const successRate = this.metrics.totalObserverExecutions > 0 ?
            (this.metrics.successfulExecutions / this.metrics.totalObserverExecutions * 100).toFixed(2) + '%' : '0%';

        return {
            ...this.metrics,
            activeObservers: this.activeObservers.size,
            queuedEvents: this.eventQueue.length,
            totalObserverCount: Array.from(this.observers.values()).reduce((sum, arr) => sum + arr.length, 0),
            successRate,
            eventsByType: Object.fromEntries(this.metrics.eventsByType)
        };
    }

    /**
     * Obtiene estadísticas detalladas de observers
     *
     * @returns {Array} Array con estadísticas de cada observer
     */
    getObserverStats() {
        const stats = [];

        for (const [event, observers] of this.observers) {
            for (const observer of observers) {
                const successRate = observer.executionCount > 0 ?
                    (observer.successCount / observer.executionCount * 100).toFixed(2) + '%' : '0%';

                stats.push({
                    id: observer.id,
                    event,
                    priority: observer.priority,
                    tags: observer.tags,
                    executionCount: observer.executionCount,
                    successCount: observer.successCount,
                    failureCount: observer.failureCount,
                    successRate,
                    createdAt: new Date(observer.createdAt).toISOString(),
                    isActive: this.activeObservers.has(observer.id)
                });
            }
        }

        return stats.sort((a, b) => b.executionCount - a.executionCount);
    }

    // ===== MÉTODOS DE UTILIDAD =====

    /**
     * Genera ID único para observer
     *
     * @returns {string} ID único
     */
    generateObserverId() {
        return `obs_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Genera ID único para evento
     *
     * @returns {string} ID único
     */
    generateEventId() {
        return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Crea un delay asíncrono
     *
     * @param {number} ms - Milisegundos de delay
     * @returns {Promise<void>}
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Obtiene todos los eventos registrados
     *
     * @returns {Array<string>} Lista de eventos
     */
    getRegisteredEvents() {
        return Array.from(this.observers.keys());
    }

    /**
     * Obtiene observers para un evento específico
     *
     * @param {string} event - Nombre del evento
     * @returns {Array} Lista de observers
     */
    getObserversForEvent(event) {
        return this.observers.get(event) || [];
    }

    /**
     * Limpia observers inactivos o con muchos fallos
     *
     * @param {Object} criteria - Criterios de limpieza
     * @returns {number} Número de observers removidos
     */
    async cleanup(criteria = {}) {
        const maxFailureRate = criteria.maxFailureRate || 0.8; // 80% de fallos
        const minExecutions = criteria.minExecutions || 10;
        const maxAge = criteria.maxAge || 86400000; // 24 horas

        let removedCount = 0;
        const now = Date.now();

        for (const [event, observers] of this.observers) {
            const toRemove = [];

            for (let i = 0; i < observers.length; i++) {
                const observer = observers[i];
                const age = now - observer.createdAt;
                const failureRate = observer.executionCount > 0 ?
                    observer.failureCount / observer.executionCount : 0;

                // Criterios de limpieza
                const shouldRemove = (
                    (observer.executionCount >= minExecutions && failureRate >= maxFailureRate) ||
                    (age > maxAge && observer.executionCount === 0)
                );

                if (shouldRemove) {
                    toRemove.push(i);
                }
            }

            // Remover en orden inverso para mantener índices
            for (let i = toRemove.length - 1; i >= 0; i--) {
                const removedObserver = observers.splice(toRemove[i], 1)[0];
                removedCount++;

                console.log(`🧹 Observer removido por limpieza: ${removedObserver.id} (${(failureRate * 100).toFixed(1)}% fallos)`);
            }
        }

        console.log(`🧹 Limpieza completada: ${removedCount} observers removidos`);
        return removedCount;
    }

    /**
     * Pausa todos los observers (útil para mantenimiento)
     *
     * @returns {number} Número de observers pausados
     */
    async pauseAll() {
        let pausedCount = 0;

        for (const [event, observers] of this.observers) {
            for (const observer of observers) {
                if (!observer.paused) {
                    observer.paused = true;
                    observer.pausedAt = Date.now();
                    pausedCount++;
                }
            }
        }

        console.log(`⏸️ ${pausedCount} observers pausados`);
        return pausedCount;
    }

    /**
     * Reanuda todos los observers pausados
     *
     * @returns {number} Número de observers reanudados
     */
    async resumeAll() {
        let resumedCount = 0;

        for (const [event, observers] of this.observers) {
            for (const observer of observers) {
                if (observer.paused) {
                    observer.paused = false;
                    delete observer.pausedAt;
                    resumedCount++;
                }
            }
        }

        console.log(`▶️ ${resumedCount} observers reanudados`);
        return resumedCount;
    }

    /**
     * Destructor del sistema de observers
     */
    async destroy() {
        // Pausar procesamiento
        this.isProcessingQueue = true;

        // Limpiar todas las estructuras
        this.observers.clear();
        this.eventQueue = [];
        this.activeObservers.clear();
        this.eventListeners.clear();

        // Reiniciar métricas
        this.metrics = {
            totalEvents: 0,
            totalObserverExecutions: 0,
            successfulExecutions: 0,
            failedExecutions: 0,
            averageExecutionTime: 0,
            concurrentPeak: 0,
            eventsByType: new Map()
        };

        console.log('🗑️ Sistema de observers destruido');
    }
}

module.exports = AdvancedAsyncObserver;
