# Proyectos Prácticos de Promesas y Async/Await en OOP

## Objetivo
Desarrollar aplicaciones prácticas que integren los conceptos y patrones aprendidos en el capítulo.

## Contenido sugerido
- Descripción del proyecto
- Estructura de carpetas y archivos
- Ejemplos de código y explicación
- Referencias cruzadas a teoría y ejemplos

---

> **Cada proyecto debe ser didáctico y demostrar integración de conceptos.** 