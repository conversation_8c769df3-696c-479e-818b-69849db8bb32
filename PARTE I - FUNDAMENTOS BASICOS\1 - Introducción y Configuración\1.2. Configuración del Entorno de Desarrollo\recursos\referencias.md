# **Referencias y Recursos Adicionales**

## **📚 Documentación Oficial**

### **Node.js**
- **[Documentación oficial](https://nodejs.org/docs/)** - Documentación completa y actualizada
- **[API Documentation](https://nodejs.org/api/)** - Referencia de todas las APIs
- **[Guías oficiales](https://nodejs.org/en/docs/guides/)** - Tutoriales y guías paso a paso
- **[Releases](https://nodejs.org/en/about/releases/)** - Información sobre versiones y ciclo de vida

### **npm**
- **[Documentación de npm](https://docs.npmjs.com/)** - Guía completa del gestor de paquetes
- **[CLI Commands](https://docs.npmjs.com/cli/v9/commands)** - Referencia de comandos
- **[Package.json](https://docs.npmjs.com/cli/v9/configuring-npm/package-json)** - Configuración de proyectos

---

## **🛠️ Herramientas de Instalación**

### **Gestores de Versiones**
- **[nvm (Node Version Manager)](https://github.com/nvm-sh/nvm)** - Para Linux/macOS
- **[nvm-windows](https://github.com/coreybutler/nvm-windows)** - Para Windows
- **[n](https://github.com/tj/n)** - Alternativa simple para Linux/macOS
- **[fnm](https://github.com/Schniz/fnm)** - Gestor rápido escrito en Rust

### **Instaladores Alternativos**
- **[Chocolatey](https://chocolatey.org/packages/nodejs)** - Para Windows
- **[Homebrew](https://formulae.brew.sh/formula/node)** - Para macOS
- **[Snap](https://snapcraft.io/node)** - Para Linux
- **[Docker](https://hub.docker.com/_/node)** - Contenedores de Node.js

---

## **📖 Tutoriales y Guías**

### **Tutoriales Oficiales**
- **[Getting Started Guide](https://nodejs.org/en/docs/guides/getting-started-guide/)** - Guía oficial para principiantes
- **[Debugging Guide](https://nodejs.org/en/docs/guides/debugging-getting-started/)** - Cómo hacer debugging
- **[Security Best Practices](https://nodejs.org/en/docs/guides/security/)** - Mejores prácticas de seguridad

### **Tutoriales de la Comunidad**
- **[Node.js Tutorial - W3Schools](https://www.w3schools.com/nodejs/)** - Tutorial básico paso a paso
- **[Learn Node.js - Codecademy](https://www.codecademy.com/learn/learn-node-js)** - Curso interactivo
- **[Node.js Crash Course - YouTube](https://www.youtube.com/results?search_query=nodejs+crash+course)** - Videos tutoriales

---

## **🔧 Herramientas de Desarrollo**

### **Editores de Código**
- **[Visual Studio Code](https://code.visualstudio.com/)** - Editor recomendado
- **[WebStorm](https://www.jetbrains.com/webstorm/)** - IDE profesional
- **[Sublime Text](https://www.sublimetext.com/)** - Editor ligero
- **[Atom](https://atom.io/)** - Editor de GitHub (descontinuado)

### **Extensiones VS Code Recomendadas**
- **[Node.js Extension Pack](https://marketplace.visualstudio.com/items?itemName=waderyan.nodejs-extension-pack)** - Pack completo
- **[npm Intellisense](https://marketplace.visualstudio.com/items?itemName=christian-kohler.npm-intellisense)** - Autocompletado npm
- **[Node.js Modules Intellisense](https://marketplace.visualstudio.com/items?itemName=leizongmin.node-module-intellisense)** - Autocompletado módulos

---

## **📊 Recursos de Aprendizaje**

### **Cursos Online**
- **[Node.js - The Complete Guide (Udemy)](https://www.udemy.com/course/nodejs-the-complete-guide/)** - Curso completo
- **[Node.js Developer Course (Pluralsight)](https://www.pluralsight.com/paths/node-js)** - Ruta de aprendizaje
- **[Introduction to Node.js (edX)](https://www.edx.org/course/introduction-to-nodejs)** - Curso gratuito

### **Libros Recomendados**
- **"Node.js Design Patterns"** por Mario Casciaro - Patrones avanzados
- **"Learning Node.js"** por Marc Wandschneider - Para principiantes
- **"Node.js in Action"** por Manning Publications - Guía práctica

### **Blogs y Artículos**
- **[Node.js Blog](https://nodejs.org/en/blog/)** - Blog oficial
- **[Node.js Best Practices](https://github.com/goldbergyoni/nodebestpractices)** - Mejores prácticas
- **[RisingStack Blog](https://blog.risingstack.com/)** - Artículos avanzados

---

## **🛡️ Seguridad y Mejores Prácticas**

### **Guías de Seguridad**
- **[Node.js Security Checklist](https://blog.risingstack.com/node-js-security-checklist/)** - Lista de verificación
- **[OWASP Node.js Security](https://cheatsheetseries.owasp.org/cheatsheets/Nodejs_Security_Cheat_Sheet.html)** - Guía OWASP
- **[Snyk Node.js Security](https://snyk.io/learn/nodejs-security/)** - Recursos de Snyk

### **Herramientas de Seguridad**
- **[npm audit](https://docs.npmjs.com/cli/v9/commands/npm-audit)** - Auditoría de vulnerabilidades
- **[Snyk](https://snyk.io/)** - Escáner de vulnerabilidades
- **[Node Security Platform](https://nodesecurity.io/)** - Plataforma de seguridad

---

## **🔍 Debugging y Profiling**

### **Herramientas de Debugging**
- **[Node.js Inspector](https://nodejs.org/en/docs/guides/debugging-getting-started/)** - Debugger integrado
- **[Chrome DevTools](https://developers.google.com/web/tools/chrome-devtools)** - Para debugging web
- **[VS Code Debugger](https://code.visualstudio.com/docs/nodejs/nodejs-debugging)** - Debugging en VS Code

### **Herramientas de Profiling**
- **[Node.js Profiler](https://nodejs.org/en/docs/guides/simple-profiling/)** - Profiling básico
- **[Clinic.js](https://clinicjs.org/)** - Suite de profiling
- **[0x](https://github.com/davidmarkclements/0x)** - Flame graphs

---

## **📦 Gestión de Paquetes**

### **Alternativas a npm**
- **[Yarn](https://yarnpkg.com/)** - Gestor de paquetes rápido
- **[pnpm](https://pnpm.io/)** - Gestor eficiente en espacio
- **[Bun](https://bun.sh/)** - Runtime y gestor ultra-rápido

### **Registros de Paquetes**
- **[npmjs.com](https://www.npmjs.com/)** - Registro oficial
- **[GitHub Packages](https://github.com/features/packages)** - Registro de GitHub
- **[Verdaccio](https://verdaccio.org/)** - Registro privado

---

## **🌐 Comunidad y Soporte**

### **Foros y Comunidades**
- **[Node.js GitHub Discussions](https://github.com/nodejs/node/discussions)** - Discusiones oficiales
- **[Stack Overflow](https://stackoverflow.com/questions/tagged/node.js)** - Preguntas y respuestas
- **[Reddit r/node](https://www.reddit.com/r/node/)** - Comunidad en Reddit
- **[Discord Node.js](https://discord.gg/nodejs)** - Chat en tiempo real

### **Eventos y Conferencias**
- **[NodeConf](https://nodeconf.com/)** - Conferencia principal
- **[Node.js Interactive](https://events.linuxfoundation.org/nodejs-interactive/)** - Evento de la fundación
- **[JSConf](https://jsconf.com/)** - Conferencias de JavaScript

---

## **📈 Monitoreo y Performance**

### **Herramientas de Monitoreo**
- **[New Relic](https://newrelic.com/nodejs)** - APM profesional
- **[DataDog](https://www.datadoghq.com/dg/monitor/nodejs/)** - Monitoreo completo
- **[AppDynamics](https://www.appdynamics.com/supported-technologies/nodejs/)** - Monitoreo empresarial

### **Herramientas de Performance**
- **[Artillery](https://artillery.io/)** - Load testing
- **[Autocannon](https://github.com/mcollina/autocannon)** - HTTP benchmarking
- **[Lighthouse CI](https://github.com/GoogleChrome/lighthouse-ci)** - Performance web

---

## **🔗 APIs y Servicios**

### **APIs Populares para Practicar**
- **[JSONPlaceholder](https://jsonplaceholder.typicode.com/)** - API de prueba
- **[httpbin](https://httpbin.org/)** - Testing HTTP requests
- **[ReqRes](https://reqres.in/)** - API REST de prueba
- **[Random User API](https://randomuser.me/)** - Datos de usuarios aleatorios

### **Servicios de Hosting**
- **[Heroku](https://www.heroku.com/)** - Platform as a Service
- **[Vercel](https://vercel.com/)** - Hosting para aplicaciones
- **[Netlify](https://www.netlify.com/)** - Hosting y CI/CD
- **[Railway](https://railway.app/)** - Hosting moderno

---

## **📱 Desarrollo Móvil y Desktop**

### **Frameworks Multiplataforma**
- **[Electron](https://www.electronjs.org/)** - Aplicaciones desktop
- **[React Native](https://reactnative.dev/)** - Aplicaciones móviles
- **[Ionic](https://ionicframework.com/)** - Apps híbridas
- **[NativeScript](https://nativescript.org/)** - Apps nativas

---

## **🎓 Certificaciones**

### **Certificaciones Oficiales**
- **[Node.js Certified Developer](https://nodejs.org/en/certification/)** - Certificación oficial
- **[OpenJS Node.js Application Developer](https://training.linuxfoundation.org/certification/jsnad/)** - Linux Foundation
- **[OpenJS Node.js Services Developer](https://training.linuxfoundation.org/certification/jsnsd/)** - Servicios avanzados

---

## **📋 Checklists y Plantillas**

### **Checklist de Instalación**
```markdown
- [ ] Node.js descargado desde nodejs.org
- [ ] Versión LTS seleccionada
- [ ] Instalador ejecutado con permisos de administrador
- [ ] PATH configurado automáticamente
- [ ] Verificación con `node --version`
- [ ] Verificación con `npm --version`
- [ ] Primer script ejecutado exitosamente
```

### **Plantilla de Proyecto Básico**
```
mi-proyecto/
├── package.json
├── index.js
├── src/
│   ├── app.js
│   └── utils/
├── test/
├── docs/
└── README.md
```

---

## **🆘 Solución de Problemas Comunes**

### **Problemas de Instalación**
- **Error de permisos:** Ejecutar como administrador
- **PATH no configurado:** Reinstalar marcando "Add to PATH"
- **Versión incorrecta:** Desinstalar versiones previas
- **Conflictos de versión:** Usar nvm para gestión

### **Problemas de Ejecución**
- **Módulo no encontrado:** Verificar instalación con npm
- **Puerto en uso:** Cambiar puerto o matar proceso
- **Permisos de archivo:** Verificar permisos de lectura/escritura
- **Memoria insuficiente:** Aumentar límite con --max-old-space-size

---

## **📞 Contacto y Soporte**

### **Soporte del Curso**
- **Email:** <EMAIL>
- **Discord:** [Servidor del curso](https://discord.gg/curso-javascript)
- **GitHub Issues:** [Reportar problemas](https://github.com/curso-javascript/issues)

### **Soporte Oficial de Node.js**
- **GitHub Issues:** [nodejs/node](https://github.com/nodejs/node/issues)
- **Help Repository:** [nodejs/help](https://github.com/nodejs/help)
- **Security:** <EMAIL>

---

**Última actualización:** 15 de enero de 2024  
**Mantenido por:** Equipo del Curso JavaScript Completo  
**Versión:** 1.0
