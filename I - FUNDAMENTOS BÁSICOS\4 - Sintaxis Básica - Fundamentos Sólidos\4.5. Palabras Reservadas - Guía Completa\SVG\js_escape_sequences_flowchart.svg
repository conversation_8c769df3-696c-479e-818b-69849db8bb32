<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2196F3;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="processGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF5722;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="resultGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#673AB7;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="2" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Título -->
  <rect x="10" y="10" width="980" height="60" fill="url(#headerGrad)" rx="10" filter="url(#shadow)"/>
  <text x="500" y="45" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="white">
    Procesamiento de Escape Sequences en JavaScript
  </text>
  
  <!-- Entrada de datos -->
  <rect x="50" y="100" width="200" height="60" fill="#E3F2FD" stroke="#1976D2" stroke-width="2" rx="8" filter="url(#shadow)"/>
  <text x="150" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Texto de Entrada</text>
  <text x="150" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10">con Escape Sequences</text>
  
  <!-- Flecha hacia validación -->
  <path d="M 250 130 L 320 130" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Validación de sintaxis -->
  <rect x="340" y="100" width="160" height="60" fill="#FFF3E0" stroke="#F57C00" stroke-width="2" rx="8" filter="url(#shadow)"/>
  <text x="420" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Validación</text>
  <text x="420" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10">de Sintaxis</text>
  
  <!-- Flecha hacia decisión -->
  <path d="M 500 130 L 570 130" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Decisión: Tipo de escape -->
  <polygon points="650,80 750,130 650,180 550,130" fill="#FFEB3B" stroke="#F57F17" stroke-width="2" filter="url(#shadow)"/>
  <text x="650" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold">Tipo de</text>
  <text x="650" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold">Escape?</text>
  
  <!-- Caracteres de Control -->
  <rect x="50" y="220" width="120" height="80" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="8" filter="url(#shadow)"/>
  <text x="110" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Caracteres</text>
  <text x="110" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold">de Control</text>
  <text x="110" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="9">\n \t \r</text>
  <text x="110" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="9">\b \f \v</text>
  
  <!-- Caracteres Literales -->
  <rect x="190" y="220" width="120" height="80" fill="#FFF3E0" stroke="#FF9800" stroke-width="2" rx="8" filter="url(#shadow)"/>
  <text x="250" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Caracteres</text>
  <text x="250" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Literales</text>
  <text x="250" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="9">\\ \' \"</text>
  <text x="250" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="9">\0</text>
  
  <!-- Escape Hexadecimal -->
  <rect x="330" y="220" width="120" height="80" fill="#F3E5F5" stroke="#9C27B0" stroke-width="2" rx="8" filter="url(#shadow)"/>
  <text x="390" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Escape</text>
  <text x="390" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Hexadecimal</text>
  <text x="390" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="9">\xHH</text>
  <text x="390" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="8">(00-FF)</text>
  
  <!-- Unicode BMP -->
  <rect x="470" y="220" width="120" height="80" fill="#E1F5FE" stroke="#03A9F4" stroke-width="2" rx="8" filter="url(#shadow)"/>
  <text x="530" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Unicode</text>
  <text x="530" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold">BMP</text>
  <text x="530" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="9">\uHHHH</text>
  <text x="530" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="8">(16-bit)</text>
  
  <!-- Unicode Extendido -->
  <rect x="610" y="220" width="120" height="80" fill="#FCE4EC" stroke="#E91E63" stroke-width="2" rx="8" filter="url(#shadow)"/>
  <text x="670" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Unicode</text>
  <text x="670" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Extendido</text>
  <text x="670" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="9">\u{HHHHHH}</text>
  <text x="670" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="8">(32-bit)</text>
  
  <!-- Error de Escape -->
  <rect x="750" y="220" width="120" height="80" fill="#FFEBEE" stroke="#F44336" stroke-width="2" rx="8" filter="url(#shadow)"/>
  <text x="810" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Error de</text>
  <text x="810" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Escape</text>
  <text x="810" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="9">Inválido</text>
  <text x="810" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="8">SyntaxError</text>
  
  <!-- Flechas desde decisión a tipos -->
  <path d="M 600 150 L 110 220" stroke="#4CAF50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="300" y="180" font-family="Arial, sans-serif" font-size="9" fill="#4CAF50">\n, \t, \r</text>
  
  <path d="M 620 150 L 250 220" stroke="#FF9800" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="400" y="180" font-family="Arial, sans-serif" font-size="9" fill="#FF9800">\\ \' \"</text>
  
  <path d="M 650 180 L 390 220" stroke="#9C27B0" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="500" y="200" font-family="Arial, sans-serif" font-size="9" fill="#9C27B0">\xHH</text>
  
  <path d="M 680 150 L 530 220" stroke="#03A9F4" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="600" y="180" font-family="Arial, sans-serif" font-size="9" fill="#03A9F4">\uHHHH</text>
  
  <path d="M 700 150 L 670 220" stroke="#E91E63" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="720" y="180" font-family="Arial, sans-serif" font-size="9" fill="#E91E63">\u{H...}</text>
  
  <path d="M 750 130 L 810 220" stroke="#F44336" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="780" y="170" font-family="Arial, sans-serif" font-size="9" fill="#F44336">Inválido</text>
  
  <!-- Procesamiento -->
  <rect x="50" y="340" width="680" height="60" fill="url(#processGrad)" rx="8" filter="url(#shadow)"/>
  <text x="390" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Procesamiento y Conversión</text>
  <text x="390" y="385" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">String.fromCharCode() | String.fromCodePoint() | Interpretación Directa</text>
  
  <!-- Flechas hacia procesamiento -->
  <path d="M 110 300 L 110 340" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 250 300 L 250 340" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 390 300 L 390 340" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 530 300 L 530 340" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 670 300 L 670 340" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Flecha hacia resultado -->
  <path d="M 390 400 L 390 470" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Resultado Final -->
  <rect x="250" y="470" width="280" height="80" fill="url(#resultGrad)" rx="8" filter="url(#shadow)"/>
  <text x="390" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">String Final Procesado</text>
  <text x="390" y="515" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Caracteres Interpretados</text>
  <text x="390" y="535" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Listos para Uso en Aplicación</text>
  
  <!-- Ejemplos de uso -->
  <rect x="50" y="580" width="200" height="100" fill="#F5F5F5" stroke="#757575" stroke-width="1" rx="5"/>
  <text x="150" y="600" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Ejemplos de Entrada</text>
  <text x="60" y="620" font-family="monospace" font-size="10">"Hola\nMundo"</text>
  <text x="60" y="635" font-family="monospace" font-size="10">"Precio: \u20AC"</text>
  <text x="60" y="650" font-family="monospace" font-size="10">"Path: C:\\Users"</text>
  <text x="60" y="665" font-family="monospace" font-size="10">"Emoji: \u{1F600}"</text>
  
  <rect x="270" y="580" width="200" height="100" fill="#F5F5F5" stroke="#757575" stroke-width="1" rx="5"/>
  <text x="370" y="600" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Resultado Procesado</text>
  <text x="280" y="620" font-family="Arial, sans-serif" font-size="10">Hola</text>
  <text x="280" y="635" font-family="Arial, sans-serif" font-size="10">Mundo</text>
  <text x="280" y="650" font-family="Arial, sans-serif" font-size="10">Precio: €</text>
  <text x="280" y="665" font-family="Arial, sans-serif" font-size="10">Emoji: 😀</text>
  
  <rect x="490" y="580" width="200" height="100" fill="#F5F5F5" stroke="#757575" stroke-width="1" rx="5"/>
  <text x="590" y="600" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Casos de Error</text>
  <text x="500" y="620" font-family="monospace" font-size="10">"\q" → SyntaxError</text>
  <text x="500" y="635" font-family="monospace" font-size="10">"\x" → Incompleto</text>
  <text x="500" y="650" font-family="monospace" font-size="10">"\uGGGG" → Inválido</text>
  <text x="500" y="665" font-family="monospace" font-size="10">"\u{110000}" → Fuera de rango</text>
  
  <rect x="710" y="580" width="240" height="100" fill="#F5F5F5" stroke="#757575" stroke-width="1" rx="5"/>
  <text x="830" y="600" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Consideraciones Especiales</text>
  <text x="720" y="620" font-family="Arial, sans-serif" font-size="9">• Template literals: menos escapes</text>
  <text x="720" y="635" font-family="Arial, sans-serif" font-size="9">• JSON: doble escape necesario</text>
  <text x="720" y="650" font-family="Arial, sans-serif" font-size="9">• RegExp: escape diferente</text>
  <text x="720" y="665" font-family="Arial, sans-serif" font-size="9">• Unicode normalization</text>
  
  <!-- Manejo de errores -->
  <rect x="750" y="340" width="200" height="60" fill="#FFCDD2" stroke="#F44336" stroke-width="2" rx="8" filter="url(#shadow)"/>
  <text x="850" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Manejo de Errores</text>
  <text x="850" y="385" text-anchor="middle" font-family="Arial, sans-serif" font-size="10">SyntaxError | RangeError</text>
  
  <!-- Flecha desde error hacia manejo -->
  <path d="M 810 300 L 850 340" stroke="#F44336" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Definición de marcadores de flecha -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- Información adicional -->
  <rect x="50" y="720" width="900" height="60" fill="#E8EAF6" stroke="#3F51B5" stroke-width="1" rx="5"/>
  <text x="500" y="740" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Información Técnica</text>
  <text x="60" y="760" font-family="Arial, sans-serif" font-size="10">• JavaScript usa UTF-16 internamente • Escape sequences se procesan en tiempo de parsing • Algunos caracteres de control pueden no ser visibles en todas las consolas</text>
  <text x="60" y="775" font-family="Arial, sans-serif" font-size="10">• Unicode BMP cubre caracteres U+0000 a U+FFFF • Unicode extendido permite hasta U+10FFFF • Performance: escape sequences simples son más rápidas</text>
</svg>