## 4.2.8. Conditional expressions

### Introducción
Las conditional expressions en JavaScript son ternarios para decisiones concisas.  
Evalúan condición y retornan valor basado en true/false.  
Son expressions, no statements.  
Pueden anidarse pero con cuidado.  
Mejoran legibilidad en casos simples.  
¿Usas ternarios o if statements?

### Código de Ejemplo
```javascript
// Ternary operator
let edad = 20;
let estado = (edad >= 18) ? 'Adulto' : 'Menor';
console.log(estado);

// Anidado
let nota = 85;
let calificacion = (nota >= 90) ? 'A' : (nota >= 80) ? 'B' : 'C';
console.log(calificacion);

// En función
function clasificar(numero) {
    return (numero > 0) ? 'Positivo' : (numero < 0) ? 'Negativo' : 'Cero';
}
console.log(clasificar(5));
console.log(clasificar(-3));
console.log(clasificar(0));
```
### Resultados en Consola
- `Adulto`
- `B`
- `Positivo`
- `Negativo`
- `Cero`

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> CondEdad["Paso 1: (edad >= 18) ? 'Adulto' : 'Menor' <br> - Ternary"]
    CondEdad --> LogEstado["Paso 2: console.log(estado) <br> - Resultado: Adulto"]
    LogEstado --> CondNota["Paso 3: (nota >= 90) ? 'A' : (nota >= 80) ? 'B' : 'C' <br> - Anidado"]
    CondNota --> LogCalif["Paso 4: console.log(calificacion) <br> - Resultado: B"]
    LogCalif --> FuncDecl["Paso 5: function clasificar(numero) <br> - Función con ternary"]
    FuncDecl --> ReturnTern["Paso 6: return (numero > 0) ? 'Positivo' : ... <br> - Return expression"]
    ReturnTern --> Calls["Paso 7: console.log(clasificar(5)) etc. <br> - Resultados"]
    Calls --> Fin["Paso 8: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Decisiones["Decisiones concisas"]
    CondEdad --> NotaTern["Nota: Expression"]
    CondNota --> NotaAnid["Nota: Legibilidad"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CondEdad fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogEstado fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CondNota fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style LogCalif fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style FuncDecl fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style ReturnTern fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Calls fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Decisiones fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaTern fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaAnid fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Condicion["Condición <br> - Evalúa a true/false"]
        Condicion --> True["True <br> - Valor si true"]
        Condicion --> False["False <br> - Valor si false"]
        True --> Resultado["Resultado <br> - Expression value"]
        False --> Resultado
        Resultado --> Anidado["Anidado <br> - Ternarios internos"]
        Anidado --> Uso["Uso: Asignaciones, returns"]
        Inicio --> Desarrollador(("Desarrollador")) --> Legibilidad["Legibilidad en simples"]
        Condicion --> NotaCond["Nota: Boolean coercion"]
        Anidado --> NotaAnid["Nota: Evitar complejidad"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Condicion fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style True fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style False fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Resultado fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Anidado fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Uso fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Legibilidad fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaCond fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaAnid fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Asignaciones condicionales concisas.
2. Returns en funciones simples.
3. Inline en JSX o templates.
4. Validaciones rápidas.
5. Defaults condicionales.

### Errores comunes
1. Anidar excesivamente, reduciendo legibilidad.
2. Olvidar paréntesis en anidados.
3. Usar para side effects (no lo hagas).
4. Confundir con if statements.
5. Ignorar coercion en condiciones.

### Recomendaciones
1. Usa para expressions simples.
2. Prefiere if para complejidad.
3. Formatea para legibilidad.
4. Evita side effects en branches.
5. Prueba condiciones truthy/falsy.