# **43.1 - FUNDAMENTOS DE LA PROGRAMACIÓN ORIENTADA A OBJETOS**

## **📖 INTRODUCCIÓN**

La Programación Orientada a Objetos representa un paradigma fundamental que revolucionó el desarrollo de software al proporcionar una metodología natural para modelar problemas del mundo real mediante la creación de objetos que encapsulan datos y comportamientos relacionados, estableciendo así una correspondencia directa entre los conceptos del dominio del problema y las estructuras del código. En JavaScript, este paradigma adquiere características únicas debido a la naturaleza dinámica y flexible del lenguaje, que permite implementar conceptos OOP de maneras innovadoras que van más allá de las implementaciones tradicionales encontradas en lenguajes como Java o C#. Comprender estos fundamentos no solo es esencial para escribir código JavaScript efectivo, sino que también proporciona la base conceptual necesaria para aprovechar frameworks modernos, bibliotecas de terceros, y patrones arquitectónicos que dominan el ecosistema de desarrollo web actual, donde la capacidad de crear abstracciones robustas y mantenibles determina la diferencia entre aplicaciones exitosas y proyectos que se vuelven inmanejables con el tiempo.

## **🔍 CONCEPTOS FUNDAMENTALES**

### **43.1.1. Definición y concepto de OOP**

![Concepto OOP](../VISUALIZACIONES/anatomia/oop-concept.svg)

La Programación Orientada a Objetos (OOP) es un paradigma de programación que organiza el código alrededor de **objetos** en lugar de funciones y lógica. Un objeto es una entidad que combina:

- **Datos** (propiedades/atributos)
- **Comportamientos** (métodos/funciones)
- **Identidad** (referencia única)

**Definición Formal:**
> La Programación Orientada a Objetos es un paradigma de programación basado en el concepto de "objetos", que pueden contener datos en forma de campos (propiedades) y código en forma de procedimientos (métodos).

### **43.1.2. Historia y evolución de OOP**

```javascript
/**
 * EJEMPLO FUNDAMENTAL: ¿QUÉ ES UN OBJETO?
 * 
 * Un objeto representa una entidad del mundo real con:
 * - Estado (propiedades)
 * - Comportamiento (métodos)
 * - Identidad (referencia única)
 */

// Objeto literal básico
const persona = {
    // ESTADO - Propiedades que describen el objeto
    nombre: 'Juan',
    edad: 30,
    profesion: 'Desarrollador',
    
    // COMPORTAMIENTO - Métodos que definen qué puede hacer
    saludar() {
        return `Hola, soy ${this.nombre}`;
    },
    
    cumplirAños() {
        this.edad++;
        console.log(`${this.nombre} ahora tiene ${this.edad} años`);
    },
    
    trabajar() {
        console.log(`${this.nombre} está programando...`);
    }
};

// IDENTIDAD - Cada objeto tiene una referencia única
const persona1 = { nombre: 'Ana' };
const persona2 = { nombre: 'Ana' };
console.log(persona1 === persona2); // false - diferentes identidades
```

```javascript
/**
 * LÍNEA TEMPORAL DE OOP - Historia y evolución
 */
const historiaOOP = {
    1960: "Simula 67 - Primer lenguaje con clases y objetos",
    1972: "Smalltalk - Primer lenguaje puramente OOP",
    1983: "C++ - OOP en lenguajes de sistemas",
    1995: "Java - OOP empresarial masiva",
    1995: "JavaScript - OOP basada en prototipos",
    2015: "ES6 - Sintaxis de clases en JavaScript"
};
```

### **43.1.3. Paradigmas de programación**

```javascript
/**
 * COMPARACIÓN DE PARADIGMAS
 * Ejemplo: Calcular área de figuras
 */

// IMPERATIVO - Describe CÓMO hacer
function areaImperativo(figuras) {
    let total = 0;
    for (let i = 0; i < figuras.length; i++) {
        if (figuras[i].tipo === 'rectangulo') {
            total += figuras[i].ancho * figuras[i].alto;
        }
    }
    return total;
}

// FUNCIONAL - Describe QUÉ hacer
const areaFuncional = (figuras) =>
    figuras.reduce((total, f) => total + (f.ancho * f.alto), 0);

// ORIENTADO A OBJETOS - Modela conceptos
class Rectangulo {
    constructor(ancho, alto) {
        this.ancho = ancho;
        this.alto = alto;
    }
    calcularArea() {
        return this.ancho * this.alto;
    }
}
```

### **43.1.4. Ventajas y desventajas de OOP**

**✅ Ventajas:**
- Modelado natural del mundo real
- Reutilización de código (herencia)
- Encapsulación de datos
- Mantenibilidad y escalabilidad

**❌ Desventajas:**
- Complejidad para problemas simples
- Overhead de performance
- Curva de aprendizaje pronunciada

### **43.1.5. Objetos y clases**

```javascript
/**
 * DIFERENCIA ENTRE OBJETOS Y CLASES
 */

// CLASE - Plantilla o molde
class Persona {
    constructor(nombre) {
        this.nombre = nombre;
    }
    saludar() {
        return `Hola, soy ${this.nombre}`;
    }
}

// OBJETOS - Instancias de la clase
const persona1 = new Persona('Ana');
const persona2 = new Persona('Luis');

// OBJETO LITERAL - Sin clase
const persona3 = {
    nombre: 'Carlos',
    saludar() {
        return `Hola, soy ${this.nombre}`;
    }
};
```

### **43.1.6. Propiedades y métodos**

```javascript
/**
 * PROPIEDADES Y MÉTODOS EN OBJETOS
 */

class Vehiculo {
    constructor(marca, modelo) {
        // PROPIEDADES - Estado del objeto
        this.marca = marca;
        this.modelo = modelo;
        this.velocidad = 0;
        this.encendido = false;
    }

    // MÉTODOS - Comportamiento del objeto
    encender() {
        this.encendido = true;
        console.log('Vehículo encendido');
    }

    acelerar(incremento) {
        if (this.encendido) {
            this.velocidad += incremento;
        }
    }

    // GETTER - Acceso controlado a propiedades
    get info() {
        return `${this.marca} ${this.modelo}`;
    }

    // SETTER - Modificación controlada
    set nuevaVelocidad(valor) {
        if (valor >= 0) {
            this.velocidad = valor;
        }
    }
}
```

### **43.1.7. Estado y comportamiento**

```javascript
/**
 * ESTADO Y COMPORTAMIENTO EN OOP
 */

class CuentaBancaria {
    constructor(numeroCuenta, saldoInicial) {
        // ESTADO - Datos que definen el objeto
        this.numeroCuenta = numeroCuenta;
        this.saldo = saldoInicial;
        this.transacciones = [];
        this.activa = true;
    }

    // COMPORTAMIENTO - Acciones que puede realizar
    depositar(cantidad) {
        // El comportamiento modifica el estado
        this.saldo += cantidad;
        this.transacciones.push({
            tipo: 'depósito',
            cantidad,
            fecha: new Date()
        });
    }

    retirar(cantidad) {
        if (cantidad <= this.saldo) {
            this.saldo -= cantidad;
            this.transacciones.push({
                tipo: 'retiro',
                cantidad,
                fecha: new Date()
            });
        }
    }

    // El comportamiento puede depender del estado
    puedeRetirar(cantidad) {
        return this.activa && cantidad <= this.saldo;
    }
}
```

### **43.1.8. Importancia en el desarrollo moderno**

```javascript
/**
 * OOP EN DESARROLLO MODERNO
 * Ejemplos de uso en frameworks y librerías actuales
 */

// React Components (OOP con clases)
class ComponenteReact extends React.Component {
    constructor(props) {
        super(props);
        this.state = { contador: 0 };
    }

    incrementar = () => {
        this.setState({ contador: this.state.contador + 1 });
    }

    render() {
        return (
            <div>
                <p>Contador: {this.state.contador}</p>
                <button onClick={this.incrementar}>+</button>
            </div>
        );
    }
}

// Node.js Modules (OOP para APIs)
class APIController {
    constructor(database) {
        this.db = database;
    }

    async obtenerUsuarios() {
        return await this.db.usuarios.findAll();
    }

    async crearUsuario(datos) {
        return await this.db.usuarios.create(datos);
    }
}

// Frameworks de Testing (OOP para organización)
class TestSuite {
    constructor(nombre) {
        this.nombre = nombre;
        this.tests = [];
    }

    agregar(test) {
        this.tests.push(test);
    }

    ejecutar() {
        this.tests.forEach(test => test.run());
    }
}
```

### **43.1.9. Visualización y diagramas**

```javascript
/**
 * REPRESENTACIÓN VISUAL DE CONCEPTOS OOP
 * Código que genera datos para visualizaciones
 */

class DiagramaOOP {
    constructor() {
        this.elementos = [];
    }

    agregarClase(nombre, propiedades, metodos) {
        this.elementos.push({
            tipo: 'clase',
            nombre,
            propiedades,
            metodos,
            posicion: { x: 0, y: 0 }
        });
    }

    agregarRelacion(origen, destino, tipo) {
        this.elementos.push({
            tipo: 'relacion',
            origen,
            destino,
            tipoRelacion: tipo // 'herencia', 'composicion', 'agregacion'
        });
    }

    generarDiagrama() {
        return {
            clases: this.elementos.filter(e => e.tipo === 'clase'),
            relaciones: this.elementos.filter(e => e.tipo === 'relacion')
        };
    }
}

// Ejemplo de uso para generar diagrama
const diagrama = new DiagramaOOP();
diagrama.agregarClase('Vehiculo', ['marca', 'modelo'], ['encender', 'acelerar']);
diagrama.agregarClase('Auto', ['puertas'], ['abrirPuertas']);
diagrama.agregarRelacion('Auto', 'Vehiculo', 'herencia');
```

### **43.1.10. Terminología y conceptos relacionados**

```javascript
/**
 * GLOSARIO DE TÉRMINOS OOP
 * Definiciones y ejemplos de conceptos clave
 */

const terminologiaOOP = {
    // Conceptos básicos
    objeto: {
        definicion: "Instancia de una clase con estado y comportamiento",
        ejemplo: "const auto = new Vehiculo('Toyota', 'Corolla')"
    },

    clase: {
        definicion: "Plantilla o molde para crear objetos",
        ejemplo: "class Vehiculo { constructor(marca) { this.marca = marca; } }"
    },

    instancia: {
        definicion: "Objeto específico creado a partir de una clase",
        ejemplo: "const miAuto = new Auto(); // miAuto es una instancia"
    },

    // Pilares de OOP
    encapsulacion: {
        definicion: "Ocultar detalles internos y exponer interfaz pública",
        ejemplo: "Usar métodos públicos para acceder a propiedades privadas"
    },

    herencia: {
        definicion: "Mecanismo para crear clases basadas en otras clases",
        ejemplo: "class Auto extends Vehiculo"
    },

    polimorfismo: {
        definicion: "Capacidad de objetos diferentes de responder al mismo mensaje",
        ejemplo: "Diferentes clases implementan el mismo método de forma específica"
    },

    abstraccion: {
        definicion: "Simplificar complejidad mostrando solo lo esencial",
        ejemplo: "Interfaz simple que oculta implementación compleja"
    },

    // Conceptos avanzados
    composicion: {
        definicion: "Construir objetos complejos combinando objetos simples",
        ejemplo: "Auto tiene Motor, no hereda de Motor"
    },

    agregacion: {
        definicion: "Relación donde un objeto contiene otros objetos",
        ejemplo: "Universidad tiene Estudiantes"
    },

    asociacion: {
        definicion: "Relación entre objetos que pueden existir independientemente",
        ejemplo: "Profesor enseña a Estudiante"
    },

    // Términos específicos de JavaScript
    prototipo: {
        definicion: "Objeto del cual otros objetos heredan propiedades",
        ejemplo: "Vehiculo.prototype.acelerar = function() {...}"
    },

    constructor: {
        definicion: "Método especial para inicializar objetos",
        ejemplo: "constructor(marca, modelo) { this.marca = marca; }"
    },

    this: {
        definicion: "Referencia al objeto actual en el contexto de ejecución",
        ejemplo: "this.marca se refiere a la propiedad marca del objeto actual"
    }
};

// Función para mostrar definiciones
function mostrarTerminologia() {
    Object.entries(terminologiaOOP).forEach(([termino, info]) => {
        console.log(`${termino.toUpperCase()}:`);
        console.log(`  Definición: ${info.definicion}`);
        console.log(`  Ejemplo: ${info.ejemplo}\n`);
    });
}
```

```javascript
/**
 * COMPARACIÓN DE PARADIGMAS
 * 
 * El mismo problema resuelto con diferentes paradigmas
 * Problema: Gestionar una biblioteca de libros
 */

// ===== PARADIGMA IMPERATIVO =====
// Enfoque: Describe CÓMO hacer las cosas paso a paso
function gestionBibliotecaImperativo() {
    let libros = [];
    let prestamos = [];
    
    // Agregar libro
    function agregarLibro(titulo, autor) {
        let libro = {
            id: libros.length + 1,
            titulo: titulo,
            autor: autor,
            disponible: true
        };
        libros.push(libro);
        return libro.id;
    }
    
    // Prestar libro
    function prestarLibro(id, usuario) {
        for (let i = 0; i < libros.length; i++) {
            if (libros[i].id === id && libros[i].disponible) {
                libros[i].disponible = false;
                prestamos.push({
                    libroId: id,
                    usuario: usuario,
                    fecha: new Date()
                });
                return true;
            }
        }
        return false;
    }
    
    return { agregarLibro, prestarLibro, libros, prestamos };
}

// ===== PARADIGMA FUNCIONAL =====
// Enfoque: Funciones puras, inmutabilidad
const gestionBibliotecaFuncional = (() => {
    const crearLibro = (id, titulo, autor) => ({
        id,
        titulo,
        autor,
        disponible: true
    });
    
    const agregarLibro = (biblioteca, titulo, autor) => {
        const nuevoId = biblioteca.libros.length + 1;
        const nuevoLibro = crearLibro(nuevoId, titulo, autor);
        
        return {
            ...biblioteca,
            libros: [...biblioteca.libros, nuevoLibro]
        };
    };
    
    const prestarLibro = (biblioteca, id, usuario) => {
        const librosActualizados = biblioteca.libros.map(libro =>
            libro.id === id && libro.disponible
                ? { ...libro, disponible: false }
                : libro
        );
        
        const nuevoPrestamo = {
            libroId: id,
            usuario,
            fecha: new Date()
        };
        
        return {
            libros: librosActualizados,
            prestamos: [...biblioteca.prestamos, nuevoPrestamo]
        };
    };
    
    return { agregarLibro, prestarLibro };
})();

// ===== PARADIGMA ORIENTADO A OBJETOS =====
// Enfoque: Modela conceptos como objetos con estado y comportamiento
class Libro {
    constructor(id, titulo, autor) {
        this.id = id;
        this.titulo = titulo;
        this.autor = autor;
        this.disponible = true;
        this.historialPrestamos = [];
    }
    
    prestar(usuario) {
        if (!this.disponible) {
            throw new Error('Libro no disponible');
        }
        
        this.disponible = false;
        this.historialPrestamos.push({
            usuario,
            fechaPrestamo: new Date(),
            fechaDevolucion: null
        });
        
        console.log(`"${this.titulo}" prestado a ${usuario}`);
    }
    
    devolver() {
        if (this.disponible) {
            throw new Error('Libro ya está disponible');
        }
        
        this.disponible = true;
        const ultimoPrestamo = this.historialPrestamos[this.historialPrestamos.length - 1];
        ultimoPrestamo.fechaDevolucion = new Date();
        
        console.log(`"${this.titulo}" devuelto`);
    }
    
    obtenerInfo() {
        return {
            id: this.id,
            titulo: this.titulo,
            autor: this.autor,
            disponible: this.disponible,
            totalPrestamos: this.historialPrestamos.length
        };
    }
}

class Biblioteca {
    constructor(nombre) {
        this.nombre = nombre;
        this.libros = new Map();
        this.siguienteId = 1;
    }
    
    agregarLibro(titulo, autor) {
        const libro = new Libro(this.siguienteId++, titulo, autor);
        this.libros.set(libro.id, libro);
        
        console.log(`Libro "${titulo}" agregado a ${this.nombre}`);
        return libro.id;
    }
    
    prestarLibro(id, usuario) {
        const libro = this.libros.get(id);
        
        if (!libro) {
            throw new Error('Libro no encontrado');
        }
        
        libro.prestar(usuario);
    }
    
    devolverLibro(id) {
        const libro = this.libros.get(id);
        
        if (!libro) {
            throw new Error('Libro no encontrado');
        }
        
        libro.devolver();
    }
    
    obtenerEstadisticas() {
        const totalLibros = this.libros.size;
        const librosDisponibles = Array.from(this.libros.values())
            .filter(libro => libro.disponible).length;
        const librosPrestados = totalLibros - librosDisponibles;
        
        return {
            nombre: this.nombre,
            totalLibros,
            librosDisponibles,
            librosPrestados,
            tasaOcupacion: ((librosPrestados / totalLibros) * 100).toFixed(1) + '%'
        };
    }
}
```

### **💻 EMULACIÓN DE CONSOLA**

```bash
# Comparación de paradigmas en acción

# === PARADIGMA IMPERATIVO ===
const bibliotecaImperativo = gestionBibliotecaImperativo();
bibliotecaImperativo.agregarLibro('1984', 'George Orwell');
bibliotecaImperativo.prestarLibro(1, 'Juan');
console.log(bibliotecaImperativo.libros);
[
  {
    id: 1,
    titulo: '1984',
    autor: 'George Orwell',
    disponible: false
  }
]

# === PARADIGMA FUNCIONAL ===
let bibliotecaFuncional = { libros: [], prestamos: [] };
bibliotecaFuncional = gestionBibliotecaFuncional.agregarLibro(
    bibliotecaFuncional, 
    'El Quijote', 
    'Cervantes'
);
bibliotecaFuncional = gestionBibliotecaFuncional.prestarLibro(
    bibliotecaFuncional, 
    1, 
    'María'
);
console.log(bibliotecaFuncional);
{
  libros: [
    {
      id: 1,
      titulo: 'El Quijote',
      autor: 'Cervantes',
      disponible: false
    }
  ],
  prestamos: [
    {
      libroId: 1,
      usuario: 'María',
      fecha: 2024-01-15T10:30:45.123Z
    }
  ]
}

# === PARADIGMA ORIENTADO A OBJETOS ===
const biblioteca = new Biblioteca('Biblioteca Central');
const libroId = biblioteca.agregarLibro('Cien años de soledad', 'García Márquez');
biblioteca.prestarLibro(libroId, 'Carlos');

console.log(biblioteca.obtenerEstadisticas());
{
  nombre: 'Biblioteca Central',
  totalLibros: 1,
  librosDisponibles: 0,
  librosPrestados: 1,
  tasaOcupacion: '100.0%'
}

Libro "Cien años de soledad" agregado a Biblioteca Central
"Cien años de soledad" prestado a Carlos
```

### **43.1.4. Ventajas y desventajas de OOP**

**✅ Ventajas:**
- **Modelado natural:** Representa conceptos del mundo real
- **Reutilización:** Herencia y composición permiten reutilizar código
- **Encapsulación:** Oculta detalles internos y expone interfaces limpias
- **Mantenibilidad:** Código organizado y modular
- **Escalabilidad:** Fácil agregar nuevas funcionalidades

**❌ Desventajas:**
- **Complejidad:** Puede ser excesivo para problemas simples
- **Performance:** Overhead de objetos y métodos
- **Curva de aprendizaje:** Requiere entender conceptos abstractos
- **Over-engineering:** Tendencia a crear jerarquías innecesarias

### **43.1.5. Objetos y clases**

En JavaScript, los objetos pueden crearse de múltiples formas:

```javascript
// 1. Objeto literal
const persona1 = {
    nombre: 'Ana',
    saludar() { return `Hola, soy ${this.nombre}`; }
};

// 2. Función constructora
function Persona(nombre) {
    this.nombre = nombre;
    this.saludar = function() {
        return `Hola, soy ${this.nombre}`;
    };
}
const persona2 = new Persona('Luis');

// 3. Clase ES6
class PersonaClase {
    constructor(nombre) {
        this.nombre = nombre;
    }
    
    saludar() {
        return `Hola, soy ${this.nombre}`;
    }
}
const persona3 = new PersonaClase('María');
```

## **🎯 CASOS DE USO PRÁCTICOS**

### **Caso 1: Sistema de E-commerce**
Modelar productos, usuarios, carritos de compra y pedidos como objetos interrelacionados.

### **Caso 2: Juego de Rol**
Crear personajes, items, habilidades y sistemas de combate usando OOP.

### **Caso 3: Aplicación de Gestión**
Organizar empleados, departamentos, proyectos y tareas en una estructura orientada a objetos.

## **💡 MEJORES PRÁCTICAS**

### **1. Principio de Responsabilidad Única**
Cada clase debe tener una sola razón para cambiar.

### **2. Encapsulación Apropiada**
Oculta detalles internos y expone solo lo necesario.

### **3. Composición sobre Herencia**
Prefiere componer objetos antes que crear jerarquías complejas.

### **4. Interfaces Claras**
Diseña métodos públicos intuitivos y consistentes.

## **🤔 PREGUNTAS PARA REFLEXIÓN**

- ¿Cuándo es apropiado usar OOP versus otros paradigmas?
- ¿Cómo decides qué debe ser una clase y qué debe ser una función?
- ¿Qué ventajas ofrece JavaScript para implementar OOP?
- ¿Cómo balanceas flexibilidad y estructura en el diseño de objetos?

## **📚 RECURSOS ADICIONALES**

- **Implementación Completa:** Ver `CODIGO/fundamentos/OOPFoundations.js`
- **Ejemplos Prácticos:** Ver `EJEMPLOS/basicos/oop-fundamentals.js`
- **Tests:** Ver `TESTS/unit/oop-foundations.test.js`
- **Visualizaciones:** Ver `VISUALIZACIONES/anatomia/oop-concept.svg`

---

**Continúa con:** `43.2 - Pilares de la Programación Orientada a Objetos.md`
