/**
 * CODECRAFT ACADEMY MOBILE APP
 * ============================
 * 
 * Aplicación móvil principal para iOS y Android
 * Plataforma completa de aprendizaje de programación
 */

import React, {useEffect, useState} from 'react';
import {
  StatusBar,
  StyleSheet,
  useColorScheme,
  AppState,
  Linking,
  Alert,
} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {Provider} from 'react-redux';
import {PersistGate} from 'redux-persist/integration/react';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {Provider as PaperProvider} from 'react-native-paper';
import Toast from 'react-native-toast-message';
import SplashScreen from 'react-native-splash-screen';
import NetInfo from '@react-native-community/netinfo';
import CodePush from 'react-native-code-push';
import {enableScreens} from 'react-native-screens';

// Redux Store
import {store, persistor} from './store/store';

// Navigation
import AppNavigator from './navigation/AppNavigator';
import AuthNavigator from './navigation/AuthNavigator';

// Components
import LoadingScreen from './components/common/LoadingScreen';
import OfflineNotice from './components/common/OfflineNotice';
import UpdateModal from './components/common/UpdateModal';

// Hooks
import {useAppSelector, useAppDispatch} from './hooks/redux';

// Services
import {authService} from './services/authService';
import {analyticsService} from './services/analyticsService';
import {notificationService} from './services/notificationService';

// Utils
import {theme} from './utils/theme';
import {navigationRef} from './utils/navigationUtils';

// Types
import {RootState} from './store/store';

// Enable screens for better performance
enableScreens();

// CodePush configuration
const codePushOptions = {
  checkFrequency: CodePush.CheckFrequency.ON_APP_RESUME,
  installMode: CodePush.InstallMode.ON_NEXT_RESUME,
};

const AppContent: React.FC = () => {
  const dispatch = useAppDispatch();
  const {isAuthenticated, isLoading: authLoading} = useAppSelector(
    (state: RootState) => state.auth,
  );
  const {theme: userTheme} = useAppSelector((state: RootState) => state.user);

  const [isConnected, setIsConnected] = useState(true);
  const [appState, setAppState] = useState(AppState.currentState);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [updateInfo, setUpdateInfo] = useState<any>(null);

  const isDarkMode = useColorScheme() === 'dark';
  const currentTheme = userTheme === 'auto' ? (isDarkMode ? 'dark' : 'light') : userTheme;

  useEffect(() => {
    initializeApp();
    setupAppStateListener();
    setupNetworkListener();
    setupDeepLinking();
    setupCodePush();

    return () => {
      // Cleanup listeners
    };
  }, []);

  const initializeApp = async () => {
    try {
      // Initialize services
      await analyticsService.initialize();
      await notificationService.initialize();
      
      // Check authentication
      await authService.checkAuthStatus();
      
      // Hide splash screen
      SplashScreen.hide();
    } catch (error) {
      console.error('Error initializing app:', error);
      analyticsService.trackError('app_initialization_error', error);
    }
  };

  const setupAppStateListener = () => {
    const handleAppStateChange = (nextAppState: string) => {
      if (appState.match(/inactive|background/) && nextAppState === 'active') {
        // App has come to the foreground
        analyticsService.trackEvent('app_foreground');
        
        // Check for updates when app becomes active
        checkForUpdates();
      } else if (nextAppState.match(/inactive|background/)) {
        // App has gone to the background
        analyticsService.trackEvent('app_background');
      }
      
      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  };

  const setupNetworkListener = () => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected ?? false);
      
      if (state.isConnected) {
        analyticsService.trackEvent('network_connected');
      } else {
        analyticsService.trackEvent('network_disconnected');
      }
    });

    return unsubscribe;
  };

  const setupDeepLinking = () => {
    // Handle initial URL if app was opened from a deep link
    Linking.getInitialURL().then(url => {
      if (url) {
        handleDeepLink(url);
      }
    });

    // Handle deep links when app is already open
    const subscription = Linking.addEventListener('url', ({url}) => {
      handleDeepLink(url);
    });

    return () => subscription?.remove();
  };

  const handleDeepLink = (url: string) => {
    try {
      const route = url.replace(/.*?:\/\//g, '');
      const routeName = route.split('/')[0];

      analyticsService.trackEvent('deep_link_opened', {url, routeName});

      // Navigate based on deep link
      switch (routeName) {
        case 'course':
          // Navigate to course
          break;
        case 'lesson':
          // Navigate to lesson
          break;
        case 'certificate':
          // Navigate to certificate
          break;
        default:
          console.log('Unknown deep link:', url);
      }
    } catch (error) {
      console.error('Error handling deep link:', error);
    }
  };

  const setupCodePush = () => {
    CodePush.checkForUpdate().then(update => {
      if (update) {
        setUpdateInfo(update);
        setShowUpdateModal(true);
      }
    });
  };

  const checkForUpdates = () => {
    CodePush.checkForUpdate().then(update => {
      if (update && !update.failedInstall) {
        setUpdateInfo(update);
        setShowUpdateModal(true);
      }
    });
  };

  const handleUpdateInstall = () => {
    setShowUpdateModal(false);
    
    CodePush.sync(
      {
        installMode: CodePush.InstallMode.IMMEDIATE,
        updateDialog: {
          title: 'Actualización Disponible',
          description: 'Se ha descargado una nueva versión de la aplicación.',
          mandatoryUpdateMessage: 'Esta actualización es obligatoria.',
          mandatoryContinueButtonLabel: 'Continuar',
          optionalIgnoreButtonLabel: 'Ignorar',
          optionalInstallButtonLabel: 'Instalar',
          optionalUpdateMessage: 'Hay una actualización disponible. ¿Deseas instalarla?',
        },
      },
      status => {
        switch (status) {
          case CodePush.SyncStatus.DOWNLOADING_PACKAGE:
            analyticsService.trackEvent('update_downloading');
            break;
          case CodePush.SyncStatus.INSTALLING_UPDATE:
            analyticsService.trackEvent('update_installing');
            break;
          case CodePush.SyncStatus.UPDATE_INSTALLED:
            analyticsService.trackEvent('update_installed');
            break;
        }
      },
    );
  };

  if (authLoading) {
    return <LoadingScreen />;
  }

  return (
    <GestureHandlerRootView style={styles.container}>
      <SafeAreaProvider>
        <PaperProvider theme={theme[currentTheme]}>
          <StatusBar
            barStyle={currentTheme === 'dark' ? 'light-content' : 'dark-content'}
            backgroundColor={theme[currentTheme].colors.surface}
          />
          
          <NavigationContainer
            ref={navigationRef}
            theme={theme[currentTheme]}
            onStateChange={state => {
              // Track navigation for analytics
              const currentRoute = state?.routes[state.index];
              if (currentRoute) {
                analyticsService.trackScreen(currentRoute.name);
              }
            }}>
            {isAuthenticated ? <AppNavigator /> : <AuthNavigator />}
          </NavigationContainer>

          {!isConnected && <OfflineNotice />}
          
          <UpdateModal
            visible={showUpdateModal}
            updateInfo={updateInfo}
            onInstall={handleUpdateInstall}
            onDismiss={() => setShowUpdateModal(false)}
          />
          
          <Toast />
        </PaperProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
};

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <PersistGate loading={<LoadingScreen />} persistor={persistor}>
        <AppContent />
      </PersistGate>
    </Provider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

// Export with CodePush HOC
export default CodePush(codePushOptions)(App);
