<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .code { font-family: 'Courier New', monospace; font-size: 11px; fill: #e74c3c; }
      .var-hoisting { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .let-const { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .function-hoisting { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .tdz { fill: #f39c12; stroke: #d68910; stroke-width: 2; }
      .execution-context { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .danger-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title">JavaScript Hoisting y Temporal Dead Zone - Análisis Completo</text>
  
  <!-- Execution Context Creation -->
  <rect x="50" y="60" width="1300" height="150" class="execution-context" rx="10" opacity="0.3"/>
  <text x="700" y="85" text-anchor="middle" class="subtitle">🏗️ EXECUTION CONTEXT CREATION PHASE</text>
  
  <rect x="70" y="110" width="400" height="80" fill="#ffffff" stroke="#8e44ad" stroke-width="2" rx="5"/>
  <text x="270" y="135" text-anchor="middle" class="subtitle">1️⃣ Creation Phase</text>
  <text x="80" y="155" class="text">• Variable Environment setup</text>
  <text x="80" y="170" class="text">• Lexical Environment creation</text>
  <text x="80" y="185" class="text">• this binding determination</text>
  
  <rect x="500" y="110" width="400" height="80" fill="#ffffff" stroke="#8e44ad" stroke-width="2" rx="5"/>
  <text x="700" y="135" text-anchor="middle" class="subtitle">2️⃣ Hoisting Process</text>
  <text x="510" y="155" class="text">• Function declarations: Fully hoisted</text>
  <text x="510" y="170" class="text">• var declarations: Hoisted, initialized undefined</text>
  <text x="510" y="185" class="text">• let/const: Hoisted, uninitialized (TDZ)</text>
  
  <rect x="930" y="110" width="400" height="80" fill="#ffffff" stroke="#8e44ad" stroke-width="2" rx="5"/>
  <text x="1130" y="135" text-anchor="middle" class="subtitle">3️⃣ Execution Phase</text>
  <text x="940" y="155" class="text">• Code execution line by line</text>
  <text x="940" y="170" class="text">• Variable assignments</text>
  <text x="940" y="185" class="text">• Function invocations</text>
  
  <!-- VAR Hoisting -->
  <rect x="50" y="240" width="650" height="300" class="var-hoisting" rx="10" opacity="0.4"/>
  <text x="375" y="265" text-anchor="middle" class="subtitle">📦 VAR HOISTING - Function Scoped</text>
  
  <!-- Original Code -->
  <rect x="70" y="290" width="280" height="220" fill="#ffffff" stroke="#2980b9" stroke-width="2" rx="5"/>
  <text x="210" y="315" text-anchor="middle" class="subtitle">📝 Original Code</text>
  <text x="80" y="335" class="code">console.log(x); // ?</text>
  <text x="80" y="350" class="code">console.log(y); // ?</text>
  <text x="80" y="365" class="code">console.log(z); // ?</text>
  <text x="80" y="385" class="code">var x = 10;</text>
  <text x="80" y="400" class="code">var y;</text>
  <text x="80" y="415" class="code">// z is never declared</text>
  <text x="80" y="435" class="code">function test() {</text>
  <text x="90" y="450" class="code">console.log(a); // ?</text>
  <text x="90" y="465" class="code">var a = 20;</text>
  <text x="80" y="480" class="code">}</text>
  <text x="80" y="495" class="code">test();</text>
  
  <!-- Hoisted Version -->
  <rect x="370" y="290" width="310" height="220" fill="#ffffff" stroke="#2980b9" stroke-width="2" rx="5"/>
  <text x="525" y="315" text-anchor="middle" class="subtitle">🔄 After Hoisting</text>
  <text x="380" y="335" class="code">var x; // undefined</text>
  <text x="380" y="350" class="code">var y; // undefined</text>
  <text x="380" y="365" class="code">// z not hoisted</text>
  <text x="380" y="385" class="code">console.log(x); // undefined</text>
  <text x="380" y="400" class="code">console.log(y); // undefined</text>
  <text x="380" y="415" class="code">console.log(z); // ReferenceError</text>
  <text x="380" y="435" class="code">x = 10;</text>
  <text x="380" y="455" class="code">function test() {</text>
  <text x="390" y="470" class="code">var a; // undefined</text>
  <text x="390" y="485" class="code">console.log(a); // undefined</text>
  <text x="390" y="500" class="code">a = 20;</text>
  <text x="380" y="515" class="code">}</text>
  
  <!-- LET/CONST and TDZ -->
  <rect x="750" y="240" width="600" height="300" class="let-const" rx="10" opacity="0.4"/>
  <text x="1050" y="265" text-anchor="middle" class="subtitle">🚫 LET/CONST - Block Scoped + TDZ</text>
  
  <!-- TDZ Visualization -->
  <rect x="770" y="290" width="560" height="220" fill="#ffffff" stroke="#c0392b" stroke-width="2" rx="5"/>
  <text x="1050" y="315" text-anchor="middle" class="subtitle">⚠️ Temporal Dead Zone</text>
  
  <rect x="780" y="330" width="540" height="30" class="tdz" rx="3"/>
  <text x="1050" y="350" text-anchor="middle" class="text">🚫 TDZ: Variables exist but are uninitialized</text>
  
  <text x="790" y="380" class="code">// TDZ starts here for 'a', 'b', 'c'</text>
  <text x="790" y="395" class="code">console.log(a); // ReferenceError: Cannot access 'a' before initialization</text>
  <text x="790" y="410" class="code">console.log(b); // ReferenceError: Cannot access 'b' before initialization</text>
  <text x="790" y="425" class="code">console.log(c); // ReferenceError: Cannot access 'c' before initialization</text>
  
  <rect x="780" y="440" width="540" height="30" fill="#27ae60" stroke="#229954" stroke-width="2" rx="3"/>
  <text x="1050" y="460" text-anchor="middle" class="text">✅ Initialization: TDZ ends, variables become accessible</text>
  
  <text x="790" y="485" class="code">let a = 10;     // TDZ ends for 'a'</text>
  <text x="790" y="500" class="code">const b = 20;   // TDZ ends for 'b'</text>
  
  <!-- Function Hoisting -->
  <rect x="50" y="570" width="1300" height="200" class="function-hoisting" rx="10" opacity="0.4"/>
  <text x="700" y="595" text-anchor="middle" class="subtitle">🔧 FUNCTION HOISTING - Complete Function Objects</text>
  
  <!-- Function Declaration vs Expression -->
  <rect x="70" y="620" width="400" height="120" fill="#ffffff" stroke="#229954" stroke-width="2" rx="5"/>
  <text x="270" y="645" text-anchor="middle" class="subtitle">📋 Function Declaration</text>
  <text x="80" y="665" class="code">// Can be called before declaration</text>
  <text x="80" y="680" class="code">sayHello(); // "Hello!" - Works!</text>
  <text x="80" y="700" class="code">function sayHello() {</text>
  <text x="90" y="715" class="code">console.log("Hello!");</text>
  <text x="80" y="730" class="code">}</text>
  
  <rect x="500" y="620" width="400" height="120" fill="#ffffff" stroke="#229954" stroke-width="2" rx="5"/>
  <text x="700" y="645" text-anchor="middle" class="subtitle">🚫 Function Expression</text>
  <text x="510" y="665" class="code">// Cannot be called before assignment</text>
  <text x="510" y="680" class="code">sayBye(); // TypeError: sayBye is not a function</text>
  <text x="510" y="700" class="code">var sayBye = function() {</text>
  <text x="520" y="715" class="code">console.log("Bye!");</text>
  <text x="510" y="730" class="code">};</text>
  
  <rect x="930" y="620" width="400" height="120" fill="#ffffff" stroke="#229954" stroke-width="2" rx="5"/>
  <text x="1130" y="645" text-anchor="middle" class="subtitle">🚫 Arrow Function</text>
  <text x="940" y="665" class="code">// Cannot be called before assignment</text>
  <text x="940" y="680" class="code">greet(); // ReferenceError (if let/const)</text>
  <text x="940" y="700" class="code">const greet = () => {</text>
  <text x="950" y="715" class="code">console.log("Hi!");</text>
  <text x="940" y="730" class="code">};</text>
  
  <!-- Detailed TDZ Timeline -->
  <rect x="50" y="800" width="1300" height="250" fill="#f8f9fa" stroke="#f39c12" stroke-width="2" rx="10"/>
  <text x="700" y="825" text-anchor="middle" class="subtitle">⏰ TEMPORAL DEAD ZONE - Detailed Timeline</text>
  
  <!-- Timeline -->
  <line x1="100" y1="860" x2="1300" y2="860" stroke="#34495e" stroke-width="3"/>
  
  <!-- Timeline markers -->
  <circle cx="150" cy="860" r="8" fill="#e74c3c"/>
  <text x="150" y="885" text-anchor="middle" class="small-text">Scope Entry</text>
  <text x="150" y="900" text-anchor="middle" class="code">TDZ Starts</text>
  
  <circle cx="400" cy="860" r="8" fill="#f39c12"/>
  <text x="400" y="885" text-anchor="middle" class="small-text">Access Attempt</text>
  <text x="400" y="900" text-anchor="middle" class="code">ReferenceError</text>
  
  <circle cx="700" cy="860" r="8" fill="#27ae60"/>
  <text x="700" y="885" text-anchor="middle" class="small-text">Declaration</text>
  <text x="700" y="900" text-anchor="middle" class="code">TDZ Ends</text>
  
  <circle cx="1000" cy="860" r="8" fill="#3498db"/>
  <text x="1000" y="885" text-anchor="middle" class="small-text">Normal Access</text>
  <text x="1000" y="900" text-anchor="middle" class="code">Variable Available</text>
  
  <!-- TDZ Zone visualization -->
  <rect x="150" y="840" width="550" height="40" fill="#e74c3c" opacity="0.3" rx="5"/>
  <text x="425" y="865" text-anchor="middle" class="text" fill="#ffffff">🚫 TEMPORAL DEAD ZONE</text>
  
  <!-- Code example for timeline -->
  <rect x="100" y="920" width="1200" height="110" fill="#ffffff" stroke="#34495e" stroke-width="2" rx="5"/>
  <text x="110" y="940" class="code">function example() {</text>
  <text x="120" y="955" class="code">// TDZ starts here for 'x' and 'y'</text>
  <text x="120" y="970" class="code">console.log(typeof x); // ReferenceError (not "undefined"!)</text>
  <text x="120" y="985" class="code">console.log(y); // ReferenceError</text>
  <text x="120" y="1000" class="code">let x = 10; // TDZ ends for 'x'</text>
  <text x="120" y="1015" class="code">const y = 20; // TDZ ends for 'y'</text>
  <text x="110" y="1030" class="code">}</text>
  
  <!-- Key Differences Table -->
  <rect x="50" y="1070" width="1300" height="200" fill="#ffffff" stroke="#2c3e50" stroke-width="2" rx="10"/>
  <text x="700" y="1095" text-anchor="middle" class="subtitle">📊 HOISTING COMPARISON TABLE</text>
  
  <!-- Table headers -->
  <rect x="70" y="1110" width="200" height="30" fill="#34495e"/>
  <text x="170" y="1130" text-anchor="middle" class="text" fill="#ffffff">Declaration Type</text>
  
  <rect x="270" y="1110" width="200" height="30" fill="#34495e"/>
  <text x="370" y="1130" text-anchor="middle" class="text" fill="#ffffff">Hoisted?</text>
  
  <rect x="470" y="1110" width="200" height="30" fill="#34495e"/>
  <text x="570" y="1130" text-anchor="middle" class="text" fill="#ffffff">Initialized?</text>
  
  <rect x="670" y="1110" width="200" height="30" fill="#34495e"/>
  <text x="770" y="1130" text-anchor="middle" class="text" fill="#ffffff">TDZ?</text>
  
  <rect x="870" y="1110" width="200" height="30" fill="#34495e"/>
  <text x="970" y="1130" text-anchor="middle" class="text" fill="#ffffff">Scope</text>
  
  <rect x="1070" y="1110" width="280" height="30" fill="#34495e"/>
  <text x="1210" y="1130" text-anchor="middle" class="text" fill="#ffffff">Access Before Declaration</text>
  
  <!-- Table rows -->
  <!-- var row -->
  <rect x="70" y="1140" width="200" height="25" fill="#e8f4fd"/>
  <text x="170" y="1157" text-anchor="middle" class="text">var</text>
  <rect x="270" y="1140" width="200" height="25" fill="#e8f4fd"/>
  <text x="370" y="1157" text-anchor="middle" class="text">✅ Yes</text>
  <rect x="470" y="1140" width="200" height="25" fill="#e8f4fd"/>
  <text x="570" y="1157" text-anchor="middle" class="text">✅ undefined</text>
  <rect x="670" y="1140" width="200" height="25" fill="#e8f4fd"/>
  <text x="770" y="1157" text-anchor="middle" class="text">❌ No</text>
  <rect x="870" y="1140" width="200" height="25" fill="#e8f4fd"/>
  <text x="970" y="1157" text-anchor="middle" class="text">Function</text>
  <rect x="1070" y="1140" width="280" height="25" fill="#e8f4fd"/>
  <text x="1210" y="1157" text-anchor="middle" class="text">undefined</text>
  
  <!-- let row -->
  <rect x="70" y="1165" width="200" height="25" fill="#fdf2e9"/>
  <text x="170" y="1182" text-anchor="middle" class="text">let</text>
  <rect x="270" y="1165" width="200" height="25" fill="#fdf2e9"/>
  <text x="370" y="1182" text-anchor="middle" class="text">✅ Yes</text>
  <rect x="470" y="1165" width="200" height="25" fill="#fdf2e9"/>
  <text x="570" y="1182" text-anchor="middle" class="text">❌ Uninitialized</text>
  <rect x="670" y="1165" width="200" height="25" fill="#fdf2e9"/>
  <text x="770" y="1182" text-anchor="middle" class="text">✅ Yes</text>
  <rect x="870" y="1165" width="200" height="25" fill="#fdf2e9"/>
  <text x="970" y="1182" text-anchor="middle" class="text">Block</text>
  <rect x="1070" y="1165" width="280" height="25" fill="#fdf2e9"/>
  <text x="1210" y="1182" text-anchor="middle" class="text">ReferenceError</text>
  
  <!-- const row -->
  <rect x="70" y="1190" width="200" height="25" fill="#fdf2e9"/>
  <text x="170" y="1207" text-anchor="middle" class="text">const</text>
  <rect x="270" y="1190" width="200" height="25" fill="#fdf2e9"/>
  <text x="370" y="1207" text-anchor="middle" class="text">✅ Yes</text>
  <rect x="470" y="1190" width="200" height="25" fill="#fdf2e9"/>
  <text x="570" y="1207" text-anchor="middle" class="text">❌ Uninitialized</text>
  <rect x="670" y="1190" width="200" height="25" fill="#fdf2e9"/>
  <text x="770" y="1207" text-anchor="middle" class="text">✅ Yes</text>
  <rect x="870" y="1190" width="200" height="25" fill="#fdf2e9"/>
  <text x="970" y="1207" text-anchor="middle" class="text">Block</text>
  <rect x="1070" y="1190" width="280" height="25" fill="#fdf2e9"/>
  <text x="1210" y="1207" text-anchor="middle" class="text">ReferenceError</text>
  
  <!-- function row -->
  <rect x="70" y="1215" width="200" height="25" fill="#eafaf1"/>
  <text x="170" y="1232" text-anchor="middle" class="text">function</text>
  <rect x="270" y="1215" width="200" height="25" fill="#eafaf1"/>
  <text x="370" y="1232" text-anchor="middle" class="text">✅ Yes</text>
  <rect x="470" y="1215" width="200" height="25" fill="#eafaf1"/>
  <text x="570" y="1232" text-anchor="middle" class="text">✅ Function object</text>
  <rect x="670" y="1215" width="200" height="25" fill="#eafaf1"/>
  <text x="770" y="1232" text-anchor="middle" class="text">❌ No</text>
  <rect x="870" y="1215" width="200" height="25" fill="#eafaf1"/>
  <text x="970" y="1232" text-anchor="middle" class="text">Function</text>
  <rect x="1070" y="1215" width="280" height="25" fill="#eafaf1"/>
  <text x="1210" y="1232" text-anchor="middle" class="text">Function works</text>
  
  <!-- Arrows -->
  <path d="M 350 400 L 370 400" class="arrow"/>
  <path d="M 150 880 L 400 880" class="danger-arrow"/>
  <path d="M 700 880 L 1000 880" class="arrow"/>
</svg>
