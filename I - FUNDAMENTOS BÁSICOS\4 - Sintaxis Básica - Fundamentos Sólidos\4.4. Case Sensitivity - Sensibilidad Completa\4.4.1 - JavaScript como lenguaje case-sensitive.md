## 4.4.1. JavaScript como lenguaje case-sensitive

### Introducción
JavaScript distingue mayúsculas y minúsculas.  
Variables como 'var' y 'Var' son diferentes.  
Afecta keywords, identifiers.  
Evita errores comunes.  
Importante en naming conventions.  
¿Manejas case sensitivity?

### Código de Ejemplo
```javascript
let test = 1;
let Test = 2;  // Diferentes variables
console.log(test);  // 1
console.log(Test);  // 2

// Keywords
if (true) {}  // OK
If (true) {}  // ReferenceError: If is not defined

// Functions
function func() {}
Func();  // TypeError: Func is not a function

// Properties
let obj = { key: 3 };
console.log(obj.Key);  // undefined
```
### Resultados en Consola
- 1 para test
- 2 para Test
- ReferenceError para If
- TypeError para Func
- undefined para obj.Key

### Diagrama de Flujo del Código
```mermaid
graph TB
    Inicio(("Inicio")) --> Declare["Paso 1: Declarar <br> - test y Test"]
    Declare --> Access["Paso 2: Acceder <br> - Diferente case"]
    Access -->|Mismo case| Success["Paso 3: Ã‰xito <br> - Valor correcto"]
    Access -->|Diferente| Error["Paso 4: Error <br> - Undefined/Reference"]
    Error --> CheckCase["Paso 5: Verificar case <br> - Debug"]
    CheckCase --> Fix["Paso 6: Corregir case <br> - Match exacto"]
    Fix --> CorrectAccess["Paso 7: Acceso correcto <br> - Funciona"]
    CorrectAccess --> Fin["Paso 8: Fin"]
    Inicio --> Desarrollador(("Desarrollador")) --> Conventions["Usar conventions <br> - camelCase"]
    Access --> NotaCase["Nota: Case-sensitive"]
    Error --> NotaCommon["Nota: Errores frecuentes"]
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Declare fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Access fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Success fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Error fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CheckCase fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fix fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style CorrectAccess fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fin fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Conventions fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaCase fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaCommon fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Visualización Conceptual
```mermaid
graph TB
    subgraph Proceso General
        Inicio(("Inicio")) --> Identifier["Identifier <br> - Case-sensitive"]
        Identifier --> Match["Match case <br> - Exacto?"]
        Match -->|SÃ­| AccessOK["Acceso OK <br> - Valor"]
        Match -->|No| Fail["Falla <br> - Error"]
        Fail --> Debug["Debug <br> - Verificar case"]
        Debug --> Correct["Corregir <br> - Ajustar case"]
        Correct --> AccessOK
        Inicio --> Desarrollador(("Desarrollador")) --> Linter["Usar linter <br> - Detectar"]
        Identifier --> NotaJS["Nota: JS case-sensitive"]
        Fail --> NotaError["Nota: Common mistakes"]
    end
    style Inicio fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Identifier fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Match fill:#FFA500,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style AccessOK fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Fail fill:#FF0000,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Debug fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Correct fill:#32CD32,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Desarrollador fill:#4682B4,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style Linter fill:#FF6347,stroke:#000,stroke-width:3px,color:#fff,font-weight:bold,font-size:14px
    style NotaJS fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
    style NotaError fill:#FFD700,stroke:#000,stroke-width:3px,color:#000,font-weight:bold,font-size:14px
```

### Casos de uso
1. Variables en scripts.
2. Llamadas a funciones.
3. Acceso a propiedades.
4. Keywords en condicionales.
5. En librerÃ­as.

### Errores comunes
1. Mezclar mayÃºsculas/minÃºsculas.
2. Copiar/pegar con case wrong.
3. Ignorar case en strings.
4. En APIs case-sensitive.
5. No usar conventions.

### Recomendaciones
1. Usa camelCase consistente.
2. Verifica case en debug.
3. Emplea linters.
4. Documenta conventions.
5. Test cross-platform.